<?php

namespace <PERSON><PERSON><PERSON>\Forms\ShopReviewControl;

use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Localization\ITranslator;
use Nette\Utils\ArrayHash;

class ShopReviewControl extends Control
{
	public array $onSuccess = [];

	private Shop $shop;
	private ITranslator $translator;

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}

	public function __construct(Shop $shop, ITranslator $translator)
	{
		$this->shop = $shop;
		$this->translator = $translator;
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		$form->addSelect('rating', $this->translator->translate('ofertocom.shop.review.rating'), [
			1 => '1',
			2 => '2',
			3 => '3',
			4 => '4',
			5 => '5',
		]);

		$form->addText('name', $this->translator->translate('ofertocom.shop.review.name'));

		$form->addSubmit('submit', $this->translator->translate('ofertocom.shop.review.submit'));

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values)
	{
		$this->onSuccess();
	}
}

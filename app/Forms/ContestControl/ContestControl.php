<?php

namespace <PERSON><PERSON><PERSON>\Forms\ContestControl;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Database\Explorer;
use Nette\Http\IRequest;
use Tracy\Debugger;

class ContestControl extends Control
{
	public array $onSuccess = [];

	private Explorer $explorer;
	private IRequest $request;
	private Leaflet $leaflet;

	public function __construct(Leaflet $leaflet, Explorer $explorer, IRequest $request)
	{
		$this->leaflet = $leaflet;
		$this->explorer = $explorer;
		$this->request = $request;
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		$form->addEmail('email', 'Email')
			->setRequired('Please enter your email.')
			->addRule(Form::EMAIL, 'Please enter a valid email address.');

		$form->addSubmit('submit', 'Odeslat');

		$form->onSuccess[] = function (Form $form, $values) {
			$countOfRows = $this->explorer->table('contest')
				->where('email', $values->email)
				->where('leaflet_id', $this->leaflet->getId())
				->count()
			;

			if ($countOfRows === 0) {
				try {
					$this->explorer->table('contest')
						->insert([
							'email' => $values->email,
							'created_at' => new \DateTime(),
							'leaflet_id' => $this->leaflet->getId(),
							'ip' => $this->request->getRemoteAddress(),
						]);
				} catch (\Exception $e) {
					Debugger::log($e->getMessage(), 'contest');
					$this->onSuccess();
					return;
				}
			}

			$this->onSuccess();
		};

		return $form;
	}
}

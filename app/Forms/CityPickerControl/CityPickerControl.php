<?php

namespace <PERSON><PERSON><PERSON>\Forms\CityPickerControl;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Offerista\Client;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Localization\ITranslator;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;

class CityPickerControl extends Control
{
	public array $onSuccess = [];
	private Localization $localization;
	private GeoFacade $geoFacade;
	private string $websiteType;
	private Response $response;
	private Request $request;
	private ITranslator $translator;

	public function __construct(Localization $localization, string $websiteType, GeoFacade $geoFacade, Response $response, Request $request, ITranslator $translator)
	{
		$this->localization = $localization;
		$this->geoFacade = $geoFacade;
		$this->websiteType = $websiteType;
		$this->response = $response;
		$this->request = $request;
		$this->translator = $translator;
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		$cities = [];

		$cities['currentLocation'] = $this->translator->translate('kaufino.cityPicker.myLocation');
		foreach ($this->geoFacade->findCitiesByLocalization($this->localization) as $city) {
			$cities[$city->getId()] = $city->getName();
		}

		$form->addSelect('cityId', 'City', $cities)
			->setRequired()
//            ->setDefaultValue($this->request->getCookie('cityId'))
		;

		$form->addSubmit('submit', 'Submit');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values)
	{
		$city = $this->geoFacade->findCity($values->cityId);

		$this->response->setCookie(
			'userLocation',
			Json::encode(
				['latitude' => $city->getLat(), 'longitude' => $city->getLng(), 'cityId' => $city->getId(), 'strategy' => Client::LOCATION_STRATEGY_USER_INPUT, 'confirmed' => true]
			),
			'10 years',
			httpOnly: false
		);

		$this->onSuccess($city);
	}
}

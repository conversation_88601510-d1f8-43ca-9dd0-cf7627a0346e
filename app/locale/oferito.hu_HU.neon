navbar:
	shops: "Üzletek"
	leaflets: "<PERSON>k<PERSON>ós <PERSON>"
	search:
		placeholder: "Üzletek keresése"
		submit: "Keresés"

	moreShops: "További üzletek"
	home: "Kezdőlap"

footer:
	copyright: "Oferito Minden jog fenntartva." 
	shops: "Üzletek"
	category: "Kategóriák"	
	aboutLetado: "A Oferito"
	cookies: "Cookies"
	leaflets: "Akciós újságok"
	aboutUs: "Rólunk"
	nextCountries: "További országok"

search:
	title: "Keresési eredmények\"%query%\""
	noResults: "Bárhogy is kerestünk, semmit sem találtunk."

homepage:
	title: "Legújabb akciós újságok és akciós termékek"
	text: "A legújabb akciós újságok, amelyek a legnagyobb áruházak akciós termékeinek széles skáláját kínálják."
	allLeaflets: "Minden akciós újság"
	shops: "Üzletek"
	allShops: "Minden üzlet"	

leaflets:
	title: "Akciós újságok"
	text: "A legújabb akciós újságok kínálata. Az akciós újságokat naponta frissítjük, hogy mindig megtalálja az akciós termékeket."

leaflet:
	metaTitle: 'A legújabb %brand% akciós újság, érvényes: %validSince%'
	metaTitlePageSuffix: 'oldalon %page%'
	metaDesc: 'A legújabb  %brand% akciós újság, érvényes: &nbsp;%validSinceDay% %validSince%.'
	leaflet: "%brand%" 
	desc: "Aktuális akciós újság a(z) %leafletBrandLink% áruháztól, érvényes %validSince% dátumtól %validTill% dátumig. %leafletPageCount% oldalon megtalálja az aktuális akciókat. A Oferito oldalán mindig megtalálja az aktuális információkat minden akciós újságról a legkedveltebb áruházaktól."
	longDesc1: "Használja ki a %leafletBrandLink% különleges ajánlatait, amelyek az aktuális promóciós prospektusban találhatók %validSince% és %validTill% között. Manapság minden egyre olcsóbb - autók, repülőjegyek, nyaralások, túrák, elektronikai cikkek, háztartási gépek, ruházati cikkek és még sok minden más. A szokásos havi kiadásokhoz azonban nem kell fogyasztási hitelt vagy más kölcsönt felvenni. A Letadónál mindent megteszünk, hogy a lehető leghamarabb elhozzuk Önnek a legnépszerűbb üzletek akcióit. Így kihasználhatja a legújabb akciókat vagy kedvezményeket, és pénzt takaríthat meg a háztartási költségvetésében."
	longDesc2: "Így nem kell pénzügyi tanácsadókat felbérelnie, hogy megkönnyítse pénzügyeit, nekünk köszönhetően ezt maga is megteheti. A megmaradt pénzt aztán felhasználhatja például külföldi nyaralásokra, belföldi szállodákba és panziókba tett utazásokra vagy a következő jelzáloghitel-fizetés pénzügyi tartalékaként."
	longDesc3: "Nagyszerű érzés anyagilag függetlennek lenni és többletforrásokkal rendelkezni. Mert így Ön is megengedheti magának, hogy jó minőségű biztosítást kössön, legyen szó életbiztosításról, lakásbiztosításról vagy kötelező felelősség- és balesetbiztosításról. Ez megvédi pénzügyeit minden olyan váratlan hatástól, amely jelentős negatív hatással lehet rájuk. A biztosítás tehát megvédi az Ön pénzügyeinek stabilitását."
	longDesc4: "Mi a Letadónál továbbra is mindent megteszünk azért, hogy segítsünk Önnek a lehető legtöbb pénzt megtakarítani a mindennapi vásárlásokon, hogy megengedhesse magának álmai autójának, kedvenc ruháinak, elektronikai cikkeinek vagy minőségi biztosításának megvásárlását. Reméljük, hogy ez a %leafletBrandLink% szórólap, amely %validSince%-től %validTill%-ig érvényes, legalább egy kicsit segít neked, és közelebb visz az álmaidhoz!"
	smallTitle: "%brand% dátumtól érvényes"
	recommendedLeaflets: "Kedvenc akciós újságok"
	similarLeaflets: "További %brand% akciós újságok"
	backToLeaflets: "Vissza az összes akciós újsághoz"   
	allBrandLeaflets: "Minden akciós újság %brand%"

shops:
	title: "Üzletek"
	text: "A legkedveltebb áruházak választéka, amelyektől naponta kínálunk új akciós újságokat."

shop:
	leaflets: "akciós újságok"
	text: "Aktuális %brand% akciós újság kedvezményes kínálatokkal."
	button: "Átirányítás a(z)  %brand% áruházba"   
	noLeaflets: "Keressük önnek az akciós újságokat... Kérjük, próbálja később."
	otherShops: "További üzletek"
	defaultTitleSuffic: '%shopName% - legújabb akciós újság, akciós termékek'
	otherLeaflets: "Egyéb újság %brand%"
	type:
		shopTitle: "{$shopName|upper|noescape} akciós újság{if $currentLeafletFromDate} szerdától {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:'Y.n.j'}{/if} + új katalógus"
		eshopTitle: "%brand% kedvezmény"
		eshop: "Tekintse meg a legfrissebb %brand% kedvezményeket az inspirációkkal és akciókkal teli katalógusukban. Az aktuális %brand% kedvezmények mindig elérhetőek, így az akciós szórólapnak köszönhetően soha nem marad le a kedvezményes ajánlatokról."
	metaTitles:
	     withFutureLeaflet: "{$shopName|upper} akciós újság lapozható {$nextLeafletFromDate|date:'Y.m.d.'} + aktuális szórólap"
	     withCurrentLeaflet: "{$shopName|upper} akciós újság lapozható + aktuális szórólap {$currentLeafletFromDate|date:'Y.m.d.'}"
	     withoutCurrentAndFutureLeaflet: "{$shopName|upper} akciós újság lapozható + aktuális szórólap online"
	metaDescriptions:
	    withFutureLeaflet: "{$shopName|upper} akciós újság lapozható ✅ Lapozza át a(z) {$shopName|upper} akciós újság új kiadványát {$nextLeafletFromDate|date:'Y.m.d.'} Online elérhető a(z) {$shopName|upper} aktuális akciós újság lapozható formában."
	    withCurrentLeaflet: "{$shopName|upper} akciós újság lapozható ✅ Lapozza át a(z) {$shopName|upper} akciós újság új kiadványát. Online elérhető a(z) {$shopName|upper} aktuális akciós újság lapozható formában {$currentLeafletFromDate|date:'Y.m.d.'}"
	    withoutCurrentAndFutureLeaflet: "{$shopName|upper} akciós újság lapozható ✅ Lapozza át a(z) {$shopName|upper} akciós újság új kiadványát. Online elérhető a(z) {$shopName|upper} aktuális szórólap, mely tartalmazza a legújabb akciókat."

tag:
	text: "A(z) %tag% kategória akciós újságainak kínálata."
	noLeaflets: "Keressük önnek az aktuális akciós újságot... Kérjük, próbálja később."
	otherShops: "További üzletek"	

about:
	title: "Rólunk"
	text: "Célunk, hogy felhasználóink időt és pénzt spóroljanak. Mindennap aktuális akciós újságokat hozunk a legnépszerűbb áruházaktól, így időt spórolunk a kedvezményes ajánlatok keresésében."
	address: "Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Praha - Nové Město <br><br> IČ: 02734699"

city:
	city:
		title: "Flyers %city%"
		text: "Current promotional leaflets %city%. In the sale leaflets in %city% you will find not only goods on sale but also many other discounts. Discounts can be found in %stores% and other stores."
		text2: "Promotional leaflets and current discounts in %city%. In the leaflets of popular shops in %city% you will find not only goods on sale but also many other discounts and offers at the best prices. Browse the flyers of the biggest stores in %city%, such as %stores%."
		h2: "Flyers, promotions and discounts from shops in %city%"
		leafletStores:
			title: "leaflet %brand%"
			store: "leaflet %brand% %city%"
			storeWithCity: "leaflet %brand% %city%"
		otherShops: "Stores in the city %city%"
		nearestCity: "Other cities with leaflets in the area"
		nearestCityWithShop: "Other cities with leaflets %shopName% in the area"
		categoriesInText: "%category% leaflets"
		citiesInText: "leaflets %city%"
		generatedText:
			1: "The city %city% with a population of %population% people offers countless shops, for which we bring you new flyers every week. Interesting promotions and discounts are prepared not only for the city %city%, but also for other nearby cities such as %cities%."
			2: "A list of the current flyers of the biggest shops in %city% can be found here:"
			3: "There are also promotional flyers for stores %stores% and many more. You can find their promotions for month %month% in the %shopsLink% section."
			leaflet: "Flyer %brand% current %validSince% - %validTill%"
			and: "and"
			or: "or"
	store:
		store: %fullAddress%
		h1: %brand% %address%
		h2: "Stores %brand% in %city%"
		title: "%brand% %address%, leaflet and opening hours 🕔"
		description: "Compare the offers in the leaflets, find out the exact address and opening hours, or read about the range of products waiting for you at the store %brand% %address%."
		open: "Open"
		closed: "Closed"
		text: "Special offers and discounts on a wide range of products are regularly offered by the %brand% %address% store, where you can use the popular %brand% leaflet when making a purchase."
		text2WithoutStores: "Customers can conveniently view this online, as well as promotions available at other branches."
		text2: "Customers can conveniently view this online, as well as the promotions available at %stores% branches."
		or: nebo
		others: "and others"
		textBottom: "The %brand% %address% shop offers customers not only a wide range of goods but also low prices, which are regularly advertised in the %brand% leaflet. The %fullAddress% branch is a popular place for those looking for great deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have the latest discounts at hand. If %brand% %address% doesn't offer everything a shopper needs, they can also take advantage of other nearby stores such as:"
		textBottom2: "Find out the exact address, contact details, or opening hours of your favorite stores all in one place. There is also information on which branches are located in your area and where you can take advantage of other good deals, which are also announced in the promotional leaflets of selected stores."
		sections:
			leaflets: Egyéb szórólapok a kategóriából
			shops: Egyéb üzletek a környéken
			stores: Egyéb %brand% üzletek a környéken
	shop:
		title: "Leaflets %brand% %city%"
		h2: "Flyers, promotions, and discounts in the store %brand% %city%"
		text: "Promotional leaflets %brand% %city% and their current discounts and promotions. In the %brand% %city% leaflet you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%."
		leafletStores:
			title: "%brand% leaflet %city%"
			store: "%brand% %city%"
		cityLink: "leaflets %city%"
		shopLink: "leaflets %shop%"
		otherShops: "Other shops in the city %city%"
		shopLeaflet: "Leaflets %brand%"
		citiesInText: "%brand% leaflet %city%"
		offers: "Offers from flyers %brand% %city%"
		generatedText:
			1: "The %brand% store in the city %city% offers promotional leaflets that we regularly update for you every week."
			2: "If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href=\"%actualLeafletUrl%\">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains exciting special offers and discounts, seasonal promotions or club prices, and a wide range of merchandise."
			3: "However, store %brand% is not only located in the city %city%. You can also find leaflets for the store %brand% in other nearby stores %stores%. All the promotional flyers are available in the section <a href=\"%leafletsUrl%\">Letáky.</a>"
			4: "If you're looking for other stores, popular ones include %stores%."

navbar:
	shops: Stores
	leaflets: Catalogues
	offers: Deals
	search:
		placeholder: Search stores
		submit: Search

	moreShops: Other Stores
	articles: Magazine
	cities: Cities
	home: Home

error404:
    title: 'Page not found'
    text: 'The page you are looking for does not exist. Please try again.'
    link: 'Back to home page'
    linkToShop: 'Back to %brand%'

footer:
	copyright: Kaufino All rights reserved.
	shops: Stores
	category: 'Categories:'
	offersCategory: Specials
	aboutKaufino: Kaufino
	cookies: Cookies
	leaflets: Catalogues
	aboutUs: About Us
	nextCountries: Other Countries

search:
	title: Search results for "%query%"
	noResults: 'No matter how hard we tried, we didn''t find anything.'

cityPicker:
	submit: Select
	placeholder: Select an option

homepage:
	title: Latest Catalogues
	metaTitle: Latest Catalogues Full of Great Deals! | Kaufino
	metaDescription: Check out the latest catalogues full of amazing specials online. Browse your favorite stores' catalogues today and save BIG on your next purchase!
	text: Check out the latest catalogues full of amazing specials online. Browse your favorite stores' catalogues today and save BIG on your next purchase!
	and: and
	leaflets: Latest Catalogues and Mailers
	allLeaflets: All Catalogues
	shops: Stores
	allShops: All Stores
	offersCategory: Deals
	city: Catalogues in your city
	articles:
		title: Blog articles

	bottomText:
		mainTitle: About Kaufino
		section1:
			title: Hassle-Free Shopping Experience
			text: '<PERSON>ufino is the perfect choice for you if you love to get the most out of the latest specials from your favorite <a href="./shops">stores</a>. Browse through a large collection of their promotional catalogues, discover the current deals, and save money on your next purchase. You can pick from a wide range of stores that offer everything from food and drinks to homeware, accessories, leisure equipment, and so much more. With Kaufino, you can enjoy a smooth and hassle-free shopping experience.'

		section2:
			title: Latest Deals in One Place
			text: 'We update our catalogue collection daily to ensure we bring you the most recent specials. Whether you’re looking for groceries, household essentials, beauty products or clothing, you can always find what you need in the catalogues we provide. Browse the latest catalogues from many retailers, including <a href="./new-world">New World</a>, <a href="./woolworths">Woolworths</a>, <a href="./pak-n-save">Pak n Save</a>, and more.'

		section3:
			title: Convenience at Your Fingertips
			text: 'Kaufino makes shopping easier than ever. We’ve streamlined everything for you, allowing you to plan your shopping comfortably from home - no more sifting through mailboxes for catalogues. Simply select your preferred store, click on their latest catalogue, and explore it from start to finish. Discover your favorite products at special prices. Plus, you can search for similar items in other catalogues and compare prices or find additional specials.'

		section4:
			title: Save Time and Money
			text: 'Managing family expenses each week can feel overwhelming and put pressure on your budget. With Kaufino, you can easily plan your shopping, maximize savings by tapping into the latest specials, and make your money go further. By regularly checking our pages, you''ll stay ahead of new offers, allowing you to buy more while spending less. Simplify your shopping and start saving today!'
			text2: ''

leaflets:
	title: Latest Catalogues Packed with Amazing Specials
	metaTitle: Latest Catalogues and Specials
	text: Browse online catalogues from the most popular stores in New Zealand. Check out the current specials and start saving today!
	metaDescription: Browse online catalogues from the most popular stores in New Zealand. Check out the current specials and start saving today!
	city: Catalogues in your city
	expiredTitle: Expired catalogues
	expiredText: Browse the selection of expired catalogues from the most popular retailers!
	expiredMetaTitle: Expired %brand% catalogues

leaflet:
	ads: ADVERTISING
	metaTitle: Current %brand% catalogue valid from %validSince% to %validTill%
	metaTitleUnChecked: Current %brand% catalogue valid from %validSince% to %validTill%
	metaDesc: 'Check out the current %brand% catalogue valid from %validSinceDay%, %validSince%. Enjoy amazing savings today!'
	metaDescUnChecked: Check out the current %brand% catalogue
	title: %brand% Catalogue - Valid from %validSince% to %validTill%
	titleUnChecked: Current %brand% Catalogue
	leaflet: %brand% Catalogue
	desc: 'You are currently viewing the %leafletBrandLink% catalogue, valid from %validSince% to %validTill%. This catalogue features %leafletPageCount% pages of current discounts. Visit Kaufino regularly to stay updated on all the latest deals in New Zealand!'
	descUnChecked: 'You are currently viewing the %leafletBrandLink% catalogue, valid from %validSince% to %validTill%. This catalogue features %leafletPageCount% pages of current discounts. Visit Kaufino regularly to stay updated on all the latest deals in New Zealand!'
	smallTitle: %brand% Catalogue Valid from
	recommendedLeaflets: Favorite Catalogues
	similarLeaflets: More %brand% Catalogues
	backToLeaflets: Back to all catalogues
	allBrandLeaflets: All %brand% catalogues
	valid: Current catalogue
	brandLeafletFrom: %brand% catalogue valifd from
	futureLeafletTitle: ' Latest %brand% catalogue valid from %validSince% - %validTill% ⭐'
	futureLeafletDescription: Browse the latest %brand% catalogue packed with great deals today ⭐ Plan your next purchase and enjoy amazing savings! ✅
	archivedLeafletTitle: Past %brand% catalogue valid from %validSince% - %validTill%
	archivedLeafletDescription: 'You are currently viewing a previous %leafletBrandLink% catalogue, valid from %validSince% to %validTill%. This catalogue contains %leafletPageCount% pages of past discounts. Visit Kaufino regularly to stay updated on all the latest deals in New Zealand.'
	expiredLeafletTitle: %brand% catalogue valid since %validSince% that expired on %validTill%
	expiredLeafletDescription: This %brand% catalogue expired on %validTill%.
	actualLeafletValidSince: 'The current catalogue is available and valid since %validSinceDay%, %validSince%.'
	expiredLeafletHeading: This catalogue is no longer valid
	expiredLeafletLinkToShop: You can find the latest %brand% catalogue here
	leaflets: %brand% catalogues
	leafletValidTill: "Valid until: %validTill%"
	imageAltTitleWithDate: Catalogues %brand% - %leafletName% valid since %validSince% until %validTill% - page %page%
	imageAltTitle: Catalogues %brand% - %leafletName% - page %page%

newsletter:
	metaTitle: Current %brand% newsletter valid from %validSince%
	metaDesc: 'Check out the current %brand% newsletter valid from %validSinceDay%, %validSince% and enjoy amazing deals!'
	leaflet: %brand%
	desc: 'Current %leafletBrandLink% newsletter valid from %validSince%. In the newsletter, you will find the latest promotions and amazing deals.'
	smallTitle: %brand% newsletter valid from
	recommendedLeaflets: Favorite newsletters
	similarLeaflets: More %brand% newsletters
	backToLeaflets: Back to all newsletters
	allBrandLeaflets: All %brand% newsletters

shops:
	title: All Your Favourite Stores
	metaTitle: Latest Catalogues and Mailers in One Place
	text: Just click on the logo and check out the latest specials!
	metaDescription: Browse the latest catalogues and save BIG on your next purchase! Get your hands on amazing specials while they last.
	otherShops:
		title: Other stores

shop:
	title: %brand% - Catalogues and Specials
	showLeaflet: Browse Catalogue
	text: 'Explore the %brand% catalogue featuring amazing offers and deals ⭐ Additionally, don’t miss the %brand% catalogue for next week, filled with new discounts!'
	textSubdomain: 'Explore the %brand% catalogue featuring amazing offers and deals ⭐ Additionally, don’t miss the %brand% catalogue for next week, filled with new discounts!'
	button: 'Go to %brand% '
	type:
		shopTitle: '{$shopName|upper} catalogue{if $currentLeafletFromDate} valid from {$currentLeafletFromDate|dayGenitive}, {$currentLeafletFromDate|date:''j.n.Y''}{/if}'
		shopTitleSubdomain: '{$shopName|upper} catalogue{if $currentLeafletFromDate} valid from {$currentLeafletFromDate|dayGenitive}, {$currentLeafletFromDate|date:''j.n.Y''}{/if}'
		eshopTitle: %brand% discount
		eshop: 'Explore the %brand% catalogue to find the latest promotions. With easy access to current discounts, you''ll never miss out on great savings again!'

	noLeaflets: We are searching for the current catalogue for you... Please try again later.
	city: %brand% catalogues in your city too.
	otherShops: More stores
	storeLeaflet: %brand% catalogues
	alternativeName: What people also search for
	internationalVariants: 'You can also find %brand% catalogues in these countries:'
	defaultTitleSuffic: '%shopName% - current catalogues, promotions'
	offers: Offers from %brand% catalogues
	offersAll: Offers from catalogues
	link: '%brand% catalogue, page %page% »'
	expired: The offer has expired.
	offersLeaflets: Catalogues from the %category% category
	offersExpire: Past offers from the %category% category
	otherLeaflets: Other %brand% catalogues
	metaTitles:
		withFutureLeaflet: '{$shopName|upper} catalogue valid from {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''j.n.Y''}'
		withCurrentLeaflet: '{$shopName|upper} current leaflet from {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''} + leaflet next week'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} brochure next week + promotional flyer online'

	metaDescriptions:
		withFutureLeaflet: '{$shopName|upper} leaflet ✅ Browse the promotional {$shopName|upper} leaflet valid from {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''j.n.Y''}. The {$shopName|upper} leaflet for next week is also available online.'
		withCurrentLeaflet: '{$shopName|upper} current leaflet ✅ Browse through the promotional {$shopName|upper} leaflet available from {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''}. The {$shopName|upper} leaflet for next week is also available online.'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} leaflet next week ✅ Browse the promotional {$shopName|upper} leaflet for next week. The current PDF leaflet of {$shopName|upper} with this week''s promotions is also available online.'

tag:
	title: %tag% leaflets
	metaTitle: %tag% promotional flyers
	titleWithTag: Flyers in the category %tag%
	citiesWithTag: Cities with flyers in the category %tag%
	text: 'Browse the current promotional flyers for this week from the category %tag%. In the flyers, you will find not only items on sale but also many discounts on various goods.'
	noLeaflets: We are looking for the current flyer for you... Please try again later.
	otherShops: More shops
	offers:
		title: %tag% on sale in promotional flyers
		metaDescription: Check out the promotions on %tag% in the flyers of the most well-known stores. Don't miss the discounts and special prices that can be found in the new flyers.
		titleWithBestOffer: %tag% on sale from ⏩ %price% in the current flyer.
		metaDescriptionWithBestOffer: Check out the promotions on %tag% products in the flyers of the most well-known stores. Don't miss out on discounts on products at special prices in the new flyers.
		text: 'Explore all the promotions on %tag% in the <a href="%leafletLink%">flyers</a> and don''t miss out on additional discounts on selected products. The promotional prices for %tag% from the current flyers are regularly updated, and finding the <strong>cheapest</strong> price is very easy.'
		currentLeaflet: Current %brand% leaflet
about:
	title: About us
	text: 'Our goal is to save users time and money. Every day, we bring the latest flyers from the most popular retailers and save time in searching for promotional offers on products.'
	address: 'Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Prague - New Town <br><br> Company ID: 02734699 <br><br> info[at]vimvic.cz'

coupon:
	type:
		sale: Discount coupon

	showCode: Show the discount code.
	valid: Validity until
	copyText: Copy the code and paste it in the cart in the store <strong>%brand%</strong>.
	copy: Copy.
	copied: Copied
	button: Save on %brand%

offer:
	title: Goods on sale at the best prices.
	text: 'Check out the selection of new products on sale in the current flyers from the most popular retailers such as Tesco, Albert, and many others.'
	metaDescription: 'Check out the selection of new goods on sale in the current flyers of the most popular retailers such as Tesco, Albert, and many others.'
	whatOnSale: WhatCurrent leaflets
	offerItem:
	    validTillDays: 1 day left|Valid for %count% days|Valid for %count% days
	    validTillToday: Only today
	    validTillWeek: More than a week left
	    validSinceDays: Valid from tomorrow|Valid in %count% days|Valid in %count% days
	    validSinceWeek: Valid for more than a week
	futureOffers: 'Discounts in future leaflets for the product %name%'
	priceHistory: 'Price development of product %name% over the last 12 months'

city:
	city:
		metaTitle: Current flyers in the city %city%
		title: Current flyers in the city %city%
		text: 'Current promotional flyers in %city%. In the flyers of %stores% in the city of %city%, you will find many discounts and items on promotion available online.'
		metaDescription: 'Current promotional flyers for %city%. In the flyers from %stores% in %city%, you will find many discounts and items on sale available online.'
		text2: 'Promotional flyers and current discounts in the city of %city%. In the flyers of popular stores in %city%, you will find not only products on sale but also many other discounts and offers at the best prices. Browse through the flyers of the largest stores in %city%, such as %stores%.'
		h2: 'Leaflets, promotions, and discounts from stores in the city of %city%'
		storesTitle: Branches in the city of %city%
		storesMoreButton: Other branches »
		actualLeaflet: Current flyers
		leafletStores:
			title: leaflet %brand%
			store: flyer %brand% %city%
			storeWithCity: flyer %brand% %city%

		otherShops: Shops in the city of %city%
		nearestCity: Other cities with flyers in the area.
		nearestCityWithShop: Other cities with flyers for %shopName% in the vicinity
		categoriesInText: %category% flyers
		citiesInText: flyers %city%
		generatedText:
			1: 'The city of %city% with a population of %population% residents offers countless shops, for which we bring you new flyers every week. Exciting promotions and discounts are prepared not only for the city of %city% but also for other nearby cities such as %cities%.'
			2: 'The list of current flyers from the largest stores in %city% can be found here:'
			3: There are also promotional flyers from %stores% and many others available. You can find their special offers for the month of %month% in the %shopsLink% section.
			leaflet: Flyer %brand% current %validSince% – %validTill%
			and: and
			or: or

	store:
		store: %fullAddress%
		h1: %brand% %city% %address%
		h2: %brand% stores in %city%
		title: '%brand% %address%, catalogue and opening hours 🕔'
		description: 'Compare the offers in the flyers, find out the exact address and opening hours, or read about the range of products waiting for you at the %brand% %address% store.'
		open: Open
		closed: Closed
		text: 'The store %brand% %address% regularly offers advantageous promotions and discounts on a diverse range of products, where you can take advantage of the popular %brand% leaflet during your purchase.'
		h2bottom: %brand% catalogue %city% %street%
		text2WithoutStores: 'Customers can conveniently browse this online, as well as promotions available at other branches.'
		text2: 'Customers can conveniently view this online, as well as the promotions available at the branches of %stores%.'
		or: or
		others: and others
		textBottom: 'The %brand% store at %address% offers customers not only a wide range of products but also low prices, which %brand% regularly informs through its leaflet. The %brand% branch at %city% %street% is a popular place for those looking for budget-friendly deals. Thanks to the fact that the %brand% leaflet is available online, shoppers always have current discounts at their fingertips. If %brand% %address% does not provide everything the customer needs, they can also take advantage'
		textBottom2: 'Find out the exact address, customer service contact, or opening hours of your favorite stores clearly in one place. You will also find information about which branches are located in your vicinity and where to take advantage of other beneficial offers reported in the promotional flyers of selected stores.'
		sections:
			leaflets: Other catalogues from the category
			shops: Other shops in the area
			stores: Other %brand% stores in the area

	shop:
		title: Flyers %brand% %city%
		metaTitle: Flyers %brand% %city%
		storesTitle: Branches of %brand% in the city of %city%
		h2: 'Flyers, promotions, and discounts at the store %brand% %city%'
		text: 'Action flyers for %brand% in %city% and their current discounts and promotions. In the flyer for %brand% in %city%, you will find a wide range of products at the best prices. However, %city% is not just home to %brand%. Other popular stores include, for example, %stores%.'
		metaDescription: 'Promotional flyers for %brand% in %city% and their current discounts and promotions. In the %brand% flyer for %city%, you will find a wide range of products at the best prices. However, %city% is not home to just %brand%. Other popular stores include %stores%, for example.'
		leafletStores:
			title: %brand% flyer %city%
			store: %brand% %city%

		cityLink: flyers %city%
		shopLink: flyers %shop%
		otherShops: More shops in the city %city%
		shopLeaflet: Flyers %brand%
		citiesInText: %brand% flyer %city%
		offers: Offers from flyers %brand% %city%
		generatedText:
			1: The %brand% store in %city% offers promotional flyers that we update for you every week.
			2: 'If you are looking for a new %brand% leaflet, you can check it out by clicking on this link: <a href="%actualLeafletUrl%">Current %brand% %city% leaflet</a>. The validity of the leaflet is from %validSince% to %validTill%. Each leaflet contains interesting promotional offers and discounts, seasonal sales or club prices, and a wide range of products.'
			3: 'The store %brand% is not only located in the city of %city%. You can also find %brand% store flyers in other nearby locations %stores%. All promotional flyers are available in the <a href="%leafletsUrl%">Flyers</a> section.'
			4: 'If you are looking for more stores, popular options include %stores%.'

	tag:
		title: Flyers in the category %tag% in the city %city%
		titleWithTag: Leaflets in the category %tag% in the city %city%
		text: Current flyers from the category %tag% in the city %city%
		metaTitle: Current flyers from the %tag% category in the city of %city%. Check out the discount offers and shop advantageously today.

articles:
	title: Articles about promotional flyers in hypermarkets and supermarkets.
	h1: Magazine
	description: Articles full of tips for easier shopping with new flyers in domestic hypermarkets and supermarkets. Take advantage of limited-time promotions and shop at discounted prices!
	otherArticles: Other articles
	author:
		title: Author %author%
		description: Articles by the author %author%
		articlesCount: %count% article|%count% articles|%count% articles

showMore:
	offers: Other Products
	cities: Other Cities
	allCities: All Cities
	shops: Other Stores
	leaflets: More Catalogues
	tags: More Deals

tabs:
	leaflets: Catalogues
	shop: Customer benefits
	product: Products
	contact: Contact


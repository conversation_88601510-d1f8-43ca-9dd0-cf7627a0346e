navbar:
	shops: Stores
	leaflets: Catalogues
	offers: Specials
	search:
		placeholder: Search stores
		submit: Search

	moreShops: Other Stores
	articles: Magazine
	cities: Cities
	home: Home

error404:
    title: 'Page not found'
    text: 'The page you are looking for does not exist. Please try again.'
    link: 'Back to home page'
    linkToShop: 'Back to %brand%'

footer:
	copyright: Kaufino All rights reserved.
	shops: Stores
	category: Category
	offersCategory: Specials
	aboutKaufino: Kaufino
	cookies: Cookies
	leaflets: Catalogues
	aboutUs: About Us
	nextCountries: Other countries

search:
	title: Search results for "%query%"
	noResults: 'No matter how hard we tried, we haven''t found anything.'

cityPicker:
	submit: Select
	placeholder: Select an option

homepage:
	title: Latest Catalogues
	metaTitle: Latest online catalogues | Kaufino
	metaDescription: 'Discover the latest catalogues from top supermarkets and hypermarkets online. Find the best deals in the Coles, ALDI, and other catalogues, all in one place.'
	text: 'Discover the latest catalogues from top supermarkets and hypermarkets online. Find the best deals in the Coles, ALDI, and other catalogues, all in one place.'
	and: and
	leaflets: Latest Catalogues
	allLeaflets: All Catalogues
	shops: Stores
	allShops: All Stores
	offersCategory: Deals
	city: Catalogues in Your City
	articles:
		title: Blog articles

	bottomText:
		mainTitle: About Kaufino
		section1:
			title: Hassle-Free Shopping Experience
			text: '<PERSON>ufino is the perfect choice for you if you love to get the most out of the latest deals from your favorite <a href="./shops">stores</a>. Browse through a large collection of their promotional catalogues, discover the current deals, and save money on your next purchase. You can pick from a wide range of stores that offer everything from food and drinks to homeware, accessories, leisure equipment, and so much more. With Kaufino, you can enjoy a smooth and hassle-free shopping experience.'

		section2:
			title: Latest Deals in One Place
			text: 'We update our catalogue collection daily to ensure we bring you the most recent deals. Whether you’re looking for groceries, household essentials or clothing, you can always find what you need in the catalogues we provide. Browse the latest catalogues from many retailers, including <a href="./coles">Coles</a>, <a href="./woolworths">Woolworths</a>, <a href="./aldi">ALDI</a>, and more.'

		section3:
			title: Convenience at Your Fingertips
			text: 'Kaufino makes shopping easier than ever. We’ve streamlined everything for you, allowing you to plan your shopping comfortably from home - no more sifting through mailboxes for catalogues. Simply select your preferred store, click on their latest catalogue, and explore it from start to finish. Discover your favorite products at special prices. Plus, you can search for similar items in other catalogues and compare prices or find additional deals.'

		section4:
			title: Save Time and Money
			text: 'Managing family expenses each week can feel overwhelming and put pressure on your budget. With Kaufino, you can easily plan your shopping, maximize savings by tapping into the latest deals, and make your money go further. By regularly checking our pages, you''ll stay ahead of new offers, allowing you to buy more while spending less. Simplify your shopping and start saving today!'
			text2: ''

leaflets:
	title: Latest Catalogues in One Place
	metaTitle: Latest Catalogues Packed with Amazing Deals
	text: Browse online catalogues from the most popular stores in Australia. Check out the current specials and start saving today!
	metaDescription: Browse online catalogues from the most popular stores in Australia ⭐ Check out the current specials and start saving today!
	city: Catalogues in your city
	expiredTitle: Expired catalogues
	expiredText: Browse a selection of past catalogues from the most popular retailers.
	expiredMetaTitle: Past %brand% catalogues

leaflet:
	ads: ADVERTISING
	metaTitle: %brand% Catalogue Valid from %validSince% to %validTill%
	metaTitleUnChecked: %brand% Catalogue Valid from %validSince% to %validTill%
	metaDesc: 'Check out the current %brand% catalogue valid from %validSinceDay%, %validSince%. Browse the amazing deals and save today!'
	metaDescUnChecked: Take a look at the current %brand% catalogue
	title: %brand% Catalogue Valid from %validSince% - %validTill%
	titleUnChecked: Current %brand% catalogue
	leaflet: %brand%
	desc: 'You are currently viewing the latest %leafletBrandLink% catalogue, valid from %validSince% to %validTill%. This catalogue features %leafletPageCount% pages of current discounts. Visit Kaufino regularly to stay updated on all the latest deals in Australia!'
	descUnChecked: 'You are currently viewing the latest %leafletBrandLink% catalogue, valid from %validSince% to %validTill%. This catalogue features %leafletPageCount% pages of current discounts. Visit Kaufino regularly to stay updated on all the latest deals in Australia!'
	smallTitle: %brand% Catalogue Valid from
	recommendedLeaflets: Favorite catalogues
	similarLeaflets: More %brand% catalogues
	backToLeaflets: Back to all catalogues
	allBrandLeaflets: All %brand% catalogues
	valid: Current catalogue
	brandLeafletFrom: %brand% Catalogue Valid from
	futureLeafletTitle: Latest %brand% Catalogue Valid from %validSince% - %validTill%
	futureLeafletDescription: Browse the latest %brand% catalogue packed with great deals today ⭐ Plan your next purchase and enjoy amazing savings! ✅
	archivedLeafletTitle: Past %brand% Catalogue Valid from %validSince% - %validTill%
	archivedLeafletDescription: 'You are currently viewing a previous %leafletBrandLink% catalogue, valid from %validSince% to %validTill%. This catalogue contains %leafletPageCount% pages of past discounts. Visit Kaufino regularly to stay updated on all the latest deals in Australia!'
	expiredLeafletTitle: %brand% catalogue that expired on %validTill%
	expiredLeafletDescription: This %brand% catalogue valid since %validSince% expired on %validTill%
	actualLeafletValidSince: 'The current catalogue is available and valid since %validSinceDay%, %validSince%. ✅'
	expiredLeafletHeading: This catalogue is no longer valid
	expiredLeafletLinkToShop: You can find the latest %brand% catalogue here
	leaflets: %brand% catalogues
	leafletValidTill: "Valid until: %validTill%"
	imageAltTitleWithDate: Catalogue %brand% - %leafletName% valid since %validSince% until %validTill% - page %page%
	imageAltTitle: Catalogue %brand% - %leafletName% - page %page%

newsletter:
	metaTitle: Current %brand% newsletter valid from %validSince%
	metaDesc: 'Check out the current %brand% newsletter valid from %validSinceDay%, %validSince% and enjoy amazing deals!'
	leaflet: %brand% newsletter
	desc: 'Current %leafletBrandLink% newsletter valid from %validSince%. In the newsletter, you will find the latest promotions and amazing deals.'
	smallTitle: %brand% newsletter valid from
	recommendedLeaflets: Favorite newsletters
	similarLeaflets: More %brand% newsletters
	backToLeaflets: Back to the list of all newsletters
	allBrandLeaflets: All %brand% newsletters

shops:
	title: All Your Favorite Stores
	metaTitle: Latest catalogues and amazing specials online
	text: Just click on the logo and check out the latest specials!
	metaDescription: Browse the latest catalogues and save BIG on your next purchase! Get your hands on amazing specials while they last.
	otherShops:
		title: Other stores

shop:
	title: %brand% - Latest Catalogues and Specials
	showLeaflet: Browse Catalogue
	text: 'Explore the %brand% catalogue featuring amazing specials and deals ⭐ Additionally, don’t miss the %brand% catalogue for next week, filled with new specials!'
	textSubdomain: 'Explore the %brand% catalogue featuring amazing specials and deals ⭐ Additionally, don’t miss the %brand% catalogue for next week, filled with new specials!'
	button: 'Go to %brand% '
	type:
		shopTitle: '{$shopName|upper} catalogue{if $currentLeafletFromDate} valid from {$currentLeafletFromDate|dayGenitive}, {$currentLeafletFromDate|date:''j.n.Y''}{/if}'
		shopTitleSubdomain: '{$shopName|upper} catalogue{if $currentLeafletFromDate} valid from {$currentLeafletFromDate|dayGenitive}, {$currentLeafletFromDate|date:''j.n.Y''}{/if}'
		eshopTitle: %brand% discount
		eshop: 'Explore the %brand% catalogue to find the latest promotions. With easy access to current discounts, you''ll never miss out on great savings again!'

	noLeaflets: We are searching for the current catalogue for you... Please try again later.
	city: %brand% catalogues in your city too.
	otherShops: More stores
	storeLeaflet: %brand% catalogues
	alternativeName: What people also search for
	internationalVariants: 'You can also find %brand% catalogues in these countries:'
	defaultTitleSuffic: '%shopName% - current catalogues, promotions'
	offers: Offers from %brand% catalogues
	offersAll: Offers from catalogues
	link: '%brand% catalogue, page %page% »'
	expired: The offer has expired.
	offersLeaflets: Catalogues from the %category% category
	offersExpire: Past offers from the %category% category
	otherLeaflets: Other %brand% catalogues
	metaTitles:
		withFutureLeaflet: '{$shopName|upper} Catalogue Valid from {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''j.n.Y''}'
		withCurrentLeaflet: '{$shopName|upper} current leaflet from {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''} + leaflet next week'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} flyer next week + online promotional flyer'

	metaDescriptions:
		withFutureLeaflet: '{$shopName|upper} leaflet ✅ Browse the promotional {$shopName|upper} leaflet valid from {$nextLeafletFromDate|dayGenitive} {$nextLeafletFromDate|date:''j.n.Y''}. The {$shopName|upper} leaflet for next week is also available online.'
		withCurrentLeaflet: '{$shopName|upper} current leaflet ✅ Browse through the promotional {$shopName|upper} leaflet available from {$currentLeafletFromDate|dayGenitive} {$currentLeafletFromDate|date:''j.n.Y''}. The {$shopName|upper} leaflet for next week is also available online.'
		withoutCurrentAndFutureLeaflet: '{$shopName|upper} flyer next week ✅ Browse the promotional {$shopName|upper} flyer for next week. The current PDF flyer {$shopName|upper} with this week''s promotions is also available online.'

tag:
	title: %tag%
	metaTitle: %tag% - online catalogues
	titleWithTag: Catalogues in the %tag% category
	citiesWithTag: Cities with flyers in the %tag% category
	text: Browse the catalogues from the %tag% category to find the best deals!
	noLeaflets: We are looking for the current catalogue for you... Please try again later.
	otherShops: Other stores
	offers:
		title: %tag% deals in current catalogues
		metaDescription: Check out the deals on %tag% in the flyers of the most well-known stores. Don't miss out on discounts and promotional prices that can be found in the new flyers.
		titleWithBestOffer: %tag% on sale from ⏩ %price% in the current flyer.
		metaDescriptionWithBestOffer: Check out the promotions for product %tag% in the flyers of the most well-known stores. Don't miss out on discounts on products at promotional prices in the new flyers.
		text: 'Explore all the promotions for %tag% in the <a href="%leafletLink%">leaflets</a> and don''t miss out on additional discounts on selected products. The promotional prices for %tag% from the current leaflets are regularly updated, and finding the <strong>cheapest</strong> price is very easy.'
		currentLeaflet: Current %brand% leaflet
about:
	title: About us
	text: 'Our goal is to save users time and money. Every day, we bring the latest flyers from the most popular retailers and save time in searching for promotional offers on products.'
	address: 'Business Animals s.r.o. <br> Na Poříčí 1067/25 <br> 110 00 Prague - New Town <br><br> Company ID: 02734699 <br><br> info[at]vimvic.cz'

coupon:
	type:
		sale: Discount coupon

	showCode: Show the discount code.
	valid: Validity until
	copyText: Copy the code and paste it in the cart at the store <strong>%brand%</strong>.
	copy: Copy
	copied: Copied
	button: Save on %brand%

offer:
	title: Latest Specials on Selected Products
	text: Find the latest specials and promotions on the most sought-after products.
	metaDescription: Find the latest specials and promotions on the most sought-after products!
	whatOnSale: What's on sale?
	offerItem:
	    validTillDays: 1 day left|Valid for %count% days|Valid for %count% days
	    validTillToday: Only today
	    validTillWeek: More than a week left
	    validSinceDays: Valid from tomorrow|Valid in %count% days|Valid in %count% days
	    validSinceWeek: Valid for more than a week
	futureOffers: 'Discounts in future leaflets for the product %name%'
	priceHistory: 'Price development of product %name% over the last 12 months'

city:
	city:
		metaTitle: Current flyers in the city of %city%
		title: Current flyers in the city of %city%
		text: 'Current promotional flyers for %city%. In the flyers from %stores% in the city of %city%, you will find many discounts and promotional items available online.'
		metaDescription: 'Current promotional flyers in %city%. In the flyers of %stores% in %city%, you will find many discounts and items on sale available online.'
		text2: 'Promotional flyers and current discounts in the city of %city%. In the flyers of popular stores in the city of %city%, you will find not only products on sale but also many other discounts and offers at the best prices. Browse through the flyers of the largest stores in the city of %city%, such as %stores%.'
		h2: 'Flyers, promotions, and store discounts in the city of %city%'
		storesTitle: Branches in the city of %city%
		storesMoreButton: More branches »
		actualLeaflet: Current flyers
		leafletStores:
			title: leaflet %brand%
			store: flyer %brand% %city%
			storeWithCity: flyer %brand% %city%

		otherShops: Shops in the city %city%
		nearestCity: Other cities with flyers in the vicinity
		nearestCityWithShop: Other cities with flyers for %shopName% in the area
		categoriesInText: %category% leaflets
		citiesInText: flyers %city%
		generatedText:
			1: 'The city of %city% with a population of %population% residents offers countless shops, for which we bring you new flyers every week. Exciting deals and discounts are prepared not only for the city of %city%, but also for nearby ones, such as the cities of %cities%.'
			2: 'You can find the list of current flyers from the largest stores in the city of %city% here:'
			3: Also available are promotional flyers from %stores% and many others. Their promotional offers for the month of %month% can be found in the %shopsLink% section.
			leaflet: Leaflet %brand% current %validSince% – %validTill%
			and: and
			or: or

	store:
		store: %fullAddress%
		h1: %brand% %city% %address%
		h2: %brand% stores in %city%
		title: '%brand% %address%, leaflet and opening hours 🕔'
		description: 'Compare the offers in the brochures, find out the exact address and opening hours, or read about the assortment waiting for you at the %brand% %address% store.'
		open: Open
		closed: Closed
		text: 'The store %brand% %address% regularly offers advantageous promotions and discounts on a variety of products, where customers can take advantage of the popular %brand% flyer when shopping.'
		h2bottom: %brand% leaflet %city% %street%
		text2WithoutStores: 'Customers can comfortably view it online, just like the promotions available at other branches.'
		text2: 'Customers can conveniently browse them online, as well as the promotions available at the %stores% locations.'
		or: or
		others: and others
		textBottom: 'The %brand% store at %address% offers customers not only a wide range of products but also low prices, which %brand% regularly informs about in its flyers. The %brand% branch at %city% %street% is a popular spot for those looking for budget-friendly deals. Thanks to the availability of the %brand% flyer online, buyers always have access to the latest discounts. If the %brand% at %address% does not have everything the customer needs, they can also take'
		textBottom2: 'Find out the exact address, customer service contact, or opening hours of your favorite stores all in one place. You will also find information about which branches are located near you and where you can take advantage of additional deals advertised in the promotional flyers of selected stores.'
		sections:
			leaflets: Other catalogues from the category
			shops: Other stores in the area
			stores: Other %brand% stores nearby

	shop:
		title: %brand% catalogues %city%
		metaTitle: %brand% catalogues %city%
		storesTitle: %brand% stores in %city%
		h2: 'Catalogues, promotions, and discounts at the %brand% store in %city%'
		text: 'Promotional flyers for %brand% in %city% and their current discounts and promotions. In the %brand% flyer for %city%, you will find a wide range of products at the best prices. However, %city% is not only home to %brand%. Other popular stores include %stores%, for example.'
		metaDescription: 'Promotional flyers for %brand% in %city% and their current discounts and promotions. In the %brand% %city% flyer, you will find a wide range of products at the best prices. However, %brand% is not the only store in %city%. Other popular stores include %stores%, for example.'
		leafletStores:
			title: %brand% catalogue %city%
			store: %brand% %city%

		cityLink: Catalogues in %city%
		shopLink: %shop% catalogues
		otherShops: More stores in %city%
		shopLeaflet: %brand% catalogues
		citiesInText: %brand% catalogue %city%
		offers: Deals in the %brand% catalogue %city%
		generatedText:
			1: The store %brand% in the city of %city% offers promotional flyers that we update for you every week.
			2: 'If you are looking for a new flyer from %brand%, you can view it by clicking on this link: <a href="%actualLeafletUrl%">Current flyer %brand% %city%</a>. The flyer is valid from %validSince% to %validTill%. Each flyer contains interesting promotional offers and discounts, seasonal sales, club prices, and a wide range of products.'
			3: 'The %brand% store is not only located in the city of %city%. You can also find %brand% store flyers in other nearby %stores%. All promotional flyers are available in the <a href="%leafletsUrl%">Flyers</a> section.'
			4: 'If you''re looking for more stores, popular ones include %stores%.'

	tag:
		title: Flyers in the category %tag% in the city of %city%
		titleWithTag: Flyers in the category %tag% in the city %city%
		text: Current flyers from the %tag% category in the city of %city%.
		metaTitle: Current flyers from the %tag% category in the city of %city%. Check out the discount offers and shop smartly today.

articles:
	title: Articles about promotional flyers in hypermarkets and supermarkets
	h1: Magazine
	description: Articles full of tips for easier shopping with new flyers in domestic hypermarkets and supermarkets. Take advantage of limited-time promotions and shop at discounted prices!
	otherArticles: Other Articles
	author:
		title: Author %author%
		description: Articles by author %author%
		articlesCount: %count% article|%count% articles|%count% articles'

showMore:
	offers: Other Products
	cities: More Cities
	allCities: All cities
	shops: Other stores
	leaflets: More Catalogues
	tags: More Specials

tabs:
	leaflets: Catalogues
	shop: Customer benefits
	product: Products
	contact: Contact


<?php

declare(strict_types=1);

namespace Kaufino\RootModule\Presenters;

use Nette;
use Nette\Application\Responses;
use Nette\Http;
use Nette\Utils\Strings;
use <PERSON>\Debugger;
use Tracy\ILogger;

final class ErrorPresenter implements Nette\Application\IPresenter
{
	use Nette\SmartObject;

	/** @var ILogger */
	private $logger;

	public function __construct(ILogger $logger)
	{
		$this->logger = $logger;
	}

	public function run(Nette\Application\Request $request): Nette\Application\IResponse
	{
		$e = $request->getParameter('exception');

		bdump("ErrorPresenter");

		if ($e instanceof Nette\Application\BadRequestException) {
			// $this->logger->log("HTTP code {$e->getCode()}: {$e->getMessage()} in {$e->getFile()}:{$e->getLine()}", 'access');
			[$module, , $sep] = Nette\Application\Helpers::splitName($request->getPresenterName());

			$factory = new Nette\Http\RequestFactory();
			$httpRequest = $factory->fromGlobals();
			$domain = $httpRequest->getUrl()->getBaseUrl();

			bdump($domain);

			if (Strings::contains($domain, 'kaufino')) {
				$module = 'Kaufino';
			} elseif (Strings::contains($domain, 'letado')) {
				$module = 'Letado';
			} elseif (Strings::contains($domain, 'mroferto.com')) {
				$module = 'OfertoCom';
			} elseif (Strings::contains($domain, 'mrofert')) {
				$module = 'Oferto';
			} elseif (Strings::contains($domain, 'localhost')) {
				$module = 'Kaufino';
			}

			$errorPresenter = $module . $sep . 'Error4xx';

			return new Responses\ForwardResponse($request->setPresenterName($errorPresenter));
		}

		$this->logger->log($e, ILogger::EXCEPTION);

		return new Responses\CallbackResponse(static function (Http\IRequest $httpRequest, Http\IResponse $httpResponse): void {
			if (preg_match('#^text/html(?:;|$)#', (string)$httpResponse->getHeader('Content-Type'))) {
				require __DIR__ . '/templates/Error/500.phtml';
			}
		});
	}
}

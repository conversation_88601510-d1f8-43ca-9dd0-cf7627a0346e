<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\RootModule\Presenters;

use Ka<PERSON>ino\Model\Websites\Entities\Website;
use Kaufino\Presenters\BasePresenter;
use Nette\Utils\Strings;

class RobotsPresenter extends BasePresenter
{
	public function actionDefault()
	{
		$domain = $this->getHttpRequest()->getUrl()->getDomain();

		if (Strings::contains($domain, 'co.za') || Strings::contains($domain, 'co.uk') || Strings::contains($domain, 'com.br') || Strings::contains($domain, 'com.tr')) {
			$domain = $this->getHttpRequest()->getUrl()->getDomain(3);
		}

		if ($this->website && $this->website->getModule() === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$this->setView('kaufinoSubdomainRobots');
			return;
		}

		$this->template->website = $this->website;

		if ($domain === 'mroferto.com' || $domain === 'mroferto.comlocal') {
			$this->setView('ofertoComRobots');
		} elseif (Strings::contains($domain, 'kaufino')) {
			$this->setView('kaufinoRobots');
		} elseif (Strings::contains($domain, 'letado') || Strings::contains($domain, 'oferito')) {
			$this->setView('letadoRobots');
		} elseif (Strings::contains($domain, 'mrofert')) {
			$this->setView('ofertoRobots');
		} elseif (Strings::contains($domain, 'localhost')) {
			$this->setView('kaufinoRobots');
		}
	}
}

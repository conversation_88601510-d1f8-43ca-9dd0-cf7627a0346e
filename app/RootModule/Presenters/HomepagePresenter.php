<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\RootModule\Presenters;

use <PERSON><PERSON>ino\Model\Localization\GeoResolver;
use Kaufino\Model\Websites\Entities\Website;
use Kaufino\Presenters\BasePresenter;
use Nette\Utils\Strings;
use Tracy\Debugger;

class HomepagePresenter extends BasePresenter
{
	public function actionDefault()
	{
		$this->disableCachedResponse = true;

		$redirectUrl = $this->link('//this');
		$showHomepage = false;
		$isGooglebot = false;

		if (Strings::contains($this->getHttpRequest()->getHeader('User-Agent'), 'Googlebot')) {
			$isGooglebot = true;
			$detectedCountry = '';
			if (isset($_SERVER["HTTP_CF_IPCOUNTRY"])) {
				$detectedCountry = \Nette\Utils\Strings::lower($_SERVER["HTTP_CF_IPCOUNTRY"]);
			}

			Debugger::log($detectedCountry . ' -> ' . $this->geoResolver->resolveRegionFromRequest() . ' - ' . $this->getHttpRequest()->getHeader('User-Agent'), 'googlebot');
		}

		$domain = $this->getHttpRequest()->getUrl()->getDomain();

		if (Strings::contains($domain, 'co.za')) {
			$domain = $this->getHttpRequest()->getUrl()->getDomain(3);
		}

		if (Strings::contains($domain, 'kaufino')) {
			if (!$isGooglebot) {
				$localization = $this->geoResolver->resolveLocalizationFromRequest();

				if ($localization) {
					$determinedWebsite = $this->websiteFacade->findActiveWebsiteByLocalization($localization, Website::MODULE_KAUFINO);

					if ($determinedWebsite) {
						$this->redirectUrl($determinedWebsite->getDomain(), 302);
					}
				}
			}

			$showHomepage = true;
		}

		if ($showHomepage) {
			$this->template->footerWebsites = function () {
				return $this->websiteFacade->findActiveWebsites(Website::MODULE_KAUFINO);
			};
		} else {
			$region = $this->geoResolver->resolveRegionFromRequest();

			if (Strings::contains($domain, 'letado')) {
				$localization = $this->localizationFacade->findLocalizationByRegion($region);

				$determinedWebsite = $this->websiteFacade->findActiveWebsiteByLocalization($localization, Website::MODULE_LETADO);

				if ($determinedWebsite) {
					$this->redirectUrl($determinedWebsite->getDomain(), 302);
				} else {
					$this->redirectUrl($redirectUrl . GeoResolver::DEFAULT_REGION, 302);
				}
			}

			$this->redirectUrl($redirectUrl . $region . '/', 302);
		}
	}
}

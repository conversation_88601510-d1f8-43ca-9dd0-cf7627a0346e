<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\RootModule\Presenters;

use Kaufino\Model\Websites\Entities\Website;
use Kaufino\Presenters\BasePresenter;
use Nette\Utils\Strings;

class AdsPresenter extends BasePresenter
{
	public function actionDefault()
	{
		$domain = $this->getHttpRequest()->getUrl()->getDomain();

		if (Strings::contains($domain, 'co.za') || Strings::contains($domain, 'co.uk') || Strings::contains($domain, 'com.br') || Strings::contains($domain, 'com.tr')) {
			$domain = $this->getHttpRequest()->getUrl()->getDomain(3);
		}

		$this->template->website = $this->website;
		$this->template->websiteType = $this->website?->getModule();

		if ($this->website && $this->website->getModule() === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$this->setView('kaufinoSubdomainAds');
		} elseif (Strings::contains($domain, 'kaufino')) {
			$this->setView('kaufinoAds');
		} elseif (Strings::contains($domain, 'letado') || Strings::contains($domain, 'oferito') || Strings::contains($domain, 'mrofferto') || Strings::contains($domain, 'mr-offerto')) {
			if (Strings::contains($domain, 'mrofferto')) {
				$domain = 'mrofferto.com';
			} elseif (Strings::contains($domain, 'oferito')) {
				$domain = 'oferito.com';
			} elseif (Strings::contains($domain, 'mr-offerto')) {
				$domain = 'mr-offerto.com';
			} else {
				$domain = 'letado.com';
			}

			$this->template->domain = $domain;
			$this->setView('letadoAds');
		} elseif (Strings::contains($domain, 'mrofert')) {
			$this->setView('ofertoAds');
		} elseif (Strings::contains($domain, 'localhost')) {
			$this->setView('kaufinoAds');
		}

		if ($this->website) {
			$adsFileByWebsiteType = $this->website->getModule() . 'Ads';

			if (file_exists(__DIR__ . '/templates/Ads/' . $adsFileByWebsiteType . '.latte')) {
				$this->setView($adsFileByWebsiteType);
			}
		}
	}
}

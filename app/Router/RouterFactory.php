<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Router;

use <PERSON><PERSON><PERSON>\Model\Articles\ArticleFacade;
use <PERSON><PERSON><PERSON>\Model\Conditions\DocumentFacade;
use <PERSON><PERSON><PERSON>\Model\Conditions\Entities\Document;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON>ino\Model\Offers\OfferFacade;
use Ka<PERSON>ino\Model\Seo\Repositories\RedirectionRepository;
use Ka<PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\StoreFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON><PERSON><PERSON>\Model\Users\Entities\User;
use <PERSON><PERSON><PERSON>\Model\Users\UserFacade;
use Ka<PERSON><PERSON>\Model\Websites\Entities\Website;
use Ka<PERSON><PERSON>\Model\Websites\WebsiteFacade;
use Nette;
use Nette\Application\Routers\Route;
use Nette\Application\Routers\RouteList;
use Nextras\Routing\StaticRouter;

final class RouterFactory
{
	use Nette\StaticClass;

	private const PRODUCTION_KAUFINO = 'k';
	private const PRODUCTION_LETADO = 'l';

	public static function createRouter(
		Nette\Http\Request $request,
		WebsiteFacade $websiteFacade,
		ShopFacade $shopFacade,
		LeafletFacade $leafletFacade,
		TagFacade $tagFacade,
		GeoFacade $geoFacade,
		OfferFacade $offerFacade,
		ArticleFacade $articleFacade,
		RedirectionRepository $redirectionRepository,
		StoreFacade $storeFacade,
		UserFacade $userFacade,
		DocumentFacade $documentFacade
	): RouteList {
		$router = new RouteList();

		$router->add(new StaticRouter([
			'Admin:Redactor:uploadImage' => 'admin/redactor-upload-image',
			'Cron:Homepage:processLeaflets' => 'cron/process-leaflets',
			'Cron:Homepage:processLeafletPages' => 'cron/process-leaflet-pages',
			'Cron:Homepage:processShops' => 'cron/process-shops',
			'Cron:Homepage:processTopLeaflets' => 'cron/process-top-leaflets',
			'Cron:Homepage:processCoupons' => 'cron/process-coupons',
			'Cron:Homepage:processProducts' => 'cron/process-products',
			'Cron:Homepage:processTags' => 'cron/process-tags',
			'Cron:Homepage:processCities' => 'cron/process-cities',
			'Cron:Homepage:processReviews' => 'cron/process-reviews',
			'Cron:Homepage:processStores' => 'cron/process-stores',
			'Cron:Homepage:processCitiesFromFile' => 'cron/process-cities-from-file',
			'Cron:Homepage:removeOfferImages' => 'cron/remove-offer-images',
			'Cron:Homepage:processGoogleIndex' => 'cron/process-google-index',
		]));

		$router->add(new StaticRouter([
			'Api:Homepage:shops' => 'api/v1/shops',
			'Api:Homepage:leaflets' => 'api/v1/leaflets',
			'Api:Homepage:leaflet' => 'api/v1/leaflet',
			'Api:Homepage:addEmail' => 'api/v1/add-email',
			'Api:Homepage:clearResponseCache' => 'api/v1/clear-response-cache',
			'Api:Offerista:brochureImpression' => 'api/v1/offerista/brochure-impression',
			'Api:Offerista:brochureClick' => 'api/v1/offerista/brochure-click',
			'Api:Offerista:brochurePageView' => 'api/v1/offerista/brochure-page-view',
			'Api:Offerista:brochurePageDuration' => 'api/v1/offerista/brochure-page-duration',
			'Api:Offerista:trackingPixel' => 'api/v1/offerista/tracking-pixel',
			'Api:Offerista:resolveUserLocationFromIp' => 'api/v1/offerista/resolve-user-location-from-ip',
			'Api:Offerista:resolveUserLocationFromPosition' => 'api/v1/offerista/resolve-user-location-from-position',
			'Api:Offerista:test' => 'api/v1/offerista/test',
			'Api:Offerista:clickout' => 'click',
			'Api:Offerista:brochurePageViewDuration' => 'api/v1/offerista/brochure-page-view-duration',
		]));

		$router->addRoute('test', 'Test:default');
		$router->addRoute('test-cache', 'Test:cache');

		$router->addRoute('admin/<presenter>/<action>[/<id>]', [
				'module' => 'Admin',
				'presenter' => 'Homepage',
				'action' => 'default',
				'id' => null,
			]);

		// Skip website resolution in CLI context (e.g., during schema creation)
		$website = null;
		$localization = null;
		if (PHP_SAPI !== 'cli') {
			$website = $websiteFacade->resolveCurrentWebsite();
			$localization = $website ? $website->getLocalization() : null;
		}

		$router->addRoute('robots.txt', 'Root:Robots:default');
		$router->addRoute('ads.txt', 'Root:Ads:default');

		if (PHP_SAPI === 'cli' || $localization === null) {
			// lokalizacni sluzba pro urceni regionu pro .com domeny
			$router->addRoute('/', 'Root:Homepage:default');

			return $router;
		}

		$tokens = self::getTokens($localization ? $localization->getFullLocale() : 'cs_CZ');

		if ($website->isKaufino()) {
			$router->addRoute('<region [a-z]{2}>/' . $tokens['shops'], [
					'module' => 'Kaufino',
					'presenter' => 'Shops',
					'action' => 'shops',
				]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['leaflets'], [
					'module' => 'Kaufino',
					'presenter' => 'Leaflets',
					'action' => 'leaflets',
				]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['offers'], [
					'module' => 'Kaufino',
					'presenter' => 'Offers',
					'action' => 'offers',
				]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['search'] . '[/<q>]', [
					'module' => 'Kaufino',
					'presenter' => 'Search',
					'action' => 'search',
				]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['articles'], [
				'module' => 'Kaufino',
				'presenter' => 'Articles',
				'action' => 'articles',
			]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['cities'], [
				'module' => 'Kaufino',
				'presenter' => 'Cities',
				'action' => 'cities',
			]);

			$router->addRoute('<region [a-z]{2}>/' . '<article>', [
				'module' => 'Kaufino',
				'presenter' => 'Article',
				'action' => 'article',
				'article' => [
					Route::FILTER_IN => static function ($article) use ($articleFacade, $website) {
						return $articleFacade->findArticleBySlug($website, $article);
					},
					Route::FILTER_OUT => static function ($article) {
						return $article->getSlug();
					},
				],
			]);

			if (isset($tokens['author'])) {
				$router->addRoute('<region [a-z]{2}>/' . $tokens['author'] . '/<author>', [
					'module' => 'Kaufino',
					'presenter' => 'Articles',
					'action' => 'author',
					'author' => [
						Route::FILTER_IN => static function ($author) use ($userFacade) {
							return $userFacade->findAuthorBySlug($author);
						},
						Route::FILTER_OUT => static function (User $author) {
							return $author->getSlug();
						},
					],
				]);
			}

			$router->addRoute('<region [a-z]{2}>/<shop>', [
					'module' => 'Kaufino',
					'presenter' => 'Shop',
					'action' => 'shop',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]);

			$router->addRoute('<region [a-z]{2}>/<shop>/<leaflet>', [
					'module' => 'Kaufino',
					'presenter' => 'Leaflet',
					'action' => 'leaflet',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
					'leaflet' => [
						Route::FILTER_IN => static function ($leaflet) use ($leafletFacade, $localization) {
							return $leafletFacade->findLeafletBySlug($localization, $leaflet, false);
						},
						Route::FILTER_OUT => static function ($leaflet) {
							return $leaflet->getSlug();
						},
					],
				]);

			$router->addRoute('<region [a-z]{2}>/'  . $tokens['offers'] . '/<tag>', [
				'module' => 'Kaufino',
				'presenter' => 'Offers',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_SHOPS, Tag::TYPE_OFFERS);
					},
					Route::FILTER_OUT => static function (Tag $tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/<city>', [
					'module' => 'Kaufino',
					'presenter' => 'City',
					'action' => 'city',
					'city' => [
						Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
							return $geoFacade->findCityBySlug($localization, $city);
						},
						Route::FILTER_OUT => static function ($city) {
							return $city->getSlug();
						},
					],
				]);

			$router->addRoute('<region [a-z]{2}>/<tag>', [
				'module' => 'Kaufino',
				'presenter' => 'Tag',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_SHOPS, Tag::TYPE_OFFERS);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/<city>/<shop>', [
					'module' => 'Kaufino',
					'presenter' => 'City',
					'action' => 'shop',
					'city' => [
						Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
							return $geoFacade->findCityBySlug($localization, $city);
						},
						Route::FILTER_OUT => static function ($city) {
							return $city->getSlug();
						},
					],
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]);

			$router->addRoute('<region [a-z]{2}>/<city>/<tag>', [
				'module' => 'Kaufino',
				'presenter' => 'City',
				'action' => 'tag',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/exit/o/<offer>', [
					'module' => 'Kaufino',
					'presenter' => 'Exit',
					'action' => 'offer',
					'offer' => [
						Route::FILTER_IN => static function ($offer) use ($offerFacade, $localization) {
							$offer = $offerFacade->findOffer($offer);

							if ($offer->getLocalization()->getId() != $localization->getId()) {
								return null;
							}

							return $offer;
						},
						Route::FILTER_OUT => static function ($offer) {
							return $offer->getId();
						},
					],
				]);

			$router->addRoute('<region [a-z]{2}>/exit/s/<shop>', [
					'module' => 'Kaufino',
					'presenter' => 'Exit',
					'action' => 'shop',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							$shop = $shopFacade->findShop($shop);

							if ($shop->getLocalization()->getId() != $localization->getId()) {
								return null;
							}

							return $shop;
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getId();
						},
					],
				]);

			$router->addRoute('<region [a-z]{2}>/<shop>/' . $tokens['archive'], [
				'module' => 'Kaufino',
				'presenter' => 'Archive',
				'action' => 'archive',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/ajax/search[/<search>]', 'Kaufino:Ajax:search');

			$router->addRoute('<region [a-z]{2}>/<city>/<shop>[/<store>]', [
				'module' => 'Kaufino',
				'presenter' => 'City',
				'action' => 'store',
				null => [
					Route::FILTER_IN => static function ($params) use ($storeFacade, $localization, $geoFacade, $shopFacade) {
						$params['city'] = $geoFacade->findCityBySlug($localization, $params['city']);
						$params['shop'] = $shopFacade->findShopBySlug($localization, $params['shop']);

						if (!$params['city'] || !$params['shop']) {
							return null;
						}

						$params['store'] = $storeFacade->findStoreBySlug($params['city'], $params['shop'], $params['store'], Website::MODULE_KAUFINO);
						return $params;
					},
					Route::FILTER_OUT => static function ($params) {
						return array_merge($params, [
							'city' => $params['city']->getSlug(),
							'shop' => $params['shop']->getSlug(),
							'store' => $params['store'] ? $params['store']->getSlug() : null,
						]);
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['aboutUs'], 'Kaufino:Static:aboutUs');
			$router->addRoute('<region [a-z]{2}>/' . $tokens['cookies'], 'Kaufino:Conditions:cookies');
			$router->addRoute('<region [a-z]{2}>/' . $tokens['copy'], 'Kaufino:Static:copy');
			$router->addRoute('<region [a-z]{2}>/sitemap.xml', 'Kaufino:Sitemap:sitemap');
			$router->addRoute('<region [a-z]{2}>/<type>.xml', 'Kaufino:Sitemap:type');

			$router->addRoute(
				'<region [a-z]{2}>/<redirection .+>',
				[
					'presenter' => 'Kaufino:Redirection',
					'action' => 'redirection',
					'redirection' => [
						Route::FILTER_IN => static function ($url) use ($localization, $website, $redirectionRepository) {
							return $redirectionRepository->findOneBy(['localization' => $localization, 'website' => $website, 'oldUrl' => $url]);
						},
						Route::FILTER_OUT => static function ($url) {
							return $url->getOldUrl();
						},
					],
				]
			);

			$router->addRoute('new/<region [a-z]{2}>/' . $tokens['shops'], [
				'module' => 'NewKaufino',
				'presenter' => 'Shops',
				'action' => 'shops',
			]);

			$router->addRoute('new/<region [a-z]{2}>/' . $tokens['leaflets'], [
				'module' => 'NewKaufino',
				'presenter' => 'Leaflets',
				'action' => 'leaflets',
			]);

			$router->addRoute('new/<region [a-z]{2}>/' . $tokens['offers'], [
				'module' => 'NewKaufino',
				'presenter' => 'Offers',
				'action' => 'offers',
			]);

			$router->addRoute('new/<region [a-z]{2}>/' . $tokens['search'] . '[/<q>]', [
				'module' => 'NewKaufino',
				'presenter' => 'Search',
				'action' => 'search',
			]);

			$router->addRoute('new/<region [a-z]{2}>/' . $tokens['articles'], [
				'module' => 'NewKaufino',
				'presenter' => 'Articles',
				'action' => 'articles',
			]);

			$router->addRoute('new/<region [a-z]{2}>/' . $tokens['cities'], [
				'module' => 'NewKaufino',
				'presenter' => 'Cities',
				'action' => 'cities',
			]);

			$router->addRoute('new/<region [a-z]{2}>/' . '<article>', [
				'module' => 'NewKaufino',
				'presenter' => 'Article',
				'action' => 'article',
				'article' => [
					Route::FILTER_IN => static function ($article) use ($articleFacade, $website) {
						return $articleFacade->findArticleBySlug($website, $article);
					},
					Route::FILTER_OUT => static function ($article) {
						return $article->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/<document>', [
				'module' => 'Kaufino',
				'presenter' => 'Conditions',
				'action' => 'default',
				'document' => [
					Route::FILTER_IN => static function ($document) use ($documentFacade, $localization) {
						return $documentFacade->findBySlug($localization, $document);
					},
					Route::FILTER_OUT => static function (Document $document) {
						return $document->getSlug();
					},
				],
			]);

			if (isset($tokens['author'])) {
				$router->addRoute('new/<region [a-z]{2}>/' . $tokens['author'] . '/<author>', [
					'module' => 'NewKaufino',
					'presenter' => 'Articles',
					'action' => 'author',
					'author' => [
						Route::FILTER_IN => static function ($author) use ($userFacade) {
							return $userFacade->findAuthorBySlug($author);
						},
						Route::FILTER_OUT => static function (User $author) {
							return $author->getSlug();
						},
					],
				]);
			}

			$router->addRoute('new/<region [a-z]{2}>/<shop>', [
				'module' => 'NewKaufino',
				'presenter' => 'Shop',
				'action' => 'shop',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/<shop>/<leaflet>', [
				'module' => 'NewKaufino',
				'presenter' => 'Leaflet',
				'action' => 'leaflet',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
				'leaflet' => [
					Route::FILTER_IN => static function ($leaflet) use ($leafletFacade, $localization) {
						return $leafletFacade->findLeafletBySlug($localization, $leaflet, false);
					},
					Route::FILTER_OUT => static function ($leaflet) {
						return $leaflet->getSlug();
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/<tag>', [
				'module' => 'NewKaufino',
				'presenter' => 'Tag',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_SHOPS, Tag::TYPE_OFFERS);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/<city>', [
				'module' => 'NewKaufino',
				'presenter' => 'City',
				'action' => 'city',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/<city>/<shop>', [
				'module' => 'NewKaufino',
				'presenter' => 'City',
				'action' => 'shop',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/<city>/<tag>', [
				'module' => 'NewKaufino',
				'presenter' => 'City',
				'action' => 'tag',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/exit/o/<offer>', [
				'module' => 'NewKaufino',
				'presenter' => 'Exit',
				'action' => 'offer',
				'offer' => [
					Route::FILTER_IN => static function ($offer) use ($offerFacade, $localization) {
						$offer = $offerFacade->findOffer($offer);

						if ($offer->getLocalization()->getId() != $localization->getId()) {
							return null;
						}

						return $offer;
					},
					Route::FILTER_OUT => static function ($offer) {
						return $offer->getId();
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/exit/s/<shop>', [
				'module' => 'NewKaufino',
				'presenter' => 'Exit',
				'action' => 'shop',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						$shop = $shopFacade->findShop($shop);

						if ($shop->getLocalization()->getId() != $localization->getId()) {
							return null;
						}

						return $shop;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getId();
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/<shop>/' . $tokens['archive'], [
				'module' => 'NewKaufino',
				'presenter' => 'Archive',
				'action' => 'archive',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/ajax/search[/<search>]', 'NewKaufino:Ajax:search');

			$router->addRoute('new/<region [a-z]{2}>/<city>/<shop>[/<store>]', [
				'module' => 'NewKaufino',
				'presenter' => 'City',
				'action' => 'store',
				null => [
					Route::FILTER_IN => static function ($params) use ($storeFacade, $localization, $geoFacade, $shopFacade) {
						$params['city'] = $geoFacade->findCityBySlug($localization, $params['city']);
						$params['shop'] = $shopFacade->findShopBySlug($localization, $params['shop']);

						if (!$params['city'] || !$params['shop']) {
							return null;
						}

						$params['store'] = $storeFacade->findStoreBySlug($params['city'], $params['shop'], $params['store'], Website::MODULE_KAUFINO);

						return $params;
					},
					Route::FILTER_OUT => static function ($params) {
						return array_merge($params, [
							'city' => $params['city']->getSlug(),
							'shop' => $params['shop']->getSlug(),
							'store' => $params['store'] ? $params['store']->getSlug() : null,
						]);
					},
				],
			]);

			$router->addRoute('new/<region [a-z]{2}>/' . $tokens['aboutUs'], 'NewKaufino:Static:aboutUs');
			$router->addRoute('new/<region [a-z]{2}>/' . $tokens['cookies'], 'NewKaufino:Static:cookies');
			$router->addRoute('new/<region [a-z]{2}>/' . $tokens['copy'], 'NewKaufino:Static:copy');
			$router->addRoute('new/<region [a-z]{2}>/sitemap.xml', 'NewKaufino:Sitemap:sitemap');

			$router->addRoute(
				'new/<region [a-z]{2}>/<redirection .+>',
				[
					'presenter' => 'NewKaufino:Redirection',
					'action' => 'redirection',
					'redirection' => [
						Route::FILTER_IN => static function ($url) use ($localization, $website, $redirectionRepository) {
							return $redirectionRepository->findOneBy(['localization' => $localization, 'website' => $website, 'oldUrl' => $url]);
						},
						Route::FILTER_OUT => static function ($url) {
							return $url->getOldUrl();
						},
					],
				]
			);

			$router->addRoute('new/<region=no [a-z]{2}>', 'NewKaufino:Homepage:default');
			$router->addRoute('<region=no [a-z]{2}>/<presenter>/<action>', 'Kaufino:Homepage:default');
		}


		if ($website->isOfertoCom()) {
			$router->addRoute('<region [a-z]{2}>/' . $tokens['shops'], [
				'module' => 'OfertoCom',
				'presenter' => 'Shops',
				'action' => 'shops',
			]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['leaflets'], [
				'module' => 'OfertoCom',
				'presenter' => 'Leaflets',
				'action' => 'leaflets',
			]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['deals'], [
				'module' => 'OfertoCom',
				'presenter' => 'Offers',
				'action' => 'offers',
			]);

			$router->addRoute('<region [a-z]{2}>/<shop>', [
				'module' => 'OfertoCom',
				'presenter' => 'Shop',
				'action' => 'shop',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/<shop>/<leaflet>', [
				'module' => 'OfertoCom',
				'presenter' => 'Leaflet',
				'action' => 'leaflet',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
				'leaflet' => [
					Route::FILTER_IN => static function ($leaflet) use ($leafletFacade, $localization) {
						return $leafletFacade->findLeafletBySlug($localization, $leaflet, false);
					},
					Route::FILTER_OUT => static function ($leaflet) {
						return $leaflet->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/exit/s/<shop>', [
				'module' => 'OfertoCom',
				'presenter' => 'Exit',
				'action' => 'shop',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						$shop = $shopFacade->findShop($shop);

						if ($shop->getLocalization()->getId() != $localization->getId()) {
							return null;
						}

						return $shop;
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getId();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/exit/o/<offer>', [
				'module' => 'OfertoCom',
				'presenter' => 'Exit',
				'action' => 'offer',
				'offer' => [
					Route::FILTER_IN => static function ($offer) use ($offerFacade, $localization) {
						$offer = $offerFacade->findOffer($offer);

						if ($offer->getLocalization()->getId() != $localization->getId()) {
							return null;
						}

						return $offer;
					},
					Route::FILTER_OUT => static function ($offer) {
						return $offer->getId();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['coupons'], [
				'module' => 'OfertoCom',
				'presenter' => 'Coupons',
				'action' => 'coupons',
			]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['search'] . '/<q>', [
				'module' => 'OfertoCom',
				'presenter' => 'Search',
				'action' => 'search',
			]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['articles'], [
				'module' => 'OfertoCom',
				'presenter' => 'Articles',
				'action' => 'articles',
			]);

			$router->addRoute('<region [a-z]{2}>/' . '<article>', [
				'module' => 'OfertoCom',
				'presenter' => 'Article',
				'action' => 'article',
				'article' => [
					Route::FILTER_IN => static function ($article) use ($articleFacade, $website) {
						return $articleFacade->findArticleBySlug($website, $article);
					},
					Route::FILTER_OUT => static function ($article) {
						return $article->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/' . $tokens['aboutUs'], 'OfertoCom:Static:aboutUs');
			$router->addRoute('<region [a-z]{2}>/' . $tokens['cookies'], 'OfertoCom:Conditions:cookies');
			$router->addRoute('<region [a-z]{2}>/sitemap.xml', 'OfertoCom:Sitemap:sitemap');
			$router->addRoute('<region [a-z]{2}>/<type>.xml', 'OfertoCom:Sitemap:type');
			$router->addRoute('<region [a-z]{2}>/ajax/search[/<search>]', 'OfertoCom:Ajax:search');

			$router->addRoute(
				'<region [a-z]{2}>/<redirection .+>',
				[
					'presenter' => 'OfertoCom:Redirection',
					'action' => 'redirection',
					'redirection' => [
						Route::FILTER_IN => static function ($url) use ($localization, $website, $redirectionRepository) {
							return $redirectionRepository->findOneBy(['localization' => $localization, 'website' => $website, 'oldUrl' => $url]);
						},
						Route::FILTER_OUT => static function ($url) {
							return $url->getOldUrl();
						},
					],
				]
			);

			$router->addRoute('<region [a-z]{2}>/<document>', [
				'module' => 'OfertoCom',
				'presenter' => 'Conditions',
				'action' => 'default',
				'document' => [
					Route::FILTER_IN => static function ($document) use ($documentFacade, $localization) {
						return $documentFacade->findBySlug($localization, $document);
					},
					Route::FILTER_OUT => static function (Document $document) {
						return $document->getSlug();
					},
				],
			]);

			$router->addRoute('<region=no>/<presenter>/<action>', 'OfertoCom:Homepage:default');
		}

		if ($website->isLetado()) {
			$channelParams = 'a1|b1|c1|a2|b2|c2|a3|b3|c3|c5a|c5b|d1|e1|f1|g1|h1|i1|j1|k1|e2|e3|t1';
			$channelPath = '[<channel ' . $channelParams . '>/]';

			$router->addRoute('<region [a-z]{2}>/' . $channelPath . $tokens['shops'], [
					'module' => 'Letado',
					'presenter' => 'Shops',
					'action' => 'shops',
				]);

			$router->addRoute('<region [a-z]{2}>/' . $channelPath . $tokens['leaflets'], [
					'module' => 'Letado',
					'presenter' => 'Leaflets',
					'action' => 'leaflets',
				]);

			$router->addRoute('<region [a-z]{2}>/' . $channelPath . $tokens['search'] . '/<q>', [
					'module' => 'Letado',
					'presenter' => 'Search',
					'action' => 'search',
				]);

			$router->addRoute('<region [a-z]{2}>/' . $channelPath . '<shop>', [
					'module' => 'Letado',
					'presenter' => 'Shop',
					'action' => 'shop',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]);

			$router->addRoute('<region [a-z]{2}>/' . $channelPath . '<shop>/<leaflet>', [
					'module' => 'Letado',
					'presenter' => 'Leaflet',
					'action' => 'leaflet',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
					'leaflet' => [
						Route::FILTER_IN => static function ($leaflet) use ($leafletFacade, $localization) {
							return $leafletFacade->findLeafletBySlug($localization, $leaflet, false);
						},
						Route::FILTER_OUT => static function ($leaflet) {
							return $leaflet->getSlug();
						},
					],
				]);


			$router->addRoute('<region [a-z]{2}>/' . $channelPath . '<tag>', [
				'module' => 'Letado',
				'presenter' => 'Tag',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_SHOPS, Tag::TYPE_OFFERS);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/' . $channelPath . '<shop>/' . $tokens['archive'], [
				'module' => 'Letado',
				'presenter' => 'Archive',
				'action' => 'archive',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute('<region [a-z]{2}>/' . $channelPath . $tokens['aboutUs'], 'Letado:Static:aboutUs');
			$router->addRoute('<region [a-z]{2}>/' . $channelPath . $tokens['cookies'], 'Letado:Conditions:cookies');
			$router->addRoute('<region [a-z]{2}>/sitemap.xml', 'Letado:Sitemap:sitemap');
			$router->addRoute('<region [a-z]{2}>/<type>.xml', 'Letado:Sitemap:type');
			$router->addRoute('<region [a-z]{2}>/ ' . $channelPath . 'ajax/search[/<search>]', 'Letado:Ajax:search');

			$router->addRoute(
				'<region [a-z]{2}>/<redirection .+>',
				[
					'presenter' => 'Letado:Redirection',
					'action' => 'redirection',
					'redirection' => [
						Route::FILTER_IN => static function ($url) use ($localization, $website, $redirectionRepository) {
							return $redirectionRepository->findOneBy(['localization' => $localization, 'website' => $website, 'oldUrl' => $url]);
						},
						Route::FILTER_OUT => static function ($url) {
							return $url->getOldUrl();
						},
					],
				]
			);

			$router->addRoute('<region [a-z]{2}>/' . $channelPath . '<document>', [
				'module' => 'Letado',
				'presenter' => 'Conditions',
				'action' => 'default',
				'document' => [
					Route::FILTER_IN => static function ($document) use ($documentFacade, $localization) {
						return $documentFacade->findBySlug($localization, $document);
					},
					Route::FILTER_OUT => static function (Document $document) {
						return $document->getSlug();
					},
				],
			]);

			$router->addRoute('<region=no>/' . $channelPath . '<presenter>/<action>', 'Letado:Homepage:default');
		}

		if ($website->isOferto()) {
			$channelParams = 'a1|b1|c1|a2|b2|c2|a3|b3|c3|c5a|c5b|d1|e1|f1|g1|h1|i1|j1|k1|e2|e3|t1';
			$channelPath = '[<channel ' . $channelParams . '>/]';

			$router->addRoute($channelPath . $tokens['shops'], [
					'module' => 'Oferto',
					'presenter' => 'Shops',
					'action' => 'shops',
				]);

			$router->addRoute($channelPath . $tokens['leaflets'], [
					'module' => 'Oferto',
					'presenter' => 'Leaflets',
					'action' => 'leaflets',
				]);

			$router->addRoute($channelPath . $tokens['articles'], [
					'module' => 'Oferto',
					'presenter' => 'Articles',
					'action' => 'articles',
				]);

			$router->addRoute($channelPath . $tokens['search'] . '/<q>', [
					'module' => 'Oferto',
					'presenter' => 'Search',
					'action' => 'search',
				]);

			$router->addRoute($channelPath . '<shop>', [
					'module' => 'Oferto',
					'presenter' => 'Shop',
					'action' => 'shop',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]);

			if ($localization->isHungarian()) {
				$router->addRoute($channelPath . '<shop>/esemenyek', [
					'module' => 'Oferto',
					'presenter' => 'Shop',
					'action' => 'offers',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]);

				$router->addRoute($channelPath . '<shop>/agazatok', [
					'module' => 'Oferto',
					'presenter' => 'Shop',
					'action' => 'stores',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]);
			}

			$router->addRoute($channelPath . '<shop>/<leaflet>', [
					'module' => 'Oferto',
					'presenter' => 'Leaflet',
					'action' => 'leaflet',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
					'leaflet' => [
						Route::FILTER_IN => static function ($leaflet) use ($leafletFacade, $localization) {
							return $leafletFacade->findLeafletBySlug($localization, $leaflet, false);
						},
						Route::FILTER_OUT => static function ($leaflet) {
							return $leaflet->getSlug();
						},
					],
				]);

			$router->addRoute($channelPath . '<article>', [
					'module' => 'Oferto',
					'presenter' => 'Article',
					'action' => 'article',
					'article' => [
						Route::FILTER_IN => static function ($article) use ($articleFacade, $website) {
							return $articleFacade->findArticleBySlug($website->getParentWebsite(), $article);
						},
						Route::FILTER_OUT => static function ($article) {
							return $article->getSlug();
						},
					],
				]);

			$router->addRoute($channelPath . '<tag>', [
				'module' => 'Oferto',
				'presenter' => 'Tag',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_OFFERS, Tag::TYPE_SHOPS);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . $tokens['cities'], [
				'module' => 'Oferto',
				'presenter' => 'Cities',
				'action' => 'cities',
			]);

			$router->addRoute($channelPath . '<city>', [
				'module' => 'Oferto',
				'presenter' => 'City',
				'action' => 'city',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . '<city>/<shop>', [
				'module' => 'Oferto',
				'presenter' => 'City',
				'action' => 'shop',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			if (isset($tokens['products'])) {
				$router->addRoute($channelPath . $tokens['products'] . '/<tag>', [
					'module' => 'Oferto',
					'presenter' => 'Tag',
					'action' => 'products',
					'tag' => [
						Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
							return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_OFFERS);
						},
						Route::FILTER_OUT => static function ($tag) {
							return $tag->getSlug();
						},
					],
				]);
			}

			$router->addRoute($channelPath . '<shop>/' . $tokens['archive'], [
				'module' => 'Oferto',
				'presenter' => 'Archive',
				'action' => 'archive',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . $tokens['offers'], [
				'module' => 'Oferto',
				'presenter' => 'Offers',
				'action' => 'offers',
			]);

			$router->addRoute($channelPath . '<tag>', [
				'module' => 'Oferto',
				'presenter' => 'Tag',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_OFFERS, Tag::TYPE_SHOPS);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . 'ajax/search[/<search>]', 'Oferto:Ajax:search');

			$router->addRoute($channelPath . $tokens['aboutUs'], 'Oferto:Static:aboutUs');
			$router->addRoute($channelPath . $tokens['cookies'], 'Oferto:Conditions:cookies');
			$router->addRoute($channelPath . $tokens['copy'], 'Oferto:Static:copy');
			$router->addRoute($channelPath . 'offer', 'Oferto:Static:offer');
			$router->addRoute($channelPath . 'sitemap.xml', 'Oferto:Sitemap:sitemap');
			$router->addRoute($channelPath . '<type>.xml', 'Oferto:Sitemap:type');
			//$router->addRoute('<other .+>', 'Oferto:Homepage:other');

			$router->addRoute($channelPath . '<city>/<shop>[/<store>]', [
				'module' => 'Oferto',
				'presenter' => 'City',
				'action' => 'store',
				null => [
					Route::FILTER_IN => static function ($params) use ($storeFacade, $localization, $geoFacade, $shopFacade) {
						$params['city'] = $geoFacade->findCityBySlug($localization, $params['city']);
						$params['shop'] = $shopFacade->findShopBySlug($localization, $params['shop']);

						if (!$params['city'] || !$params['shop']) {
							return null;
						}

						$params['store'] = $storeFacade->findStoreBySlug($params['city'], $params['shop'], $params['store'], Website::MODULE_OFERTO);

						return $params;
					},
					Route::FILTER_OUT => static function ($params) {
						return array_merge($params, [
							'city' => $params['city']->getSlug(),
							'shop' => $params['shop']->getSlug(),
							'store' => $params['store'] ? $params['store']->getSlug() : null,
						]);
					},
				],
			]);

			$router->addRoute(
				$channelPath . '<redirection .+>',
				[
					'presenter' => 'Oferto:Redirection',
					'action' => 'redirection',
					'redirection' => [
						Route::FILTER_IN => static function ($url) use ($localization, $website, $redirectionRepository) {
							return $redirectionRepository->findOneBy(['localization' => $localization, 'website' => $website, 'oldUrl' => $url]);
						},
						Route::FILTER_OUT => static function ($url) {
							return $url->getOldUrl();
						},
					],
				]
			);

			$router->addRoute($channelPath . '<document>', [
				'module' => 'Oferto',
				'presenter' => 'Conditions',
				'action' => 'default',
				'document' => [
					Route::FILTER_IN => static function ($document) use ($documentFacade, $localization) {
						return $documentFacade->findBySlug($localization, $document);
					},
					Route::FILTER_OUT => static function (Document $document) {
						return $document->getSlug();
					},
				],
			]);

			$router->addRoute('new/' . $tokens['shops'], [
				'module' => 'NewOferto',
				'presenter' => 'Shops',
				'action' => 'shops',
			]);

			$router->addRoute('new/' . $tokens['leaflets'], [
				'module' => 'NewOferto',
				'presenter' => 'Leaflets',
				'action' => 'leaflets',
			]);

			$router->addRoute('new/' . $tokens['articles'], [
				'module' => 'NewOferto',
				'presenter' => 'Articles',
				'action' => 'articles',
			]);

			$router->addRoute('new/' . $tokens['search'] . '/<q>', [
				'module' => 'NewOferto',
				'presenter' => 'Search',
				'action' => 'search',
			]);

			$router->addRoute('new/' . '<shop>', [
				'module' => 'NewOferto',
				'presenter' => 'Shop',
				'action' => 'shop',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			if ($localization->isHungarian()) {
				$router->addRoute('new/' . '<shop>/esemenyek', [
					'module' => 'NewOferto',
					'presenter' => 'Shop',
					'action' => 'offers',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]);

				$router->addRoute('new/' . '<shop>/agazatok', [
					'module' => 'NewOferto',
					'presenter' => 'Shop',
					'action' => 'stores',
					'shop' => [
						Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
							return $shopFacade->findShopBySlug($localization, $shop);
						},
						Route::FILTER_OUT => static function ($shop) {
							return $shop->getSlug();
						},
					],
				]);
			}

			$router->addRoute('new/' . '<shop>/<leaflet>', [
				'module' => 'NewOferto',
				'presenter' => 'Leaflet',
				'action' => 'leaflet',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
				'leaflet' => [
					Route::FILTER_IN => static function ($leaflet) use ($leafletFacade, $localization) {
						return $leafletFacade->findLeafletBySlug($localization, $leaflet, false);
					},
					Route::FILTER_OUT => static function ($leaflet) {
						return $leaflet->getSlug();
					},
				],
			]);

			$router->addRoute('new/' . '<article>', [
				'module' => 'NewOferto',
				'presenter' => 'Article',
				'action' => 'article',
				'article' => [
					Route::FILTER_IN => static function ($article) use ($articleFacade, $website) {
						return $articleFacade->findArticleBySlug($website->getParentWebsite(), $article);
					},
					Route::FILTER_OUT => static function ($article) {
						return $article->getSlug();
					},
				],
			]);

			$router->addRoute('new/' . '<tag>', [
				'module' => 'NewOferto',
				'presenter' => 'Tag',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_OFFERS, Tag::TYPE_SHOPS);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute('new/' . $tokens['cities'], [
				'module' => 'NewOferto',
				'presenter' => 'Cities',
				'action' => 'cities',
			]);

			$router->addRoute('new/' . '<city>', [
				'module' => 'NewOferto',
				'presenter' => 'City',
				'action' => 'city',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
			]);

			$router->addRoute('new/' . '<city>/<shop>', [
				'module' => 'NewOferto',
				'presenter' => 'City',
				'action' => 'shop',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			if (isset($tokens['products'])) {
				$router->addRoute('new/' . $tokens['products'] . '/<tag>', [
					'module' => 'NewOferto',
					'presenter' => 'Tag',
					'action' => 'products',
					'tag' => [
						Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
							return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_OFFERS);
						},
						Route::FILTER_OUT => static function ($tag) {
							return $tag->getSlug();
						},
					],
				]);
			}

			$router->addRoute('new/' . '<shop>/' . $tokens['archive'], [
				'module' => 'NewOferto',
				'presenter' => 'Archive',
				'action' => 'archive',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute('new/' . $tokens['offers'], [
				'module' => 'NewOferto',
				'presenter' => 'Offers',
				'action' => 'offers',
			]);

			$router->addRoute('new/' . '<tag>', [
				'module' => 'NewOferto',
				'presenter' => 'Tag',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_OFFERS, Tag::TYPE_SHOPS);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute('new/' . 'ajax/search[/<search>]', 'NewOferto:Ajax:search');

			$router->addRoute('new/' . $tokens['aboutUs'], 'NewOferto:Static:aboutUs');
			$router->addRoute('new/' . $tokens['cookies'], 'NewOferto:Conditions:cookies');
			$router->addRoute('new/' . $tokens['copy'], 'NewOferto:Static:copy');
			$router->addRoute('new/' . 'offer', 'NewOferto:Static:offer');
			$router->addRoute('new/' . 'sitemap.xml', 'NewOferto:Sitemap:sitemap');
			$router->addRoute('new/' . '<type>.xml', 'NewOferto:Sitemap:type');
			//$router->addRoute('<other .+>', 'Oferto:Homepage:other');

			$router->addRoute('new/' . '<city>/<shop>[/<store>]', [
				'module' => 'NewOferto',
				'presenter' => 'City',
				'action' => 'store',
				null => [
					Route::FILTER_IN => static function ($params) use ($storeFacade, $localization, $geoFacade, $shopFacade) {
						$params['city'] = $geoFacade->findCityBySlug($localization, $params['city']);
						$params['shop'] = $shopFacade->findShopBySlug($localization, $params['shop']);

						if (!$params['city'] || !$params['shop']) {
							return null;
						}

						$params['store'] = $storeFacade->findStoreBySlug($params['city'], $params['shop'], $params['store'], Website::MODULE_OFERTO);

						return $params;
					},
					Route::FILTER_OUT => static function ($params) {
						return array_merge($params, [
							'city' => $params['city']->getSlug(),
							'shop' => $params['shop']->getSlug(),
							'store' => $params['store'] ? $params['store']->getSlug() : null,
						]);
					},
				],
			]);

			$router->addRoute(
				'new/' . '<redirection .+>',
				[
					'presenter' => 'NewOferto:Redirection',
					'action' => 'redirection',
					'redirection' => [
						Route::FILTER_IN => static function ($url) use ($localization, $website, $redirectionRepository) {
							return $redirectionRepository->findOneBy(['localization' => $localization, 'website' => $website, 'oldUrl' => $url]);
						},
						Route::FILTER_OUT => static function ($url) {
							return $url->getOldUrl();
						},
					],
				]
			);

			$router->addRoute('new/' . '<document>', [
				'module' => 'NewOferto',
				'presenter' => 'Conditions',
				'action' => 'default',
				'document' => [
					Route::FILTER_IN => static function ($document) use ($documentFacade, $localization) {
						return $documentFacade->findBySlug($localization, $document);
					},
					Route::FILTER_OUT => static function (Document $document) {
						return $document->getSlug();
					},
				],
			]);

			$router->addRoute('new/' . '<presenter>/<action>', [
				'module' => 'NewOferto',
				'presenter' => 'Homepage',
				'action' => 'default',
			]);

			$router->addRoute($channelPath . '<presenter>/<action>', [
				'module' => 'Oferto',
				'presenter' => 'Homepage',
				'action' => 'default',
			]);
		}

		if ($website->isKaufinoSubdomain()) {
			$channelParams = 'k|l|m|n';
			$channelPath = '[<channel ' . $channelParams . '>/]';

			$router->addRoute($channelPath . $tokens['shops'], [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Shops',
				'action' => 'shops',
			]);

			$router->addRoute($channelPath . $tokens['leaflets'], [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Leaflets',
				'action' => 'leaflets',
			]);

			$router->addRoute($channelPath . $tokens['articles'], [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Articles',
				'action' => 'articles',
			]);

			$router->addRoute($channelPath . $tokens['search'] . '/<q>', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Search',
				'action' => 'search',
			]);

			$router->addRoute($channelPath . '<shop>', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Shop',
				'action' => 'shop',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . '<shop>/<leaflet>', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Leaflet',
				'action' => 'leaflet',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
				'leaflet' => [
					Route::FILTER_IN => static function ($leaflet) use ($leafletFacade, $localization) {
						return $leafletFacade->findLeafletBySlug($localization, $leaflet, false);
					},
					Route::FILTER_OUT => static function ($leaflet) {
						return $leaflet->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . $tokens['offers'], [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Offers',
				'action' => 'offers',
			]);

			$router->addRoute($channelPath . '<article>', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Article',
				'action' => 'article',
				'article' => [
					Route::FILTER_IN => static function ($article) use ($articleFacade, $website) {
						return $articleFacade->findArticleBySlug($website->getParentWebsite(), $article);
					},
					Route::FILTER_OUT => static function ($article) {
						return $article->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . '<tag>', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Tag',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_OFFERS, Tag::TYPE_SHOPS);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . $tokens['cities'], [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Cities',
				'action' => 'cities',
			]);

			$router->addRoute($channelPath . '<city>', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'City',
				'action' => 'city',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . '<city>/<shop>', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'City',
				'action' => 'shop',
				'city' => [
					Route::FILTER_IN => static function ($city) use ($geoFacade, $localization) {
						return $geoFacade->findCityBySlug($localization, $city);
					},
					Route::FILTER_OUT => static function ($city) {
						return $city->getSlug();
					},
				],
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . '<shop>/' . $tokens['archive'], [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Archive',
				'action' => 'archive',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . 'ajax/search[/<search>]', 'KaufinoSubdomain:Ajax:search');

			$router->addRoute($channelPath . '<city>/<shop>[/<store>]', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'City',
				'action' => 'store',
				null => [
					Route::FILTER_IN => static function ($params) use ($storeFacade, $localization, $geoFacade, $shopFacade) {
						$params['city'] = $geoFacade->findCityBySlug($localization, $params['city']);
						$params['shop'] = $shopFacade->findShopBySlug($localization, $params['shop']);

						if ($params['city'] === null || $params['shop'] === null) {
							return null;
						}

						$params['store'] = $storeFacade->findStoreBySlug($params['city'], $params['shop'], $params['store'], Website::MODULE_KAUFINO);

						return $params;
					},
					Route::FILTER_OUT => static function ($params) {
						return array_merge($params, [
							'city' => $params['city']->getSlug(),
							'shop' => $params['shop']->getSlug(),
							'store' => $params['store'] ? $params['store']->getSlug() : null,
						]);
					},
				],
			]);

			$router->addRoute($channelPath . $tokens['aboutUs'], 'KaufinoSubdomain:Static:aboutUs');
			$router->addRoute($channelPath . $tokens['cookies'], 'KaufinoSubdomain:Conditions:cookies');
			$router->addRoute($channelPath . $tokens['copy'], 'KaufinoSubdomain:Static:copy');
			$router->addRoute($channelPath . 'offer', 'KaufinoSubdomain:Static:offer');
			$router->addRoute($channelPath . 'sitemap.xml', 'KaufinoSubdomain:Sitemap:sitemap');
			$router->addRoute($channelPath . '<type>.xml', 'KaufinoSubdomain:Sitemap:type');
			//$router->addRoute('<other .+>', 'Oferto:Homepage:other');

			$router->addRoute(
				'<redirection .+>',
				[
					'presenter' => 'KaufinoSubdomain:Redirection',
					'action' => 'redirection',
					'redirection' => [
						Route::FILTER_IN => static function ($url) use ($localization, $website, $redirectionRepository) {
							return $redirectionRepository->findOneBy(['localization' => $localization, 'website' => $website, 'oldUrl' => $url]);
						},
						Route::FILTER_OUT => static function ($url) {
							return $url->getOldUrl();
						},
					],
				]
			);

			$router->addRoute($channelPath . '/podminky-souteze', 'KaufinoSubdomain:Static:contestConditions');

			$router->addRoute($channelPath . '<document>', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Conditions',
				'action' => 'default',
				'document' => [
					Route::FILTER_IN => static function ($document) use ($documentFacade, $localization) {
						return $documentFacade->findBySlug($localization, $document);
					},
					Route::FILTER_OUT => static function (Document $document) {
						return $document->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . '<presenter>/<action>', [
				'module' => 'KaufinoSubdomain',
				'presenter' => 'Homepage',
				'action' => 'default',
			]);
		}

		if ($website->isLetadoSubdomain()) {
			$channelPath = '[<channel k|l>/]';

			$router->addRoute($channelPath . $tokens['shops'], [
				'module' => 'LetadoSubdomain',
				'presenter' => 'Shops',
				'action' => 'shops',
			]);

			$router->addRoute($channelPath . $tokens['leaflets'], [
				'module' => 'LetadoSubdomain',
				'presenter' => 'Leaflets',
				'action' => 'leaflets',
			]);

			$router->addRoute($channelPath . $tokens['search'] . '/<q>', [
				'module' => 'LetadoSubdomain',
				'presenter' => 'Search',
				'action' => 'search',
			]);

			$router->addRoute($channelPath . '<shop>', [
				'module' => 'LetadoSubdomain',
				'presenter' => 'Shop',
				'action' => 'shop',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . '<shop>/<leaflet>', [
				'module' => 'LetadoSubdomain',
				'presenter' => 'Leaflet',
				'action' => 'leaflet',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
				'leaflet' => [
					Route::FILTER_IN => static function ($leaflet) use ($leafletFacade, $localization) {
						return $leafletFacade->findLeafletBySlug($localization, $leaflet, false);
					},
					Route::FILTER_OUT => static function ($leaflet) {
						return $leaflet->getSlug();
					},
				],
			]);


			$router->addRoute($channelPath . '<tag>', [
				'module' => 'LetadoSubdomain',
				'presenter' => 'Tag',
				'action' => 'tag',
				'tag' => [
					Route::FILTER_IN => static function ($tag) use ($tagFacade, $localization) {
						return $tagFacade->findTagBySlug($localization, $tag, Tag::TYPE_SHOPS, Tag::TYPE_OFFERS);
					},
					Route::FILTER_OUT => static function ($tag) {
						return $tag->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . '<shop>/' . $tokens['archive'], [
				'module' => 'LetadoSubdomain',
				'presenter' => 'Archive',
				'action' => 'archive',
				'shop' => [
					Route::FILTER_IN => static function ($shop) use ($shopFacade, $localization) {
						return $shopFacade->findShopBySlug($localization, $shop);
					},
					Route::FILTER_OUT => static function ($shop) {
						return $shop->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . $tokens['aboutUs'], 'LetadoSubdomain:Static:aboutUs');
			$router->addRoute($channelPath . $tokens['cookies'], 'LetadoSubdomain:Conditions:cookies');
			$router->addRoute($channelPath . 'sitemap.xml', 'LetadoSubdomain:Sitemap:sitemap');
			$router->addRoute($channelPath . '<type>.xml', 'LetadoSubdomain:Sitemap:type');
			$router->addRoute($channelPath . 'ajax/search[/<search>]', 'LetadoSubdomain:Ajax:search');

			$router->addRoute(
				$channelPath . '<redirection .+>',
				[
					'presenter' => 'LetadoSubdomain:Redirection',
					'action' => 'redirection',
					'redirection' => [
						Route::FILTER_IN => static function ($url) use ($localization, $website, $redirectionRepository) {
							return $redirectionRepository->findOneBy(['localization' => $localization, 'website' => $website, 'oldUrl' => $url]);
						},
						Route::FILTER_OUT => static function ($url) {
							return $url->getOldUrl();
						},
					],
				]
			);

			$router->addRoute($channelPath . '<document>', [
				'module' => 'LetadoSubdomain',
				'presenter' => 'Conditions',
				'action' => 'default',
				'document' => [
					Route::FILTER_IN => static function ($document) use ($documentFacade, $localization) {
						return $documentFacade->findBySlug($localization, $document);
					},
					Route::FILTER_OUT => static function (Document $document) {
						return $document->getSlug();
					},
				],
			]);

			$router->addRoute($channelPath . '<presenter>/<action>', 'LetadoSubdomain:Homepage:default');
		}

		return $router;
	}

	private function getKaufinoNewRoutes()
	{
	}

	public static function getLocalizedUrl(Website $website, ?string $path = null): ?string
	{
		$localization = $website->getLocalization();
		$tokens = self::getTokens($localization->getFullLocale());

		if ($path === null) {
			return $website->getDomain();
		}

		if ($path && isset($tokens[$path])) {
			return $website->getDomain() . $tokens[$path];
		}

		return null;
	}

	private static function getTokens(string $fullLocale): array
	{
		return Nette\Neon\Neon::decode(
			Nette\Utils\FileSystem::read(__DIR__ . '/../locale/router.' . $fullLocale . '.neon')
		);
	}
}

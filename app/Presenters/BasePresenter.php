<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Presenters;

use <PERSON><PERSON><PERSON>\Model\Configuration;
use <PERSON><PERSON><PERSON>\Model\Content\ContentFilter;
use <PERSON><PERSON><PERSON>\Model\Images\ImageFilter;
use <PERSON><PERSON><PERSON>\Model\Localization\DayGenitiveFilter;
use <PERSON><PERSON><PERSON>\Model\Localization\DayNominativeFilter;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Localization\FlagFilter;
use Ka<PERSON>ino\Model\Localization\GeoResolver;
use Kaufino\Model\Localization\LocalDateFilter;
use Ka<PERSON>ino\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\MonthNameFilter;
use <PERSON><PERSON><PERSON>\Model\Localization\PriceFilter;
use <PERSON><PERSON><PERSON>\Model\ResponseCacheManager;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use <PERSON><PERSON><PERSON>\Model\Websites\WebsiteFacade;
use K<PERSON>by\Autowired\AutowireProperties;
use Nette;
use Nette\Application\IResponse;
use Nette\Application\UI\ITemplate;
use Nette\Application\UI\Presenter;
use Nette\Utils\Strings;

/**
 * Base presenter for all application presenters.
 */
abstract class BasePresenter extends Nette\Application\UI\Presenter
{
	use AutowireProperties;

	/** @var Configuration @inject */
	public $configuration;

	/** @var FlagFilter @inject */
	public $flagFilter;

	/** @var ImageFilter @inject */
	public $imageFilter;

	/** @var ContentFilter @inject */
	public $contentFilter;

	/** @var LocalDateFilter @inject */
	public $localDateFilter;

	/** @var PriceFilter @inject */
	public $priceFilter;

	/** @var DayGenitiveFilter @inject */
	public $dayGenitiveFilter;

	/** @var DayNominativeFilter @inject */
	public $dayNominativeFilter;

	/** @var MonthNameFilter @inject */
	public $monthNameFilter;

	/** @var GeoResolver @inject */
	public $geoResolver;

	/** @var WebsiteFacade @inject */
	public $websiteFacade;

	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var Localization */
	public $localization;

	/** @var Website */
	public $website;

	/** @var bool */
	public $cachedResponse = false;

	/** @var bool */
	public $disableCachedResponse = false;

	/** @var ResponseCacheManager @inject */
	public $responseCacheManager;

	/** @var array */
	public $responseCacheTags = [];

	protected function startup()
	{
		parent::startup();

		$this->resolveUserCityId();

		$responseCacheKey = $this->getResponseCacheKey();

		if ($responseCacheKey !== null && $this->getHttpRequest()->getMethod() !== 'POST') {
			/** @var Nette\Application\Responses\TextResponse|null $response */
			$response = $this->responseCacheManager->loadResponseFromCache($responseCacheKey);

			if ($response !== null) {
				$this->cachedResponse = true;
				$this->sendResponse($response);
			}
		}

		$this->website = $this->websiteFacade->resolveCurrentWebsite();
		$this->localization = $this->website ? $this->website->getLocalization() : null;

		if ($this->localization === null) {
			$this->localization = $this->localizationFacade->getCurrentLocalization();
		}

		$this->template->configuration = $this->configuration;
		$this->template->geoCountry = $this->geoResolver->resolveGeoFromIP();
		$this->template->localization = $this->localization;
		$this->template->website = $this->website;
		$this->template->presenterName = $this->getName();
		$this->template->cachedResponse = $this->cachedResponse;
		$this->template->userCityId = $this->resolveUserCityId();
	}

	private function resolveUserCityId()
	{
		if ($cityId = $this->getHttpRequest()->getCookie('cityId')) {
			// return $cityId;
		}
	}

	protected function createTemplate(): ITemplate
	{
		/** @var Nette\Bridges\ApplicationLatte\Template $template */
		$template = parent::createTemplate();

		$template->addFilter('flag', $this->flagFilter);
		$template->addFilter('image', $this->imageFilter);
		$template->addFilter('content', $this->contentFilter);
		$template->addFilter('localDate', $this->localDateFilter);
		$template->addFilter('price', $this->priceFilter);
		$template->addFilter('dayGenitive', $this->dayGenitiveFilter);
		$template->addFilter('dayNominative', $this->dayNominativeFilter);
		$template->addFilter('monthName', $this->monthNameFilter);

		return $template;
	}

	public function onShutdown(Presenter $sender, IResponse $response)
	{
		$cacheKey = $this->getResponseCacheKey();

		if (
			$cacheKey !== null
			&& $this->cachedResponse === false
			&& $response instanceof Nette\Application\Responses\TextResponse
			&& Strings::startsWith($this->getName(), 'Admin:') === false
		) {
			$this->responseCacheManager->cacheResponse(
				$cacheKey,
				$response,
				array_merge($this->responseCacheTags, ['response'])
			);
		}
	}

	private function getResponseCacheKey(): ?string
	{
//		return null; // @todo zatim vypnuto dokud se nevyresi problem s velikosti + tagy
		if ($this->disableCachedResponse === true) {
			return null;
		}

		if ($_SERVER['SERVER_NAME'] === '127.0.0.1') {
			return null;
		}

		// API a sitemapu nechceme cachovat
		if (Strings::startsWith($this->getName(), 'Api:') === true || Strings::contains(Strings::lower($this->getName()), 'sitemap') === true) {
			return null;
		}

		// robots.txt a ads.txt
		if (
			Strings::contains($this->getHttpRequest()->getUrl()->getAbsoluteUrl(), 'robots.txt') === true
			|| Strings::contains($this->getHttpRequest()->getUrl()->getAbsoluteUrl(), 'ads.txt')
		) {
			return null;
		}

		// adminy nechceme cachovat
		if (Strings::startsWith($this->getName(), 'Admin:') === true || $this->getUser()->isLoggedIn() === true) {
			return null;
		}

		if (isset($_COOKIE['d2s0KZA1rp9pwsRI9n0l']) && $_COOKIE['d2s0KZA1rp9pwsRI9n0l'] === 'Rj1Z53FM17fL6nskc5NG') {
			return null; // v debugu nechceme cachovat
		}

		// @todo zatim pouze s debug cookie
//		if ($this->getHttpRequest()->getCookie('debug') !== '=2BpB}M[sK!d:SrP') {
//			return null;
//		}
//		if ($_SERVER['REMOTE_ADDR'] !== '94.113.187.180') {
//			return null;
//		}

		// URL
		$cacheKey = [
			$this->getHttpRequest()->getUrl()->getAbsoluteUrl(),
		];

		// Google Optimize
		foreach ($this->getHttpRequest()->getCookies() as $cookieName => $cookieValue) {
			if (Strings::startsWith($cookieName, 'go-')) {
				$cacheKey[] = $cookieName . '-' . $cookieValue;
			}
		}

		if (isset($_COOKIE['newcdn'])) {
			$cacheKey[] = 'newcdn';
		}

		return sha1(implode('.', $cacheKey));
	}
}

{import 'components/form.latte'}

<!DOCTYPE html>
<html lang="{$localization->getLocale()}">
<head>
	<meta charset="utf-8">
	{capture $headTitle|spaceless}
		{ifset title}{include title|stripHtml}{/ifset}
	{/capture}
	<title>{if strlen($headTitle) > 55}{$headTitle}{else}{$headTitle} | {$websiteName}{/if}</title>
	<meta name="keywords" content="">
	<meta name="description" content="{ifset description}{include description|stripHtml|spaceless}{/ifset}">
	<meta name="author" content="{$websiteName}">
	{if $isOferito || $isMrOfferto}
		<meta name="robots" content="noindex,nofollow">
	{else}
		<meta name="robots" content="{block #robots|stripHtml|trim}index,follow{/block}">
	{/if}
	<link rel="canonical" href="{$canonicalUrl}">

	<meta property="og:title" content="{ifset title}{include title|stripHtml} | {/ifset}{$websiteName}" />
	<meta property="og:site_name" content="{$websiteName}"/>
	<meta property="og:url" content="{link //this}" />
	<meta property="og:description" content="{ifset description}{include description|stripHtml}{/ifset}" />
	<meta property="og:type" content="website" />
	<meta property="og:image" content="{$basePath}/images/1200x627_0G_letado.png" />
	<meta property="fb:app_id" content="" />

	<meta name="twitter:card" content="summary" />

	<!-- Viewport for mobile devices -->
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=1">

	<link rel="apple-touch-icon" sizes="180x180" href="{$basePath}/images/favicon/letado/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="{$basePath}/images/favicon/letado/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="{$basePath}/images/favicon/letado/favicon-16x16.png">
	<link rel="icon" type="image/png" sizes="192x192"  href="{$basePath}/images/favicon/letado/android-chrome-192x192.png">
	<link rel="icon" type="image/png" sizes="512x512"  href="{$basePath}/images/favicon/letado/android-chrome-512x512.png">
	{*	<link rel="manifest" href="{$basePath}/images/favicon/letado/site.webmanifest">*}
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#ffffff">

	<meta name="google-site-verification" content="7ypcHzpCB0upyEy8VKX_9akp-hXM0zMSokKA2MMVQtk" />

	{var $version = 0.27}
	<link rel="stylesheet" href="{$basePath}/css/main.letado.css?v={$version}">

	{block head}{/block}

	{if $isMrOfferto && $channel === 'b2'}
		<!--Google GPT/ADM code -->
		<script type="text/javascript" async="async" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
		<script type="text/javascript">
			window.googletag = window.googletag || { cmd: [] };
			window.googletag.cmd.push(function () {
				window.googletag.pubads().enableSingleRequest();
			});
		</script>

		<!--Site config -->
		<script type="text/javascript" async="async" src="https://protagcdn.com/s/{$domain |noescape}/site.js"></script>
		<script type="text/javascript">
			window.protag = window.protag || { cmd: [] };
			window.protag.config = { s: {$domain}, childADM: '23037269705', l: 'FbM3ys2m' };
			window.protag.cmd.push(function () {
				window.protag.pageInit();
			});
		</script>
	{/if}

	{if $isOferito}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
						new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
					j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
					'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
			})(window,document,'script','dataLayer','GTM-NM7H45CC');</script>
		<!-- End Google Tag Manager -->
	{elseif $isMrOfferto}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
						new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
					j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
					'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
			})(window,document,'script','dataLayer','GTM-NJFPQXGF');</script>
		<!-- End Google Tag Manager -->
	{else}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
						new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
					j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
					'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
			})(window,document,'script','dataLayer','GTM-5WQ2TTX');</script>
		<!-- End Google Tag Manager -->
	{/if}

	<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4233432057183172" crossorigin="anonymous"></script>
</head>

<body>
{if $isOferito}
	<!-- Google Tag Manager (noscript) -->
	<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NM7H45CC"
					  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
	<!-- End Google Tag Manager (noscript) -->
{elseif $isMrOfferto}
	<!-- Google Tag Manager (noscript) -->
	<noscript><iframe src=“https://www.googletagmanager.com/ns.html?id=GTM-NJFPQXGF"
					  height=“0" width=“0" style="display:none;visibility:hidden"></iframe></noscript>
	<!-- End Google Tag Manager (noscript) -->
{else}
	<!-- Google Tag Manager (noscript) -->
	<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5WQ2TTX"
					  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
	<!-- End Google Tag Manager (noscript) -->
{/if}

<div class="k-header">
	<div class="container container--flex">
		<a n:href="Homepage:default" class="k-header__logo-link">
			{if $isOferito}
				Oferito
			{elseif $isMrOfferto}
				MrOfferto
			{else}
				<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 367.09 99.0463" n:syntax="off">
					<defs>
						<style>.a{isolation:isolate;}.b{fill:url(#a);}.c{fill:url(#b);}.d{fill:url(#c);}.e{fill:url(#d);}.f{fill:#231f20;mix-blend-mode:multiply;opacity:0.2;}</style>
						<linearGradient id="a" x1="105.4882" y1="38.6096" x2="11.822" y2="120.9042" gradientTransform="matrix(0.9996, 0.0282, -0.0282, 0.9996, -9.5274, -6.9833)" gradientUnits="userSpaceOnUse">
							<stop offset="0" stop-color="#537bbd"/>
							<stop offset="0.0113" stop-color="#5380ba"/>
							<stop offset="0.0757" stop-color="#519ba9"/>
							<stop offset="0.1414" stop-color="#50ae9d"/>
							<stop offset="0.209" stop-color="#4fb996"/>
							<stop offset="0.2809" stop-color="#4fbd94"/>
							<stop offset="0.6685" stop-color="#4dc8ed"/>
							<stop offset="0.8876" stop-color="#45c6e6"/>
							<stop offset="1" stop-color="#6ac49c"/>
						</linearGradient>
						<linearGradient id="b" x1="98.8313" y1="31.0328" x2="5.1655" y2="113.3271" xlink:href="#a"/>
						<linearGradient id="c" x1="95.5723" y1="27.3233" x2="1.9061" y2="109.618" xlink:href="#a"/>
						<linearGradient id="d" x1="96.1256" y1="27.9531" x2="2.4581" y2="110.249" gradientTransform="matrix(0.9996, 0.0282, -0.0282, 0.9996, -9.5274, -6.9833)" gradientUnits="userSpaceOnUse">
							<stop offset="0" stop-color="#39b54a"/>
							<stop offset="0.0255" stop-color="#3db657"/>
							<stop offset="0.0865" stop-color="#45b972"/>
							<stop offset="0.1488" stop-color="#4bbb85"/>
							<stop offset="0.2127" stop-color="#4ebd90"/>
							<stop offset="0.2809" stop-color="#4fbd94"/>
							<stop offset="0.6685" stop-color="#4dc8ed"/>
							<stop offset="0.8876" stop-color="#45c6e6"/>
							<stop offset="1" stop-color="#6ac49c"/>
						</linearGradient>
					</defs>
					<g class="a">
						<path d="M141.2538,73.6666H120.8053a.0539.0539,0,0,1-.0538-.0538l.0087-40.01a.054.054,0,0,0-.0538-.0539H109.6638l.0012,50.306a.055.055,0,0,0,.0548.0549h31.5984l-.01-10.188A.055.055,0,0,0,141.2538,73.6666Z" transform="translate(-10.7205 -4.8999)"/>
						<path d="M181.7321,73.6666H157.4413a.0539.0539,0,0,1-.0537-.0538l-.01-10.1893h21.837l-.01-10.189a.0541.0541,0,0,0-.0538-.0538H157.422a.0538.0538,0,0,1-.0537-.0539l.0381-9.3355h23.1174l-.01-10.188a.0551.0551,0,0,0-.0549-.0548H146.3l.0012,50.306a.055.055,0,0,0,.0548.0549h35.44l-.01-10.189A.054.054,0,0,0,181.7321,73.6666Z" transform="translate(-10.7205 -4.8999)"/>
						<path d="M224.3341,33.6024a.054.054,0,0,0-.0538-.0539H184.501l.0211,9.7623a.0541.0541,0,0,0,.054.0538h14.26a.0551.0551,0,0,1,.0549.0549l-.02,40.436a.0539.0539,0,0,0,.0537.0539h11.0422l.02-40.5448h14.3682Z" transform="translate(-10.7205 -4.8999)"/>
						<path d="M247.557,33.5485h-9.1344l-21.734,50.288a.0522.0522,0,0,0,.0479.0729h12.1554l4.1987-10.67H252.61a.0628.0628,0,0,1,.058.039l4.3077,10.5977a.0531.0531,0,0,0,.0491.0331h12.4668a.0109.0109,0,0,0,.01-.0151L247.6148,33.5864A.0629.0629,0,0,0,247.557,33.5485ZM248.8464,63.85H236.62a.0525.0525,0,0,1-.0489-.0718l5.7494-14.6674a.3949.3949,0,0,1,.7323.0435l5.795,14.6936Z" transform="translate(-10.7205 -4.8999)"/>
						<path d="M310.6081,39.2743a26.6371,26.6371,0,0,0-9.503-4.3745,47.2615,47.2615,0,0,0-11.4638-1.3513H272.9966l.0016,50.306a.055.055,0,0,0,.0548.0549h18.0833a35.2579,35.2579,0,0,0,10.7611-1.6363,27.289,27.289,0,0,0,9.0381-4.8011,23.1337,23.1337,0,0,0,6.1965-7.8245,24.0375,24.0375,0,0,0,2.2986-10.7765,27.0772,27.0772,0,0,0-2.3887-11.7364A21.0092,21.0092,0,0,0,310.6081,39.2743ZM306.49,65.5222a12.29,12.29,0,0,1-3.7788,4.7653,15.2327,15.2327,0,0,1-5.7195,2.5966,30.9294,30.9294,0,0,1-7.1452.7825H284.14a.0548.0548,0,0,1-.0548-.0549l.0183-29.82h6.544a24.3091,24.3091,0,0,1,6.6328.8891,16.7931,16.7931,0,0,1,5.53,2.6675,12.3216,12.3216,0,0,1,3.7262,4.5166,14.6554,14.6554,0,0,1,1.3627,6.4374A17.5734,17.5734,0,0,1,306.49,65.5222Z" transform="translate(-10.7205 -4.8999)"/>
						<path d="M375.78,47.775a24.261,24.261,0,0,0-5.6495-8.3582,24.8367,24.8367,0,0,0-8.5658-5.3,30.8243,30.8243,0,0,0-10.8569-1.8492,31.2505,31.2505,0,0,0-10.9093,1.8492A24.007,24.007,0,0,0,325.5263,47.775a28.21,28.21,0,0,0-2.06,10.9536,28.1939,28.1939,0,0,0,2.03,10.9543,24.23,24.23,0,0,0,5.65,8.3582,24.72,24.72,0,0,0,8.6015,5.2995,32.95,32.95,0,0,0,21.7663,0,25.0886,25.0886,0,0,0,8.6123-5.2995,23.809,23.809,0,0,0,5.6595-8.3582,28.6729,28.6729,0,0,0,2.0249-10.9543A28.2192,28.2192,0,0,0,375.78,47.775ZM365.17,65.2376a15.2221,15.2221,0,0,1-3.16,5.1212,14.2158,14.2158,0,0,1-4.9332,3.3787,17.7416,17.7416,0,0,1-12.8749,0,14.2842,14.2842,0,0,1-4.9181-3.3787,15.3038,15.3038,0,0,1-3.17-5.1212,18.14,18.14,0,0,1-1.1235-6.509,18.5326,18.5326,0,0,1,1.1222-6.4725,15.1179,15.1179,0,0,1,3.1549-5.157,14.25,14.25,0,0,1,4.9341-3.3791,17.7413,17.7413,0,0,1,12.8748,0,14.45,14.45,0,0,1,8.1291,8.5361,18.5669,18.5669,0,0,1,1.0827,6.4725A18.2716,18.2716,0,0,1,365.17,65.2376Z" transform="translate(-10.7205 -4.8999)"/>
						<polygon class="b" points="39.443 90.461 13.767 70.151 0 76.523 42.23 99.046 85.665 78.944 85.046 78.613 39.443 90.461"/>
						<polygon class="c" points="38.635 72.5 21.72 44.471 6.592 45.606 38.232 81.517 85.959 77.935 85.496 77.409 38.635 72.5"/>
						<polygon class="d" points="44.181 55.398 38.178 23.215 23.613 18.969 40.64 63.7 86.588 77.095 86.339 76.439 44.181 55.398"/>
						<polygon class="e" points="87.226 28.667 48.9 0 49.146 47.861 87.473 76.528 87.226 28.667"/>
						<path class="f" d="M59.6205,4.9s9.8936,10.466,18.2868,37.3115C86.41,69.408,98.1931,81.428,98.1931,81.428L59.8669,52.7607Z" transform="translate(-10.7205 -4.8999)"/>
					</g>
				</svg>
			{/if}
		</a>

		<form class="k-header__search">
			<input type="text" class="k-header__search-input js-search-input" data-search-url="{link Ajax:search}" placeholder="{_"$websiteType.navbar.search.placeholder"}">
			<input type="submit" class="k-header__search-submit js-search-submit" data-search-url="{link Search:search, q => 'q'}" value="{_"$websiteType.navbar.search.submit"}">

			<div class="k-header__search-wrapper">
			</div>
		</form>

		<div class="k-header__nav">
			<a href="{link Shops:shops}" class="k-header__nav-item">{_"$websiteType.navbar.shops"}</a>
			<a href="{link Leaflets:leaflets}" class="k-header__nav-item">{_"$websiteType.navbar.leaflets"}</a>

			<span class="k-header__nav-separator">|</span>

			{foreach $headerShops() as $headerShop}
				<a n:if="$iterator->counter < 4" n:href="Shop:shop $headerShop" class="k-header__nav-item color-grey">{$headerShop->getName()}</a>

				{if $iterator->counter == 3}
					<div class="k-header__nav-dropdown-wrapper">
					<a href="{link Shops:shops}" class="k-header__nav-item k-header__nav-item--more">{_"$websiteType.navbar.moreShops"} »</a>

					<div class="k-header__nav-dropdown">
				{/if}
				<a n:if="$iterator->counter > 3" n:href="Shop:shop $headerShop" class="k-header__nav-dropdown-item">{$headerShop->getName()}</a>
				{if $iterator->last}
					</div>
					</div>
				{/if}
			{/foreach}
		</div>

		<button class="k-header__menu-icon">
			<svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bars" class="svg-inline--fa fa-bars fa-w-14" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"></path></svg>
		</button>
	</div>
</div>

<div n:foreach="$flashes as $flash" n:class="alert, 'alert-' . $flash->type">{$flash->message}</div>

{include content}

{if in_array($presenterName, ['Letado:Shop', 'Letado:Tag', 'Letado:City']) && isset($userLoggedIn)}
	<div class="k-page-extension">
		<span n:class="$pageExtension && $pageExtension->getTitle() ? k-page-extension__tag--green ,k-page-extension__tag">MT</span>
		<span n:class="$pageExtension && $pageExtension->getDescription() ? k-page-extension__tag--green ,k-page-extension__tag">MD</span>
		<span n:class="$pageExtension && $pageExtension->getHeading() ? k-page-extension__tag--green ,k-page-extension__tag">H1</span>
		<span n:class="$pageExtension && $pageExtension->getKeywords() ? k-page-extension__tag--green ,k-page-extension__tag">KW</span>

		{if $presenterName == 'Letado:Shop'}
			{var $shopAlternativeNames = $shop->getAlternativeNames()}

			<span n:class="$shopAlternativeNames && count($shopAlternativeNames|explode) ? k-page-extension__tag--green ,k-page-extension__tag">AN</span>
			<a n:href=":Admin:Shop:shop $shop->getId()" class="k-page-extension__btn" target="_blank">Edit shop</a>
		{/if}

		{if $presenterName == 'Letado:Tag'}
			<a n:href=":Admin:Tag:tag $tag->getId()" class="k-page-extension__btn" target="_blank">Edit tag</a>
		{/if}

		{if $pageExtension}
			<a n:href=":Admin:Seo:pageExtension $pageExtension->getId()" class="k-page-extension__btn" target="_blank">Edit page extension</a>

		{var $shopKeywords = $pageExtension->getKeywords()}
			<span class="k-alternative-name js-alternative-name" data-alternative-name="{$shopKeywords}"></span>
		{else}
			<a n:href=":Admin:Seo:pageExtension id => null, websiteId => $website->getId(), slug => $pageExtensionSlug" class="k-page-extension__btn" target="_blank">Edit page extension</a>
		{/if}

		<a n:href=":Admin:Translations:Dictionary:dictionary dictionary => 'letado', localizationId => $localization->getId()" class="k-page-extension__btn" target="_blank">Translations</a>
	</div>
{/if}

<footer class="k-footer mt-5">
	<div class="container">
		<div class="k-footer__wrapper">
			<div class="k-footer__column k-footer__column--first">
				<a n:href="Homepage:default" class="d-block mb-4">
					{if $isOferito}
						Oferito
					{elseif $isMrOfferto}
						MrOfferto
					{else}
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 367.09 99.0463" n:syntax="off">
							<defs>
								<style>.a{isolation:isolate;}.b{fill:url(#a);}.c{fill:url(#b);}.d{fill:url(#c);}.e{fill:url(#d);}.f{fill:#231f20;mix-blend-mode:multiply;opacity:0.2;}</style>
								<linearGradient id="a" x1="105.4882" y1="38.6096" x2="11.822" y2="120.9042" gradientTransform="matrix(0.9996, 0.0282, -0.0282, 0.9996, -9.5274, -6.9833)" gradientUnits="userSpaceOnUse">
									<stop offset="0" stop-color="#537bbd"/>
									<stop offset="0.0113" stop-color="#5380ba"/>
									<stop offset="0.0757" stop-color="#519ba9"/>
									<stop offset="0.1414" stop-color="#50ae9d"/>
									<stop offset="0.209" stop-color="#4fb996"/>
									<stop offset="0.2809" stop-color="#4fbd94"/>
									<stop offset="0.6685" stop-color="#4dc8ed"/>
									<stop offset="0.8876" stop-color="#45c6e6"/>
									<stop offset="1" stop-color="#6ac49c"/>
								</linearGradient>
								<linearGradient id="b" x1="98.8313" y1="31.0328" x2="5.1655" y2="113.3271" xlink:href="#a"/>
								<linearGradient id="c" x1="95.5723" y1="27.3233" x2="1.9061" y2="109.618" xlink:href="#a"/>
								<linearGradient id="d" x1="96.1256" y1="27.9531" x2="2.4581" y2="110.249" gradientTransform="matrix(0.9996, 0.0282, -0.0282, 0.9996, -9.5274, -6.9833)" gradientUnits="userSpaceOnUse">
									<stop offset="0" stop-color="#39b54a"/>
									<stop offset="0.0255" stop-color="#3db657"/>
									<stop offset="0.0865" stop-color="#45b972"/>
									<stop offset="0.1488" stop-color="#4bbb85"/>
									<stop offset="0.2127" stop-color="#4ebd90"/>
									<stop offset="0.2809" stop-color="#4fbd94"/>
									<stop offset="0.6685" stop-color="#4dc8ed"/>
									<stop offset="0.8876" stop-color="#45c6e6"/>
									<stop offset="1" stop-color="#6ac49c"/>
								</linearGradient>
							</defs>
							<g class="a">
								<path d="M141.2538,73.6666H120.8053a.0539.0539,0,0,1-.0538-.0538l.0087-40.01a.054.054,0,0,0-.0538-.0539H109.6638l.0012,50.306a.055.055,0,0,0,.0548.0549h31.5984l-.01-10.188A.055.055,0,0,0,141.2538,73.6666Z" transform="translate(-10.7205 -4.8999)"/>
								<path d="M181.7321,73.6666H157.4413a.0539.0539,0,0,1-.0537-.0538l-.01-10.1893h21.837l-.01-10.189a.0541.0541,0,0,0-.0538-.0538H157.422a.0538.0538,0,0,1-.0537-.0539l.0381-9.3355h23.1174l-.01-10.188a.0551.0551,0,0,0-.0549-.0548H146.3l.0012,50.306a.055.055,0,0,0,.0548.0549h35.44l-.01-10.189A.054.054,0,0,0,181.7321,73.6666Z" transform="translate(-10.7205 -4.8999)"/>
								<path d="M224.3341,33.6024a.054.054,0,0,0-.0538-.0539H184.501l.0211,9.7623a.0541.0541,0,0,0,.054.0538h14.26a.0551.0551,0,0,1,.0549.0549l-.02,40.436a.0539.0539,0,0,0,.0537.0539h11.0422l.02-40.5448h14.3682Z" transform="translate(-10.7205 -4.8999)"/>
								<path d="M247.557,33.5485h-9.1344l-21.734,50.288a.0522.0522,0,0,0,.0479.0729h12.1554l4.1987-10.67H252.61a.0628.0628,0,0,1,.058.039l4.3077,10.5977a.0531.0531,0,0,0,.0491.0331h12.4668a.0109.0109,0,0,0,.01-.0151L247.6148,33.5864A.0629.0629,0,0,0,247.557,33.5485ZM248.8464,63.85H236.62a.0525.0525,0,0,1-.0489-.0718l5.7494-14.6674a.3949.3949,0,0,1,.7323.0435l5.795,14.6936Z" transform="translate(-10.7205 -4.8999)"/>
								<path d="M310.6081,39.2743a26.6371,26.6371,0,0,0-9.503-4.3745,47.2615,47.2615,0,0,0-11.4638-1.3513H272.9966l.0016,50.306a.055.055,0,0,0,.0548.0549h18.0833a35.2579,35.2579,0,0,0,10.7611-1.6363,27.289,27.289,0,0,0,9.0381-4.8011,23.1337,23.1337,0,0,0,6.1965-7.8245,24.0375,24.0375,0,0,0,2.2986-10.7765,27.0772,27.0772,0,0,0-2.3887-11.7364A21.0092,21.0092,0,0,0,310.6081,39.2743ZM306.49,65.5222a12.29,12.29,0,0,1-3.7788,4.7653,15.2327,15.2327,0,0,1-5.7195,2.5966,30.9294,30.9294,0,0,1-7.1452.7825H284.14a.0548.0548,0,0,1-.0548-.0549l.0183-29.82h6.544a24.3091,24.3091,0,0,1,6.6328.8891,16.7931,16.7931,0,0,1,5.53,2.6675,12.3216,12.3216,0,0,1,3.7262,4.5166,14.6554,14.6554,0,0,1,1.3627,6.4374A17.5734,17.5734,0,0,1,306.49,65.5222Z" transform="translate(-10.7205 -4.8999)"/>
								<path d="M375.78,47.775a24.261,24.261,0,0,0-5.6495-8.3582,24.8367,24.8367,0,0,0-8.5658-5.3,30.8243,30.8243,0,0,0-10.8569-1.8492,31.2505,31.2505,0,0,0-10.9093,1.8492A24.007,24.007,0,0,0,325.5263,47.775a28.21,28.21,0,0,0-2.06,10.9536,28.1939,28.1939,0,0,0,2.03,10.9543,24.23,24.23,0,0,0,5.65,8.3582,24.72,24.72,0,0,0,8.6015,5.2995,32.95,32.95,0,0,0,21.7663,0,25.0886,25.0886,0,0,0,8.6123-5.2995,23.809,23.809,0,0,0,5.6595-8.3582,28.6729,28.6729,0,0,0,2.0249-10.9543A28.2192,28.2192,0,0,0,375.78,47.775ZM365.17,65.2376a15.2221,15.2221,0,0,1-3.16,5.1212,14.2158,14.2158,0,0,1-4.9332,3.3787,17.7416,17.7416,0,0,1-12.8749,0,14.2842,14.2842,0,0,1-4.9181-3.3787,15.3038,15.3038,0,0,1-3.17-5.1212,18.14,18.14,0,0,1-1.1235-6.509,18.5326,18.5326,0,0,1,1.1222-6.4725,15.1179,15.1179,0,0,1,3.1549-5.157,14.25,14.25,0,0,1,4.9341-3.3791,17.7413,17.7413,0,0,1,12.8748,0,14.45,14.45,0,0,1,8.1291,8.5361,18.5669,18.5669,0,0,1,1.0827,6.4725A18.2716,18.2716,0,0,1,365.17,65.2376Z" transform="translate(-10.7205 -4.8999)"/>
								<polygon class="b" points="39.443 90.461 13.767 70.151 0 76.523 42.23 99.046 85.665 78.944 85.046 78.613 39.443 90.461"/>
								<polygon class="c" points="38.635 72.5 21.72 44.471 6.592 45.606 38.232 81.517 85.959 77.935 85.496 77.409 38.635 72.5"/>
								<polygon class="d" points="44.181 55.398 38.178 23.215 23.613 18.969 40.64 63.7 86.588 77.095 86.339 76.439 44.181 55.398"/>
								<polygon class="e" points="87.226 28.667 48.9 0 49.146 47.861 87.473 76.528 87.226 28.667"/>
								<path class="f" d="M59.6205,4.9s9.8936,10.466,18.2868,37.3115C86.41,69.408,98.1931,81.428,98.1931,81.428L59.8669,52.7607Z" transform="translate(-10.7205 -4.8999)"/>
							</g>
						</svg>
					{/if}
				</a>
				<p class="fz-s color-grey mb-2">Copyright © {date('Y')} {_"$websiteType.footer.copyright"} ({$geoCountry})</p>

			</div>
			<div class="k-footer__column">
				<div class="k-footer__wrapper">
					<div class="k-footer__column">
						<p>
							<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_"$websiteType.footer.shops"}:</strong>
							<a n:foreach="$footerShops() as $footerShop" n:href="Shop:shop $footerShop" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$footerShop->getName()}</a>
						</p>
					</div>

					<div class="k-footer__column">
						<p>
							<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_"$websiteType.footer.category"}:</strong>
							<a n:foreach="$footerShopsTags() as $footerTag" n:href="Tag:tag $footerTag" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$footerTag->getName()}</a>
						</p>
					</div>

					<div class="k-footer__column">
						<p>
							<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_"$websiteType.footer.aboutLetado"}:</strong>
							<a n:href="Leaflets:leaflets" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.leaflets"}</a>
							<a n:href="Shops:shops" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.shops"}</a>
							<a n:href="Static:aboutUs" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.aboutUs"}</a>
							{if $conditions}
								<a n:href="Conditions:default, $conditions" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$conditions->getName()}</a>
							{else}
								<a n:href="Static:cookies" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.cookies"}</a>
							{/if}
						</p>
					</div>

					<div class="k-footer__column">
						<p n:if="$isOferito === false">
							<strong class="d-block fz-m color-grey tt-uppercase mb-4">{_"$websiteType.footer.nextCountries"}:</strong>
							{foreach $footerWebsites() as $footerWebsite}
								{continueIf $footerWebsite === $website}

								<a href="{$footerWebsite->getDomain()}" class="d-block fz-m color-black td-none td-hover-underline mb-4">
									{$footerWebsite->getLocalization()->getOriginalName()}
								</a>
							{/foreach}
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</footer>

{block scripts}
	<script src="{$basePath}/js/lazysizes/lazysizes.min.js" async></script>
	<script src="{$basePath}/js/main.js?v={$version}" async></script>

	{if $websiteType === 'letado' && $channel === null && ($localization->isCzech() || $localization->isGermany() || $localization->isFrancian() || $localization->isSpaian() || $localization->isUnitedStatesAmerican())}
		<script type = "text/javascript" >
			var nadzLang = document.documentElement.lang.toLowerCase(),
					oParams = {
						cs: {
							ids: "5141582af54dd09b"
						},
						de: {
							ids: "eb5d478e1fdc0fb3"
						},
						es: {
							ids: "4ae3c8bea4e43b20"
						},
						fr: {
							ids: "8b0ee3cea70c15f2"
						},
						gb: {
							ids: "1b3fac37f095c0c5"
						},
						en: {
							ids: "1b3fac37f095c0c5"
						}
					};
			var chosenIds = oParams[nadzLang] ? oParams[nadzLang].ids : oParams.en.ids;
			window._nAdzq = window._nAdzq || [];
			(function() {
				window._nAdzq.push(["setIds", chosenIds]);
				var e = "https://notifpush.com/scripts/";
				var t = document.createElement("script"); t.type = "text/javascript"; t.defer = true; t.async = true; t.src = e + "nadz-sdk.js";
				var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(t, s)
			})();
		</script>
	{/if}
{/block}
</body>
</html>
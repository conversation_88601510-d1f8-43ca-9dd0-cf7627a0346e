<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\LetadoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class LeafletsPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	public function actionLeaflets(): void
	{
		$this->responseCacheTags[] = 'leaflets';

		$this->template->leaflets = $this->leafletFacade->findLeaflets($this->localization, true, 100, Website::MODULE_LETADO);
	}
}

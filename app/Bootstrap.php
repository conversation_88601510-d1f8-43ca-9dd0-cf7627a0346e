<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>;

use Nette\Configurator;
use Nette\Utils\Strings;
use Tester\Environment;

class Bootstrap
{
	public static function boot(): Configurator
	{
		$configurator = new Configurator();

//		$configurator->setDebugMode(['************']);
		$debugIps = [];
		if (isset($_COOKIE['d2s0KZA1rp9pwsRI9n0l']) && $_COOKIE['d2s0KZA1rp9pwsRI9n0l'] === 'Rj1Z53FM17fL6nskc5NG') {
			$debugIps[] = $_SERVER['REMOTE_ADDR'];
		}

		if (isset($_SERVER['REMOTE_ADDR']) && getenv('NETTE_DEBUG') && getenv('NETTE_DEBUG') === 'dd72daf3c3654258475a34a21') {
			$debugIps[] = $_SERVER['REMOTE_ADDR'];
		}

		if (isset($_SERVER['REMOTE_ADDR']) && ($_SERVER['REMOTE_ADDR'] === '**************' || $_SERVER['REMOTE_ADDR'] === '**************')) {
			$debugIps[] = $_SERVER['REMOTE_ADDR'];
		}

		file_exists(__DIR__ . '/../sessions') or mkdir(__DIR__ . '/../sessions');

		if (Strings::contains($_SERVER['DOCUMENT_ROOT'], 'buddy/')) {
			$tempDir = __DIR__ . '/../temp';
		} else {
			$tempDir = __DIR__ . '/../temp_local';
		}

		$configurator->setDebugMode($debugIps);
//        $configurator->setDebugMode(true); // 36
		$configurator->enableTracy(__DIR__ . '/../log');

		\Tracy\Debugger::$strictMode = E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED;
		error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);

		$configurator->setTimeZone('Europe/Prague');
		$configurator->setTempDirectory($tempDir);

		$configurator->createRobotLoader()
			->addDirectory(__DIR__)
			->register();

		$configurator
			->addConfig(__DIR__ . '/config/common.neon')
			->addConfig(__DIR__ . '/config/local.neon')
		;

		return $configurator;
	}

	public static function bootForTests(): Configurator
	{
		$configurator = self::boot();
		if (class_exists('Tester\Environment')) {
			Environment::setup();
		}

		return $configurator;
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Products;

use Ka<PERSON>ino\Model\Offers\Entities\Offer;
use <PERSON><PERSON>ino\Model\Offers\Repositories\OfferRepository;
use Ka<PERSON>ino\Model\Products\Repositories\ProductRepository;
use Tracy\Debugger;

class ProductAutocomplete
{
	/**
	 * @var ProductRepository
	 */
	private $productRepository;

	public function __construct(ProductRepository $productRepository)
	{
		$this->productRepository = $productRepository;
	}

	public function resolveNameFromOcr(Offer $offer)
	{
		$tokens = $this->tokenizeString($offer->getOcrOutput());

		//Debugger::dump($tokens);

		$ngrams = [];
		for ($i = 1; $i <= count($tokens); $i++) {
			$ngrams = array_merge($ngrams, $this->createNgrams($tokens, $i));
		}

		$ngrams = array_filter($ngrams, static function ($item) {
			return ctype_upper($item[0]);
		});

		$candidateOffers = $this->productRepository->findOffersByNames($ngrams, $offer->getLocalization())->getResult();

		//Debugger::dump($ngrams);
		//Debugger::dump($candidateOffers);

		$candidateNames = [];
		foreach ($candidateOffers as $candidateOffer) {
			$candidateNames[] = $candidateOffer->getName();
		}

		usort($candidateNames, static function ($a, $b) {
			return strlen($b) <=> strlen($a);
		});

		return $candidateNames;
	}

	private function tokenizeString(string $input)
	{
		$tokens = explode(' ', $input);
		return array_filter($tokens);
	}

	private function createNgrams(array $tokens, int $n)
	{
		$countOfTokens = count($tokens);
		$ngrams = [];
		foreach ($tokens as $key => $token) {
			if ($key + $n <= $countOfTokens) {
				$ngram = [];
				for ($i = 0; $i < $n; $i++) {
					$ngram[] = $tokens[$key + $i];
				}

				$ngrams[] = implode(' ', $ngram);
			}
		}

		return $ngrams;
	}
}

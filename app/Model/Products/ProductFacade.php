<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ino\Model\Products;

use Doctrine\ORM\QueryBuilder;
use Ka<PERSON>ino\Model\EntityManager;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Offers\Entities\Offer;
use Kaufino\Model\Products\Repositories\ProductRepository;
use Kaufino\Model\Products\Entities\Product;
use Nette\Utils\Image;
use Nette\Utils\Strings;
use Tracy\Debugger;

class ProductFacade
{
	/** @var EntityManager */
	private $em;

	/** @var ProductManager */
	private $productManager;

	/** @var ProductRepository */
	private $productRepository;

	public function __construct(EntityManager $em, ProductManager $productManager, ProductRepository $productRepository)
	{
		$this->em = $em;
		$this->productRepository = $productRepository;
		$this->productManager = $productManager;
	}

	public function createProduct(Localization $localization, string $name, string $slug): Product
	{
		return $this->productManager->createProduct($localization, $name, $slug);
	}

	public function saveProduct(Product $product)
	{
		return $this->productManager->saveProduct($product);
	}

	public function getProducts(Localization $localization = null): QueryBuilder
	{
		return $this->productRepository->getProducts($localization);
	}

	public function findProduct($id): ?Product
	{
		/** @var ?Product $product */
		$product = $this->productRepository->find($id);

		return $product;
	}

	public function findProductBySlug(Localization $localization, string $slug): ?Product
	{
		return $this->productRepository->findProductBySlug($localization, $slug);
	}

	public function findProductByName(Localization $localization, string $name): ?Product
	{
		return $this->productRepository->findProductByName($localization, $name);
	}

	public function findProducts(Localization $localization, int $limit = null): array
	{
		return $this->productRepository->findBy(['localization' => $localization], ['id' => 'DESC'], $limit);
	}

	public function saveProductImage(Product $product, Image $image, $imageType)
	{
		return $this->productManager->saveProductImage($product, $image, $imageType);
	}

	public function findProductsByFulltext($keyword, Localization $localization, ?int $limit = null)
	{
		$query = $this->productRepository->findProductsByFulltext($keyword, $localization);

		if ($limit) {
			$query->setMaxResults($limit);
		}

		return $query->getResult();
	}
}

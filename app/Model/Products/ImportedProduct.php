<?php

namespace <PERSON><PERSON><PERSON>\Model\Products;

class ImportedProduct
{
	private string $locale;
	private int $shopId;
	private string $name;
	private ?string $description;
	private string $url;
	private ?string $imageUrl;
	private float $price;
	private ?float $oldPrice;
	private ?string $currency;
	private bool $inStock;
	private int $position;
	private string $validTill;
	private string $productId;
	private ?string $removedAt;
	private string $action = '';

	public function __construct(string $locale, int $shopId, string $name, string $productId, ?string $description, string $url, ?string $imageUrl, float $price, ?float $oldPrice, ?string $currency, bool $inStock, int $position, string $validTill, ?string $removedAt)
	{
		$this->locale = $locale;
		$this->shopId = $shopId;
		$this->name = $name;
		$this->productId = $productId;
		$this->description = $description;
		$this->url = $url;
		$this->imageUrl = $imageUrl;
		$this->price = $price;
		$this->oldPrice = $oldPrice;
		$this->currency = $currency;
		$this->inStock = $inStock;
		$this->position = $position;
		$this->validTill = $validTill;
		$this->removedAt = $removedAt;
	}

	public function getUrl(): string
	{
		return $this->url;
	}

	public function getAction(): string
	{
		return $this->action;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function getDescription(): ?string
	{
		return $this->description;
	}

	public function getImageUrl(): ?string
	{
		return $this->imageUrl;
	}

	public function getPrice(): float
	{
		return $this->price;
	}

	public function getOldPrice(): ?float
	{
		return $this->oldPrice;
	}

	public function getCurrency(): ?string
	{
		return $this->currency;
	}

	public function isInStock(): bool
	{
		return $this->inStock;
	}

	public function getPosition(): int
	{
		return $this->position;
	}

	public function getLocale(): string
	{
		return $this->locale;
	}

	public function getShopId(): int
	{
		return $this->shopId;
	}

	public function getValidTill(): \DateTime
	{
		return new \DateTime($this->validTill);
	}

	public function getProductId(): string
	{
		return $this->productId;
	}

	public function getRemovedAt(): ?string
	{
		return $this->removedAt;
	}
}

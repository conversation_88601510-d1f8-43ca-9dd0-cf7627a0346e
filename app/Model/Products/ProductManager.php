<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Products;

use Ka<PERSON>ino\Model\EntityManager;
use Ka<PERSON>ino\Model\Images\ImageStorage;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\Offers\Entities\Offer;
use Ka<PERSON>ino\Model\Products\Entities\Product;

class ProductManager
{
	/** @var EntityManager */
	private $em;

	/** @var ImageStorage */
	private $imageStorage;

	public function __construct(EntityManager $em, ImageStorage $imageStorage)
	{
		$this->em = $em;
		$this->imageStorage = $imageStorage;
	}

	public function createProduct(Localization $localization, string $name, string $slug): Product
	{
		$product = new Product($localization, $name, $slug);

		return $this->saveProduct($product);
	}

	public function saveProduct(Product $product): Product
	{
		$this->em->persist($product);
		$this->em->flush();

		return $product;
	}

	public function saveProductImage(Product $product, \Nette\Utils\Image $image, $imageType)
	{
		$oldImage = $product->getImageUrl();
		$newImage = $this->imageStorage->saveImagev2($image, $imageType, ImageStorage::NAMESPACE_PRODUCT_IMAGE, rand(500, 1000), $product->getSlug());
		$product->setImageUrl($newImage);

		if ($oldImage != $newImage) {
			$this->imageStorage->removeImage($oldImage);
		}

		$this->saveProduct($product);
	}
}

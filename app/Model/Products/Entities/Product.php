<?php

declare(strict_types=1);

namespace Kaufino\Model\Products\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Kaufino\Model\Tags\Entities\Tag;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Products\Repositories\ProductRepository")
 * @ORM\Table(name="kaufino_products_product", uniqueConstraints={
 *     @ORM\UniqueConstraint(name="product_unique", columns={"localization_id", "slug"})}
 * )
 */
class Product
{
	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

	/**
	 * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Tags\Entities\Tag", inversedBy="products")
	 * @ORM\JoinTable(name="kaufino_products_product_tag")
	 */
	protected $tags;

	/**
	 * @ORM\OneToMany(targetEntity="\Kaufino\Model\Offers\Entities\Offer", mappedBy="product")
	 */
	private $offers;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $brand;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $manufacturer;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $slug;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $imageUrl;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $description;

	/**
	 * @ORM\Column(type="integer")
	 */
	protected $priority = 0;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $updatedSitemapAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $archivedAt;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $active = true;

	public function __construct(Localization $localization, string $name, string $slug)
	{
		$this->localization = $localization;
		$this->name = $name;
		$this->slug = $slug;
		$this->updateSitemap();
		$this->createdAt = new DateTime();

		$this->tags = new ArrayCollection();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName($name): void
	{
		$this->name = $name;
	}

	public function getSlug(): string
	{
		return $this->slug;
	}

	public function getDescription(): ?string
	{
		return $this->description;
	}

	public function setDescription(?string $description): void
	{
		$this->description = $description;
	}

	/**
	 * @return mixed
	 */
	public function getImageUrl()
	{
		return $this->imageUrl;
	}

	/**
	 * @param mixed $imageUrl
	 */
	public function setImageUrl($imageUrl): void
	{
		$this->imageUrl = $imageUrl;
	}

	public function archive()
	{
		$this->archivedAt = new DateTime();
	}

	/**
	 * @param string $slug
	 */
	public function setSlug(string $slug): void
	{
		$this->slug = $slug;
	}

	/**
	 * @return mixed
	 */
	public function getTags()
	{
		return $this->tags;
	}

	public function addTag(Tag $tag)
	{
		$this->tags->add($tag);
	}

	public function clearTags()
	{
		$this->tags->clear();
	}

	/**
	 * @return mixed
	 */
	public function getBrand()
	{
		return $this->brand;
	}

	/**
	 * @param mixed $brand
	 */
	public function setBrand($brand): void
	{
		$this->brand = $brand;
	}

	/**
	 * @return mixed
	 */
	public function getManufacturer()
	{
		return $this->manufacturer;
	}

	/**
	 * @param mixed $manufacturer
	 */
	public function setManufacturer($manufacturer): void
	{
		$this->manufacturer = $manufacturer;
	}

	public function updateSitemap(): void
	{
		$this->updatedSitemapAt = new \DateTime();
	}

	/**
	 * @return int
	 */
	public function getPriority(): int
	{
		return $this->priority;
	}

	/**
	 * @param int $priority
	 */
	public function setPriority(int $priority): void
	{
		$this->priority = $priority;
	}
}

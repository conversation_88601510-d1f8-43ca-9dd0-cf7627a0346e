<?php

namespace <PERSON><PERSON><PERSON>\Model\Marketing\Entities;

use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Marketing\Repositories\EmailRepository")
 * @ORM\Table(name="kaufino_marketing_email")
 */
class Email
{
	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="Ka<PERSON>ino\Model\Shops\Entities\Shop", inversedBy="contentBlocks")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
	 */
	private $shop;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $email;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(string $email, ?Shop $shop = null)
	{
		$this->email = $email;
		$this->shop = $shop;
		$this->createdAt = new \DateTime();
	}
}

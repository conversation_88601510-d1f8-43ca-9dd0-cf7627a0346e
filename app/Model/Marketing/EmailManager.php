<?php

namespace <PERSON><PERSON><PERSON>\Model\Marketing;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Marketing\Entities\Email;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;

class EmailManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function createEmail(string $email, ?Shop $shop): Email
	{
		return $this->saveEmail(new Email($email, $shop));
	}

	public function saveEmail(Email $email): Email
	{
		$this->em->persist($email);
		$this->em->flush();

		return $email;
	}
}

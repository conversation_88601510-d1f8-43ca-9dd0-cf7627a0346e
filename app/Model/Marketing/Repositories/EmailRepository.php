<?php

namespace <PERSON><PERSON><PERSON>\Model\Marketing\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Marketing\Entities\Email;

class EmailRepository extends EntityRepository
{
	public function getEmails(): QueryBuilder
	{
		return $this->createQueryBuilder('e');
	}

	public function findEmail(string $email): ?Email
	{
		return $this->getEmails()
			->andWhere('e.email = :email')
			->setParameter('email', $email)
			->getQuery()
			->getOneOrNullResult();
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo\Repositories;

use Doctrine\ORM\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\GoogleIndex;

class GoogleIndexRepository extends EntityRepository
{
	public function findByUrl(string $url): ?GoogleIndex
	{
		return $this->findOneBy(['url' => $url]);
	}

	public function findUrlsToIndex(int $limit = 50): array
	{
		return $this->createQueryBuilder('gi')
			->where('gi.isIndexed = 0')
			->andWhere('gi.lastIndexingRequestAt IS NULL OR gi.lastIndexingRequestAt < :oneDayAgo')
			->setParameter('oneDayAgo', new \DateTime('-24 hours'))
			->setMaxResults($limit)
			->orderBy('gi.createdAt', 'ASC')
			->getQuery()
			->getResult();
	}

	public function findUrlsToCheckStatus(int $limit = 50): array
	{
		return $this->createQueryBuilder('gi')
			->where('gi.isIndexed = 0')
			->andWhere('gi.lastIndexingRequestAt IS NOT NULL')
			->andWhere('gi.lastStatusCheckAt IS NULL OR gi.lastStatusCheckAt < :sixHoursAgo')
			->setParameter('sixHoursAgo', new \DateTime('-6 hours'))
			->setMaxResults($limit)
			->orderBy('gi.lastIndexingRequestAt', 'ASC')
			->getQuery()
			->getResult();
	}

	public function findByWebsiteDomain(string $websiteDomain, int $limit = 100): array
	{
		return $this->createQueryBuilder('gi')
			->where('gi.websiteDomain = :websiteDomain')
			->setParameter('websiteDomain', $websiteDomain)
			->setMaxResults($limit)
			->orderBy('gi.createdAt', 'DESC')
			->getQuery()
			->getResult();
	}

	public function countPendingIndexing(): int
	{
		return (int) $this->createQueryBuilder('gi')
			->select('COUNT(gi.id)')
			->where('gi.isIndexed = 0')
			->getQuery()
			->getSingleScalarResult();
	}

	public function countIndexed(): int
	{
		return (int) $this->createQueryBuilder('gi')
			->select('COUNT(gi.id)')
			->where('gi.isIndexed = 1')
			->getQuery()
			->getSingleScalarResult();
	}

	public function findRecentlyProcessed(int $limit = 100): array
	{
		return $this->createQueryBuilder('gi')
			->where('gi.lastIndexingRequestAt IS NOT NULL')
			->setMaxResults($limit)
			->orderBy('gi.lastIndexingRequestAt', 'DESC')
			->getQuery()
			->getResult();
	}
}

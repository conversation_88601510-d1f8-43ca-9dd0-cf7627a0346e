<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\PageExtension;
use <PERSON><PERSON><PERSON>\Model\Seo\Repositories\PageExtensionRepository;
use Ka<PERSON>ino\Model\Seo\Repositories\RedirectionRepository;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

class SeoFacade
{
	/** @var EntityManager */
	private $em;

	/** @var SeoManager */
	private $seoManager;

	/** @var PageExtensionRepository */
	private $pageExtensionRepository;

	private RedirectionRepository $redirectionRepository;

	public function __construct(EntityManager $em, SeoManager $seoManager, PageExtensionRepository $pageExtensionRepository, RedirectionRepository $redirectionRepository)
	{
		$this->em = $em;
		$this->pageExtensionRepository = $pageExtensionRepository;
		$this->seoManager = $seoManager;
		$this->redirectionRepository = $redirectionRepository;
	}

	public function getPageExtensions(Website $website = null): QueryBuilder
	{
		return $this->pageExtensionRepository->getPageExtensions($website);
	}

	public function findPageExtension($id): ?PageExtension
	{
		return $this->pageExtensionRepository->find($id);
	}

	public function findPageExtensionBySlug(Website $website, string $slug): ?PageExtension
	{
		return $this->pageExtensionRepository->findPageExtensionBySlug($website, $slug);
	}

	public function createPageExtension(Website $website, string $slug): PageExtension
	{
		return $this->seoManager->createPageExtension($website, $slug);
	}

	public function savePageExtension(PageExtension $pageExtension)
	{
		return $this->seoManager->savePageExtension($pageExtension);
	}

	public function deletePageExtension(PageExtension $pageExtension)
	{
		$this->seoManager->deletePageExtension($pageExtension);
	}
}

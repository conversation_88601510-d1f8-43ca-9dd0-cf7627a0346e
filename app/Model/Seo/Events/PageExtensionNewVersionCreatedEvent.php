<?php

namespace <PERSON><PERSON><PERSON>\Model\Seo\Events;

use <PERSON><PERSON><PERSON>\Model\Seo\Entities\PageExtension;
use <PERSON><PERSON><PERSON>\Model\Users\Entities\User;

final class PageExtensionNewVersionCreatedEvent
{
	/** @var PageExtension */
	public $pageExtension;

	/** @var User */
	public $user;

	public function __construct(PageExtension $pageExtension, User $user)
	{
		$this->pageExtension = $pageExtension;
		$this->user = $user;
	}

	public function getPageExtension(): PageExtension
	{
		return $this->pageExtension;
	}

	public function getUser(): User
	{
		return $this->user;
	}
}

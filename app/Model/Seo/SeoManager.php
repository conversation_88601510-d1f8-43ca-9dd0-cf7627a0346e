<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\Seo\Entities\PageExtension;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

class SeoManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function createPageExtension(Website $website, string $slug)
	{
		$pageExtension = new PageExtension($website, $slug);

		return $this->savePageExtension($pageExtension);
	}

	public function savePageExtension(PageExtension $pageExtension)
	{
		$this->em->persist($pageExtension);
		$this->em->flush();

		return $pageExtension;
	}

	public function deletePageExtension(PageExtension $pageExtension)
	{
		$this->em->remove($pageExtension);
		$this->em->flush();
	}
}

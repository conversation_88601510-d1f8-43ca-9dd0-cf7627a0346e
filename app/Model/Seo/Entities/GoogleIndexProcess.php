<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Seo\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="<PERSON><PERSON><PERSON>\Model\Seo\Repositories\GoogleIndexProcessRepository")
 * @ORM\Table(name="kaufino_seo_google_index_process")
 */
class GoogleIndexProcess
{
	public const API_TYPE_INDEXING = 'indexing';
	public const API_TYPE_SEARCH_CONSOLE = 'search_console';

	public const REQUEST_TYPE_URL_UPDATED = 'URL_UPDATED';
	public const REQUEST_TYPE_URL_DELETED = 'URL_DELETED';
	public const REQUEST_TYPE_STATUS_CHECK = 'status_check';

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=false)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Ka<PERSON>ino\Model\Seo\Entities\GoogleIndex")
	 * @ORM\JoinColumn(name="google_index_id", referencedColumnName="id")
	 */
	protected $googleIndex;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $apiType;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $requestType;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $requestData;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $responseData;

	/**
	 * @ORM\Column(type="integer")
	 */
	protected $httpStatusCode;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $success = false;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $errorMessage;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(GoogleIndex $googleIndex, string $apiType, string $requestType)
	{
		$this->googleIndex = $googleIndex;
		$this->apiType = $apiType;
		$this->requestType = $requestType;
		$this->createdAt = new DateTime();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getGoogleIndex(): GoogleIndex
	{
		return $this->googleIndex;
	}

	public function getApiType(): string
	{
		return $this->apiType;
	}

	public function getRequestType(): string
	{
		return $this->requestType;
	}

	public function getRequestData(): ?string
	{
		return $this->requestData;
	}

	public function setRequestData(?string $requestData): void
	{
		$this->requestData = $requestData;
	}

	public function getResponseData(): ?string
	{
		return $this->responseData;
	}

	public function setResponseData(?string $responseData): void
	{
		$this->responseData = $responseData;
	}

	public function getHttpStatusCode(): int
	{
		return $this->httpStatusCode;
	}

	public function setHttpStatusCode(int $httpStatusCode): void
	{
		$this->httpStatusCode = $httpStatusCode;
	}

	public function isSuccess(): bool
	{
		return $this->success;
	}

	public function setSuccess(bool $success): void
	{
		$this->success = $success;
	}

	public function getErrorMessage(): ?string
	{
		return $this->errorMessage;
	}

	public function setErrorMessage(?string $errorMessage): void
	{
		$this->errorMessage = $errorMessage;
	}

	public function getCreatedAt(): DateTime
	{
		return $this->createdAt;
	}

	public function markSuccess(int $httpStatusCode, ?string $responseData = null): void
	{
		$this->success = true;
		$this->httpStatusCode = $httpStatusCode;
		$this->responseData = $responseData;
	}

	public function markError(int $httpStatusCode, string $errorMessage, ?string $responseData = null): void
	{
		$this->success = false;
		$this->httpStatusCode = $httpStatusCode;
		$this->errorMessage = $errorMessage;
		$this->responseData = $responseData;
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Model\Images;

use <PERSON><PERSON><PERSON>\Model\Configuration;
use Nette\Http\Url;
use Nette\Utils\Strings;

class ImageFilter
{
	private const VSHCDN_KEY = '511a375a56556f7d';
	private const VSHCDN_SALT = '1707bf366ac2cb60';

	public const FLAG_FIT = 'fit';
	public const FLAG_EXACT = 'exact';
	public const FLAG_FIT_MIDDLE = 'fitMiddle';
	public const FLAG_FIT_MIDDLE_WITHOUT_BORDERS = 'fitMiddleWithoutBorders';
	public const FLAG_EXACT_TOP = 'exactTop';

	/** @var Configuration */
	private $configuration;

	public function __construct(Configuration $configuration)
	{
		$this->configuration = $configuration;
	}

	public function __invoke(
		?string $imageUrl,
		float $width = null,
		float $height = null,
		string $flag = 'fit',
		string $extension = null,
		$quality = 70,
		float $maxBytes = null
	) {
		if (!$imageUrl) {
			$imageUrl = "/images/placeholder.png";
		}

//		if (isset($_COOKIE['d2s0KZA1rp9pwsRI9n0l'])) {
		if ($quality === '' || is_int($quality) === false || $quality < 1 || $quality > 100) { //fix prazdneho parametru - @todo je treba v sablone najit odkud se vola a smazat; pote do quality parametru pridat float type hint
			$quality = null;
		}

		return $this->invokeNewCdn($imageUrl, $width, $height, $flag, $extension, $quality, $maxBytes);
//		}

		$url = new Url($imageUrl);
		$urlAuthority = str_replace('www.', '', $url->getAuthority());

//		if (empty($urlAuthority)) {
//			$urlAuthority = 'https://kaufino.com' . $imageUrl;
//		}

		if ($urlAuthority === 'letaky.tipli.cz') {
			$cdnPrefix = 's'; // steve
		} elseif ($urlAuthority === 'kaufino.com' || $urlAuthority === '') {
			$cdnPrefix = 'k'; // kaufino
		} elseif ($urlAuthority === 'tipli.cz') {
			$cdnPrefix = 't'; // tipli
		} elseif (Strings::contains($urlAuthority, 'oferto')) {
			$cdnPrefix = 'o'; // Oferto
		} else {
			$cdnPrefix = 'a';
		}

		if ($cdnPrefix === 'a') {
			$urlPaths = [];
			$urlPaths[] = $cdnPrefix;
			$urlPaths[] = rawurlencode($imageUrl);

			$extension = 'jpg';
			bdump($extension);
			$fileName = Strings::substring(sha1($imageUrl), 0, 16) . '.' . $extension;

			$proxyToken = Strings::substring(
				sha1('osel' . $imageUrl . $fileName . ($width ?? 0) . ($height ?? 0) . $flag),
				0,
				4
			);
		} else {
			$urlPath = trim($url->getPath(), '/');
			$urlPaths = explode('/', $urlPath);
			$fileName = $urlPaths[count($urlPaths) - 1];

			if ($extension === 'webp') {
				$originalExtension = explode('.', $fileName);
				$originalExtension = end($originalExtension);
				$newExtension = 'webp';

				$fileName = mb_substr($fileName, 0, -1 - strlen($originalExtension)) . '.webp'; // with .webp
			}

			if ($extension === 'jpg') {
				$originalExtension = explode('.', $fileName);
				$originalExtension = end($originalExtension);

				if ($originalExtension !== $extension) {
					$newExtension = 'jpg';
					$fileName = mb_substr($fileName, 0, -1 - strlen($originalExtension)) . '.jpg'; // with .jpg
				}
			}

			array_pop($urlPaths); // remove last value - filename
			array_unshift($urlPaths, $cdnPrefix); // add prefix to first position of array

			$originalFileName = isset($newExtension)
				? str_replace('.' . $newExtension, '.' . $originalExtension, $fileName)
				: $fileName
			;

			$proxyToken = Strings::substring(
				sha1('osel' . $originalFileName . ($width ?? 0) . ($height ?? 0) . $flag),
				0,
				4
			);
		}

		$urlPaths[] = $proxyToken;
		$urlPaths[] = ($width ?: '') . 'x' . ($height ?: '');
		$urlPaths[] = $flag;
		$urlPaths[] = $fileName;

		if ($this->configuration->isLetado()) {
			$baseDomain = 'l.klmcdn.com';
		} elseif ($this->configuration->isOferto()) {
			$baseDomain = 'm.klmcdn.com';
		} else {
			$baseDomain = 'k.klmcdn.com';
		}

		$proxyUrl = 'https://' . $baseDomain . '/' . implode('/', $urlPaths);
		$proxyUrl .= '?v=13.1';

		// https://static.tipli.cz/a/https%3A%2F%2Fwww.datart.cz%2Ffoto%2F250%2F2%2F3%2F5%2Fproduct_5070532.jpg/cff5/400x400/fit/fa3564f9ffdb6929.jpg?v=13.0
		// https://m.klmcdn.com/a/https%3A%2F%2Fwww.datart.cz%2Ffoto%2F250%2F2%2F3%2F5%2Fproduct_5070532.jpg/8f24/300x300/fit/fa3564f9ffdb6929.jpg?v=13.0

		// https://m.klmcdn.com/a/https%3A%2F%2Fwww.datart.cz%2Ffoto%2F250%2F2%2F3%2F5%2Fproduct_5070532.jpg/cff5/400x400/fit/fa3564f9ffdb6929.jpg?v=13.0

		if (
			isset($newExtension, $originalExtension)
			&& (
				($newExtension === 'webp' && $originalExtension !== 'jpg') || ($newExtension === 'jpg')
			)
		) {
			$proxyUrl .= '&from=' . $originalExtension;
		} elseif (isset($originalExtension) && $originalExtension === 'webp') {
			$proxyUrl .= '&from=webp';
		}

		return $proxyUrl;
	}

	private function invokeNewCdn(?string $imageUrl, ?float $width, ?float $height, string $flag, ?string $extension = null, ?int $quality = 100, float $maxBytes = null): string
	{
		$ttl = 7200; // nacachování (browser + CDN) na x sekund minimalni hodnota 3600
		$width = $width !== null ? $width : 0;
		$height = $height !== null ? $height : 0;
		$enlarge = 1;

		if (Strings::startsWith($imageUrl, '/upload')) {
			$imageUrl = 'https://kaufino.com' . $imageUrl;
		}

		if ($flag === 'exactTop' || $flag === 'exact') {
			$flag = 'fill';
		}

		if ($extension === null) {
			$extension = explode('.', $imageUrl);
			$extension = end($extension);
		}

		$keyBin = pack("H*", self::VSHCDN_KEY);
		if (empty($keyBin)) {
			return $imageUrl;
		}

		$saltBin = pack("H*", self::VSHCDN_SALT);
		if (empty($saltBin)) {
			return $imageUrl;
		}

		$encodedUrl = rtrim(strtr(base64_encode($imageUrl), '+/', '-_'), '=');

		$parameters = [
			'resize:' . $flag . ':' . $width . ':' . $height . ':' . $enlarge,
			'gravity:no',
		];

		if ($maxBytes !== null) {
			$parameters[] = 'max_bytes:' . $maxBytes;
		}

		if ($quality !== null) {
			$parameters[] = 'quality:' . $quality;
		}

		$path = '/' . implode('/', $parameters) . '/' . $encodedUrl . '.' . $extension;
		$signature = rtrim(strtr(base64_encode(hash_hmac('sha256', $saltBin . '/' . $ttl . '/' . $path, $keyBin, true)), '+/', '-_'), '=');

		return 'https://n.klmcdn.com/zoh4eiLi/IMG/' . $ttl . '/' . $signature . $path;
	}
}

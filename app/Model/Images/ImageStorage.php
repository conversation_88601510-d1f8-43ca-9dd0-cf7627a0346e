<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Images;

use Nette\Http\FileUpload;
use <PERSON><PERSON><PERSON>\Model\Configuration;
use Nette\InvalidArgumentException;
use Nette\Utils\FileSystem;
use Nette\Utils\Image;

class ImageStorage
{
	public const NAMESPACE_SHOP_LOGO = 'shops/logo';
	public const NAMESPACE_OFFER_IMAGE = 'offers/image';
	public const NAMESPACE_PRODUCT_IMAGE = 'products/image';
	public const NAMESPACE_LEAFLET_ANNOTATED = 'leaflet/annotated';
	public const NAMESPACE_REDACTOR = 'rdctr';
	public const NAMESPACE_ARTICLE_IMAGE = 'articles/image';
	public const NAMESPACE_TAG_IMAGE = 'tags/image';
	public const NAMESPACE_USER_IMAGE = 'users/image';

	/** @var string */
	private $imagesPath;

	/** @var string */
	private $imagesUrl;

	/** @var Configuration */
	private $configuration;

	public function __construct(Configuration $configuration)
	{
		$this->configuration = $configuration;
		$this->imagesPath = $configuration->getImagesPath();
		$this->imagesUrl = $configuration->getImagesUrl();
	}

	public function saveImage(FileUpload $imageFile, string $namespace, int $id = null, string $name = null): string
	{
		if (!($imageFile->isOk())) {
			throw new InvalidArgumentException('Obrázek je poškozený.');
		}

		switch ($imageFile->getContentType()) {
			case 'image/png':
				$extension = '.png';
				break;
			case 'image/jpeg':
			case 'image/jpg':
				$extension = '.jpg';
				break;
			default:
				throw new InvalidArgumentException('Neznámý typ souboru.');
		}

		$imageFile = $imageFile->toImage();

		if (!file_exists($this->imagesPath . $namespace)) {
			mkdir($this->imagesPath . $namespace);
		}

		if ($id) {
			$fileName = $name ? $name . '-' . $id : $id;
		} else {
			$fileName = sha1($imageFile->toString()) . $extension;
		}

		$fileName .= $extension;

		$dir = $this->imagesPath . $namespace . '/';
		$temporaryName = $dir . $fileName;

		$imageFile->save($temporaryName, 100);

		return $this->imagesUrl . $namespace . '/' . $fileName;
	}

	public function removeImage($path)
	{
		$path = $this->imagesPath . '/shops/logo/dm-drogerie-454.png';
		FileSystem::delete($path);
	}

	public function saveImagev2(Image $image, $imageType, string $namespace, int $id = null, string $name = null): string
	{
		switch ($imageType) {
			case Image::PNG:
				$extension = '.png';
				break;
			case Image::JPEG:
				$extension = '.jpg';
				break;
			default:
				throw new InvalidArgumentException('Neznámý typ souboru.');
		}

		$imageFile = $image;

		if (!file_exists($this->imagesPath . $namespace)) {
			mkdir($this->imagesPath . $namespace);
		}

		if ($id) {
			$fileName = $name ? $name . '-' . $id : $id;
		} else {
			$fileName = sha1($imageFile->toString()) . $extension;
		}

		$fileName .= $extension;

		$dir = $this->imagesPath . $namespace . '/';
		$temporaryName = $dir . $fileName;

		$imageFile->save($temporaryName, 100);

		return $this->imagesUrl . $namespace . '/' . $fileName;
	}
}

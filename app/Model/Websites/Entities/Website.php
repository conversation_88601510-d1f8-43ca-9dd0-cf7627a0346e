<?php

declare(strict_types=1);

namespace Ka<PERSON>ino\Model\Websites\Entities;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use Doctrine\ORM\Mapping as ORM;
use Nette\Http\Url;

/**
 * @ORM\Entity(repositoryClass="Ka<PERSON><PERSON>\Model\Websites\Repositories\WebsiteRepository")
 * @ORM\Table(name="kaufino_websites_website")
 */
class Website
{
	public const COMPANY_ADSALVA = 'adsalva';
	public const COMPANY_BUSINESS_ANIMALS = 'business_animals';

	public const MODULE_KAUFINO = 'kaufino';
	public const MODULE_KAUFINO_SUBDOMAIN = 'kaufino_subdomain';
	public const MODULE_LETADO = 'letado';
	public const MODULE_LETADO_SUBDOMAIN = 'letado_subdomain';
	public const MODULE_OFERTO = 'oferto';
	public const MODULE_OFERTO_COM = 'oferto_com';

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Websites\Entities\Website")
	 * @ORM\JoinColumn(name="parent_website_id", referencedColumnName="id", nullable=true)
	 */
	protected $parentWebsite;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected string $module;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected ?string $type = null;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $domain;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected ?string $gtmId = null;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected ?int $countOfShops = 0;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected ?int $countOfOffers = 0;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected ?int $countOfActiveCities = 0;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected ?int $countOfShopsInCities = 0;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected ?int $countOfActiveShopsInCities = 0;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected ?int $countOfStores = 0;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected ?int $countOfActiveStores = 0;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected ?int $countOfArticles = 0;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $active = true;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $company = null;

	/**
	 * @return int
	 */
	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * @return mixed
	 */
	public function getLocalization()
	{
		return $this->localization;
	}

	public function getModule(): string
	{
		return $this->module;
	}

	public function getLocaleFileName()
	{
		if ($this->getModule() === self::MODULE_KAUFINO_SUBDOMAIN) {
			return self::MODULE_KAUFINO;
		}

		if ($this->getModule() === self::MODULE_LETADO_SUBDOMAIN) {
			return self::MODULE_LETADO;
		}

		return $this->getModule();
	}

	/**
	 * @return mixed
	 */
	public function getDomain()
	{
		return $this->domain;
	}

	/**
	 * @return mixed
	 */
	public function getName()
	{
		return $this->name;
	}

	/**
	 * @return bool
	 */
	public function isActive(): bool
	{
		return $this->active;
	}

	public function isKaufino(): bool
	{
		return $this->module === self::MODULE_KAUFINO;
	}

	public function isKaufinoSubdomain(): bool
	{
		return $this->module === self::MODULE_KAUFINO_SUBDOMAIN;
	}

	public function isLetadoSubdomain(): bool
	{
		return $this->module === self::MODULE_LETADO_SUBDOMAIN;
	}

	public function isLetado(): bool
	{
		return $this->module === self::MODULE_LETADO;
	}

	public function isOferto(): bool
	{
		return $this->module === self::MODULE_OFERTO;
	}

	public function isOfertoCom(): bool
	{
		return $this->module === self::MODULE_OFERTO_COM;
	}

	public function hasKaufinoAdsense(): bool
	{
		$localization = $this->getLocalization();

		return $localization->isLatvian() || $localization->isSerbian() || $localization->isBulgarian() || $localization->isFrancian() || $localization->isSpaian() || $localization->isUnitedStatesAmerican();
	}

	public function hasOfertoAdsense(): bool
	{
		$localization = $this->getLocalization();

		return $localization->isLatvian() === false && $localization->isSerbian() === false && $localization->isBulgarian() === false && $localization->isFrancian() === false && $localization->isSpaian() === false && $localization->isLithuanian() === false && $localization->isEstonian() === false && $localization->isMoldavian() === false && $localization->isUnitedStatesAmerican() === false;
	}

	public function hasAdsalvaAdsense(): bool
	{
		$localization = $this->getLocalization();

		return $localization->isLithuanian() || $localization->isEstonian();
	}

	public function hasStarioAdsense(): bool
	{
		/** @var Localization $localization */
		$localization = $this->getLocalization();

		return $localization->isMoldavian();
	}

	public function hasAdSense(): bool
	{
		if ($this->module === self::MODULE_KAUFINO) {
			return true;
		}

		if ($this->module === self::MODULE_LETADO) {
			return true;
		}

		if ($this->module === self::MODULE_OFERTO) {
			$localization = $this->getLocalization();

			if (
				$localization->isCzech()
				|| $localization->isSlovak()
				|| $localization->isPolish()
				|| $localization->isRomanian()
				|| $localization->isHungarian()
				|| $localization->isCroatian()
				|| $localization->isItaly()
				|| $localization->isGermany()
				|| $localization->isCanadian()
				|| $localization->isBelgian()
				|| $localization->isNetherlandian()
				|| $localization->isDenmarkian()
				|| $localization->isGreecian()
				|| $localization->isJar()
				|| $localization->isSwedian()
				|| $localization->isFinlandian()
				|| $localization->isAustrian()
				|| $localization->isSwitzerlandian()
				|| $localization->isNorwaian()
				|| $localization->isGreatbritian()
				|| $localization->isSlovenian()
				|| $localization->isLatvian()
				|| $localization->isSerbian()
				|| $localization->isBulgarian()
				|| $localization->isFrancian()
				|| $localization->isLithuanian()
				|| $localization->isSpaian()
				|| $localization->isEstonian()
				|| $localization->isMoldavian()
				|| $localization->isUnitedStatesAmerican()
			) {
				return true;
			}
		}

		return false;
	}

	public static function getModules(): array
	{
		return [self::MODULE_KAUFINO, self::MODULE_LETADO, self::MODULE_OFERTO, self::MODULE_OFERTO_COM];
	}

	public function getCountOfShops(): int
	{
		return $this->countOfShops;
	}

	public function setCountOfShops(int $countOfShops)
	{
		$this->countOfShops = $countOfShops;
	}

	public function getCountOfOffers(): ?int
	{
		return $this->countOfOffers;
	}

	public function setCountOfOffers(int $countOfOffers)
	{
		$this->countOfOffers = $countOfOffers;
	}

	public function getCountOfActiveCities(): ?int
	{
		return $this->countOfActiveCities;
	}

	public function setCountOfActiveCities(int $countOfActiveCities)
	{
		$this->countOfActiveCities = $countOfActiveCities;
	}

	public function getCountOfShopsInCities(): ?int
	{
		return $this->countOfShopsInCities;
	}

	public function setCountOfShopsInCities(int $countOfShopsInCities)
	{
		$this->countOfShopsInCities = $countOfShopsInCities;
	}

	public function getCountOfStores(): ?int
	{
		return $this->countOfStores;
	}

	public function setCountOfStores(int $countOfStores)
	{
		$this->countOfStores = $countOfStores;
	}

	public function getCountOfActiveStores(): ?int
	{
		return $this->countOfActiveStores;
	}

	public function setCountOfActiveStores(int $countOfActiveStores)
	{
		$this->countOfActiveStores = $countOfActiveStores;
	}

	public function getCountOfArticles(): ?int
	{
		return $this->countOfArticles;
	}

	public function setCountOfArticles(int $countOfArticles)
	{
		$this->countOfArticles = $countOfArticles;
	}

	public function getHost()
	{
		$url = new Url($this->domain);

		return str_replace('www.', '', $url->getHost());
	}

	public function getType(): ?string
	{
		return $this->type;
	}

	public function getParentWebsite(): Website
	{
		return $this->parentWebsite ?: $this;
	}

	public function getCompany(): ?string
	{
		return $this->company;
	}

	public function getCompanyName(): ?string
	{
		if ($this->getCompany() === self::COMPANY_ADSALVA) {
			return 'Adsalva s.r.o.';
		} elseif ($this->getCompany() === self::COMPANY_BUSINESS_ANIMALS) {
			return 'Business Animals s.r.o.';
		}

		return null;
	}

	public function getCompanyId(): ?string
	{
		if ($this->getCompany() === self::COMPANY_ADSALVA) {
			return '03786986';
		} elseif ($this->getCompany() === self::COMPANY_BUSINESS_ANIMALS) {
			return '02734699';
		}

		return null;
	}

	public function getCompanyVatId(): ?string
	{
		if ($this->getCompany() === self::COMPANY_ADSALVA) {
			return 'CZ03786986';
		} elseif ($this->getCompany() === self::COMPANY_BUSINESS_ANIMALS) {
			return 'CZ02734699';
		}

		return null;
	}

	public function setCountOfActiveShopsInCities(int $countOfActiveShopsInCities): void
	{
		$this->countOfActiveShopsInCities = $countOfActiveShopsInCities;
	}

	public function getCountOfActiveShopsInCities(): ?int
	{
		return $this->countOfActiveShopsInCities;
	}

	public function getGtmId(): ?string
	{
		return $this->gtmId;
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Websites;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityManager;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use Ka<PERSON>ino\Model\Websites\Repositories\WebsiteRepository;

class WebsiteFacade
{
	/** @var EntityManager */
	private $em;

	/**
	 * @var WebsiteRepository
	 */
	private $websiteRepository;

	/**
	 * @var WebsiteResolver
	 */
	private $websiteResolver;

	public function __construct(EntityManager $em, WebsiteRepository $websiteRepository, WebsiteResolver $websiteResolver)
	{
		$this->em = $em;
		$this->websiteRepository = $websiteRepository;
		$this->websiteResolver = $websiteResolver;
	}

	public function findWebsite($id): ?Website
	{
		/** @var ?Website $website */
		$website = $this->websiteRepository->find($id);

		return $website;
	}

	public function findWebsites(): array
	{
		return $this->websiteRepository->findAll();
	}

	public function findActiveWebsites($module = null): array
	{
		$params = [
			'active' => true,
		];

		if ($module) {
			$params['module'] = $module;
		}

		return $this->websiteRepository->findBy($params, ['name' => 'ASC']);
	}

	public function getWebsites(): QueryBuilder
	{
		return $this->websiteRepository->getWebsites();
	}

	public function findActiveWebsiteByLocalization(Localization $localization, $module)
	{
		$params = [
			'localization' => $localization,
			'module' => $module,
			'active' => true,
		];

		return $this->websiteRepository->findOneBy($params);
	}

	public function findPairs(): array
	{
		return $this->websiteRepository->findPairsList();
	}

	public function resolveCurrentWebsite(): ?Website
	{
		return $this->websiteResolver->resolve();
	}

	public function isOferito(): bool
	{
		return $this->websiteResolver->isOferito();
	}

	public function isMrOfferto(): bool
	{
		return $this->websiteResolver->isMrOfferto();
	}

	public function save(Website $website): void
	{
		$this->em->persist($website);
		$this->em->flush();
	}
}

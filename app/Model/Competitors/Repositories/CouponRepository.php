<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Competitors\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityRepository;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;

class CouponRepository extends EntityRepository
{
	public function getCoupons(Localization $localization = null, $current = true): QueryBuilder
	{
		$qb = $this->createQueryBuilder('c');

		if ($localization) {
			$qb->andWhere('c.localization = :localization')->setParameter('localization', $localization);
		} else {
			$qb->leftJoin('c.localization', 'l');
		}

		if ($current) {
			$qb->andWhere('c.validTill > :validTill')
				->setParameter('validTill', new \DateTime());
		}

		return $qb;
	}

	public function findCouponByCouponId(Localization $localization, string $source, $couponId)
	{
		$qb = $this->getCoupons($localization)
			->andWhere('c.sourceName = :sourceName')
			->andWhere('c.couponId = :couponId')
			->setParameter('sourceName', $source)
			->setParameter('couponId', $couponId);


		return $qb->getQuery()->getOneOrNullResult();
	}

	public function getOffers(Localization $localization = null, $current = true): QueryBuilder
	{
		$qb = $this->createQueryBuilder('c')
			->innerJoin('c.offer', 'o')
			->addSelect('o');

		if ($localization) {
			$qb->andWhere('o.localization = :localization')->setParameter('localization', $localization);
		} else {
			$qb->leftJoin('o.localization', 'l');
		}

		if ($current) {
			$qb->andWhere('o.validTill > :validTill')
				->setParameter('validTill', new \DateTime());
		}

		if ($current == false) {
			$qb->andWhere('o.validTill < :validTill')
				->setParameter('validTill', new \DateTime());
		}

		return $qb;
	}

	public function findOffersToConfirm(int $limit)
	{
		return $this->getOffers()
			->andWhere('o.confirmedAt IS NULL')
			->andWhere('o.archivedAt IS NULL')
			->getQuery();
	}

	public function getCountOfOffersToConfirm()
	{
		return (int) $this->getOffers()
			->select('count(o.id)')
			->andWhere('o.confirmedAt IS NULL')
			->andWhere('o.archivedAt IS NULL')
			->getQuery()->getSingleScalarResult();
	}

	public function getOffersByShop(Shop $shop, $current)
	{
		$qb = $this->getOffers(null, $current)
			->andWhere('o.confirmedAt IS NOT NULL')
			->andWhere('o.shop = :shop')
			->setParameter('shop', $shop)
			->addOrderBy('o.priority', 'DESC')
			->addOrderBy('o.validTill', 'DESC');

		return $qb->getQuery();
	}

	public function findOffersByNames(array $names, Localization $localization)
	{
		$qb = $this->getOffers($localization, false)
			->andWhere('o.name IN (:names)')
			->setParameter('names', $names);

		return $qb->getQuery();
	}

	public function findOffersByFulltext($keyword, Localization $localization, $current = true)
	{
		return $this->getOffers($localization, $current)
			->andWhere('o.confirmedAt IS NOT NULL')
			->andWhere('o.name LIKE :keyword OR o.ocrOutput LIKE :keyword')
			->setParameter('keyword', '%' . $keyword . '%')
			->getQuery();
	}
}

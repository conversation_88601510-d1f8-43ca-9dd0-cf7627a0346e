<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Competitors;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\Competitors\Entities\Coupon;
use <PERSON><PERSON><PERSON>\Model\Competitors\Repositories\CouponRepository;
use <PERSON><PERSON>ino\Model\EntityManager;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Kaufino\Model\Offers\Repositories\OfferRepository;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Tags\Entities\Tag;
use Nette\Utils\Image;
use Nette\Utils\Strings;
use Tracy\Debugger;

class CompetitorFacade
{
	/** @var EntityManager */
	private $em;

	/** @var CompetitorManager */
	private $competitorManager;

	/** @var CouponRepository */
	private $couponRepository;

	public function __construct(EntityManager $em, CompetitorManager $competitorManager, CouponRepository $couponRepository)
	{
		$this->em = $em;
		$this->couponRepository = $couponRepository;
		$this->competitorManager = $competitorManager;
	}

	public function saveCoupon(Coupon $coupon)
	{
		return $this->competitorManager->saveCoupon($coupon);
	}

	public function getCoupons(Localization $localization = null): QueryBuilder
	{
		return $this->couponRepository->getCoupons($localization);
	}

	public function findCoupon($id): ?Coupon
	{
		/** @var ?Coupon $coupon */
		$coupon = $this->couponRepository->find($id);

		return $coupon;
	}

	public function findCouponByCouponId(Localization $localization, string $source, $couponId): ?Coupon
	{
		return $this->couponRepository->findCouponByCouponId($localization, $source, $couponId);
	}

	public function findCouponsToProcess($limit = 3): array
	{
		$query = $this->couponRepository->findCouponsToProcess($limit);
		$query->setMaxResults($limit);

		return $query->getResult();
	}

	public function getCountOfCouponsToProcess(): int
	{
		return $this->couponRepository->getCountOfCouponsToProcess();
	}
}

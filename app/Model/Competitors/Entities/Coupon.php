<?php

declare(strict_types=1);

namespace Ka<PERSON>ino\Model\Competitors\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Competitors\Repositories\CouponRepository")
 * @ORM\Table(name="kaufino_competitors_coupon")
 */
class Coupon
{
	public const DISCOUNT_TYPE_RELATIVE = 'relative';
	public const DISCOUNT_TYPE_ABSOLUTE = 'absolute';
	public const DISCOUNT_TYPE_FREE_SHIPPING = 'free_shipping';

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Shops\Entities\Shop")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id", nullable=true)
	 */
	protected $shop;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Offers\Entities\Offer")
	 * @ORM\JoinColumn(name="offer_id", referencedColumnName="id", nullable=true)
	 */
	protected $offer;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $shopName;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $shopDomain;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $sourceName;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $couponId;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $description;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $discountType;

	/**
	 * @ORM\Column(type="float", nullable=true)
	 */
	protected $discountAmount;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $code;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $exitUrl;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validTill;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Users\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=true)
	 */
	protected $closedBy;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $closedReason;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $processedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $closedAt;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected $timeToCreate;

	public function __construct(Localization $localization, Shop $shop = null, $shopName, $shopDomain, $sourceName, $couponId, $discountType, $discountAmount, $name, $description, $code, $validSince, $validTill)
	{
		$this->localization = $localization;
		$this->shop = $shop;
		$this->shopName = $shopName;
		$this->shopDomain = $shopDomain;
		$this->sourceName = $sourceName;
		$this->couponId = $couponId;
		$this->discountType = $discountType;
		$this->discountAmount = $discountAmount;
		$this->name = $name;
		$this->description = $description;
		$this->code = $code;
		$this->validSince = $validSince;
		$this->validTill = $validTill;

		$this->createdAt = new DateTime();
		$this->processedAt = new DateTime();
	}

	/**
	 * @return int
	 */
	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * @param int $id
	 */
	public function setId(int $id): void
	{
		$this->id = $id;
	}

	/**
	 * @return Localization
	 */
	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	/**
	 * @param Localization $localization
	 */
	public function setLocalization(Localization $localization): void
	{
		$this->localization = $localization;
	}

	/**
	 * @return Shop
	 */
	public function getShop(): Shop
	{
		return $this->shop;
	}

	/**
	 * @param Shop $shop
	 */
	public function setShop(Shop $shop): void
	{
		$this->shop = $shop;
	}

	/**
	 * @return mixed
	 */
	public function getOffer()
	{
		return $this->offer;
	}

	/**
	 * @param mixed $offer
	 */
	public function setOffer($offer): void
	{
		$this->offer = $offer;
	}

	/**
	 * @return mixed
	 */
	public function getSourceName()
	{
		return $this->sourceName;
	}

	/**
	 * @param mixed $sourceName
	 */
	public function setSourceName($sourceName): void
	{
		$this->sourceName = $sourceName;
	}

	/**
	 * @return mixed
	 */
	public function getCouponId()
	{
		return $this->couponId;
	}

	/**
	 * @param mixed $couponId
	 */
	public function setCouponId($couponId): void
	{
		$this->couponId = $couponId;
	}

	/**
	 * @return string
	 */
	public function getName(): string
	{
		return $this->name;
	}

	/**
	 * @param string $name
	 */
	public function setName(string $name): void
	{
		$this->name = $name;
	}

	/**
	 * @return mixed
	 */
	public function getDescription()
	{
		return $this->description;
	}

	/**
	 * @param mixed $description
	 */
	public function setDescription($description): void
	{
		$this->description = $description;
	}

	/**
	 * @return string
	 */
	public function getDiscountType(): string
	{
		return $this->discountType;
	}

	/**
	 * @param string $discountType
	 */
	public function setDiscountType(string $discountType): void
	{
		$this->discountType = $discountType;
	}

	/**
	 * @return mixed
	 */
	public function getDiscountAmount()
	{
		return $this->discountAmount;
	}

	/**
	 * @param mixed $discountAmount
	 */
	public function setDiscountAmount($discountAmount): void
	{
		$this->discountAmount = $discountAmount;
	}

	/**
	 * @return mixed
	 */
	public function getCode()
	{
		return $this->code;
	}

	/**
	 * @param mixed $code
	 */
	public function setCode($code): void
	{
		$this->code = $code;
	}

	/**
	 * @return mixed
	 */
	public function getExitUrl()
	{
		return $this->exitUrl;
	}

	/**
	 * @param mixed $exitUrl
	 */
	public function setExitUrl($exitUrl): void
	{
		$this->exitUrl = $exitUrl;
	}

	/**
	 * @return DateTime
	 */
	public function getValidSince(): DateTime
	{
		return $this->validSince;
	}

	/**
	 * @param DateTime $validSince
	 */
	public function setValidSince(DateTime $validSince): void
	{
		$this->validSince = $validSince;
	}

	/**
	 * @return DateTime
	 */
	public function getValidTill(): DateTime
	{
		return $this->validTill;
	}

	/**
	 * @param DateTime $validTill
	 */
	public function setValidTill(DateTime $validTill): void
	{
		$this->validTill = $validTill;
	}

	/**
	 * @return mixed
	 */
	public function getTimeToCreate()
	{
		return $this->timeToCreate;
	}

	/**
	 * @param mixed $timeToCreate
	 */
	public function setTimeToCreate($timeToCreate): void
	{
		$this->timeToCreate = $timeToCreate;
	}

	/**
	 * @return mixed
	 */
	public function getClosedBy()
	{
		return $this->closedBy;
	}

	/**
	 * @param mixed $closedBy
	 */
	public function setClosedBy($closedBy): void
	{
		$this->closedBy = $closedBy;
	}

	/**
	 * @return mixed
	 */
	public function getClosedReason()
	{
		return $this->closedReason;
	}

	/**
	 * @param mixed $closedReason
	 */
	public function setClosedReason($closedReason): void
	{
		$this->closedReason = $closedReason;
	}

	/**
	 * @return DateTime
	 */
	public function getCreatedAt(): DateTime
	{
		return $this->createdAt;
	}

	/**
	 * @param DateTime $createdAt
	 */
	public function setCreatedAt(DateTime $createdAt): void
	{
		$this->createdAt = $createdAt;
	}

	/**
	 * @return mixed
	 */
	public function getProcessedAt()
	{
		return $this->processedAt;
	}

	/**
	 * @param mixed $processedAt
	 */
	public function setProcessedAt($processedAt): void
	{
		$this->processedAt = $processedAt;
	}

	/**
	 * @return mixed
	 */
	public function getClosedAt()
	{
		return $this->closedAt;
	}

	/**
	 * @param mixed $closedAt
	 */
	public function setClosedAt($closedAt): void
	{
		$this->closedAt = $closedAt;
	}

	/**
	 * @return mixed
	 */
	public function getShopName()
	{
		return $this->shopName;
	}

	/**
	 * @param mixed $shopName
	 */
	public function setShopName($shopName): void
	{
		$this->shopName = $shopName;
	}

	/**
	 * @return mixed
	 */
	public function getShopDomain()
	{
		return $this->shopDomain;
	}

	/**
	 * @param mixed $shopDomain
	 */
	public function setShopDomain($shopDomain): void
	{
		$this->shopDomain = $shopDomain;
	}

	public function close(): void
	{
		$this->closedAt = new DateTime();
	}

	public function isClosed(): bool
	{
		return $this->closedAt ? true : false;
	}
}

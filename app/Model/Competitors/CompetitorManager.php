<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Competitors;

use <PERSON><PERSON><PERSON>\Model\Competitors\Entities\Coupon;
use <PERSON><PERSON><PERSON>\Model\EntityManager;

class CompetitorManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function saveCoupon(Coupon $coupon): Coupon
	{
		$this->em->persist($coupon);
		$this->em->flush();

		return $coupon;
	}
}

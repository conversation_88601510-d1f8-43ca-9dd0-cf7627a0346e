<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Offers\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Tags\Entities\Tag;
use Nette\Utils\Strings;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Offers\Repositories\OfferRepository")
 * @ORM\Table(name="kaufino_offers_offer", uniqueConstraints={
 *     @ORM\UniqueConstraint(name="offer_unique", columns={"localization_id", "slug"})}
 * )
 */
class Offer
{
	public const PRIORITY_LEVEL_COMMON = 0;
	public const PRIORITY_LEVEL_IMPORTANT = 1;
	public const PRIORITY_LEVEL_TOP = 2;

	public const TYPE_DEAL = 'deal';
	public const TYPE_COUPON = 'coupon';
	public const TYPE_LEAFLET = 'leaflet';
	public const TYPE_PRODUCT = 'product';

	public const DISCOUNT_TYPE_RELATIVE = 'relative';
	public const DISCOUNT_TYPE_ABSOLUTE = 'absolute';
	public const DISCOUNT_TYPE_FREE_SHIPPING = 'free_shipping';

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Shops\Entities\Shop", inversedBy="offers")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id")
	 */
	protected $shop;

	/**
	 * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Tags\Entities\Tag", inversedBy="offers")
	 * @ORM\JoinTable(name="kaufino_offers_offer_tag")
	 */
	protected $tags;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Products\Entities\Product", inversedBy="offers")
	 * @ORM\JoinColumn(name="product_id", referencedColumnName="id", nullable=true)
	 */
	protected $product;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Leaflets\Entities\LeafletPage", inversedBy="offers")
	 * @ORM\JoinColumn(name="leaflat_page_id", referencedColumnName="id", nullable=true)
	 */
	protected $leafletPage;

	/**
	 * @ORM\Column(type="string", unique=true, nullable=true)
	 */
	protected $offerId;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected $shopId;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $leafletPageCoordinates;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $slug;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $imageUrl;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $description;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $ocrOutput;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $type;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $discountType; // percentage off, pound, free shipping

	/**
	 * @ORM\Column(type="float", nullable=true)
	 */
	protected $discountAmount;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $code;

	/**
	 * @ORM\Column(type="float", nullable=true)
	 */
	protected $currentPrice;

	/**
	 * @ORM\Column(type="float", nullable=true)
	 */
	protected $commonPrice;

	/**
	 * @ORM\Column(type="boolean", nullable=true)
	 * @var bool|null
	 */
	private $inStock;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $exitUrl;

	/**
	 * @ORM\Column(type="integer")
	 */
	protected $priorityLevel = 0;

	/**
	 * @ORM\Column(type="integer")
	 */
	protected $priority = 0;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $validTill;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $confirmedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?DateTime $removedAt = null;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Users\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id")
	 */
	protected $author;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected $timeToCreate;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $archivedAt;

	public function __construct(Localization $localization, string $name, string $slug, Shop $shop, string $type, string $discountType, DateTime $validSince, DateTime $validTill)
	{
		if (Strings::upper($name) === $name) {
			$name = Strings::firstUpper($name);
		}

		$this->localization = $localization;
		$this->name = $name;
		$this->slug = $slug;
		$this->shop = $shop;
		$this->type = $type;
		$this->discountType = $discountType;
		$this->validSince = $validSince;
		$this->validTill = $validTill;
		$this->createdAt = new DateTime();

		$this->tags = new ArrayCollection();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName($name): void
	{
		$this->name = $name;
	}

	public function getSlug(): string
	{
		return $this->slug;
	}

	public function getShopId(): ?int
	{
		return $this->shopId;
	}

	public function setShopId($shopId): void
	{
		$this->shopId = $shopId;
	}

	public function getDescription(): ?string
	{
		return $this->description;
	}

	public function setDescription(?string $description): void
	{
		$this->description = $description;
	}

	public function getPriority(): ?int
	{
		return $this->priority;
	}

	public function setPriority(int $priority): void
	{
		$this->priority = $priority;
	}

	/**
	 * @return mixed
	 */
	public function getType()
	{
		return $this->type;
	}

	/**
	 * @param mixed $type
	 */
	public function setType($type): void
	{
		$this->type = $type;
	}

	public static function getTypes(): array
	{
		return [
			self::TYPE_DEAL => 'Deal',
			self::TYPE_COUPON => 'Coupon',
			self::TYPE_LEAFLET => 'Leaflet',
		];
	}

	public static function getDiscountTypes(): array
	{
		return [
			self::DISCOUNT_TYPE_RELATIVE => 'Relative (percentage)',
			self::DISCOUNT_TYPE_ABSOLUTE => 'Absolute (price off)',
			self::DISCOUNT_TYPE_FREE_SHIPPING => 'Free shipping',
		];
	}

	public static function getPriorityLevels(): array
	{
		return [
			self::PRIORITY_LEVEL_COMMON => 'Common',
			self::PRIORITY_LEVEL_IMPORTANT => 'Important',
			self::PRIORITY_LEVEL_TOP => 'Top',
		];
	}

	/**
	 * @return mixed
	 */
	public function getShop()
	{
		return $this->shop;
	}

	/**
	 * @param mixed $shop
	 */
	public function setShop($shop): void
	{
		$this->shop = $shop;
	}

	/**
	 * @return mixed
	 */
	public function getLeafletPage()
	{
		return $this->leafletPage;
	}

	/**
	 * @param mixed $leafletPage
	 */
	public function setLeafletPage($leafletPage): void
	{
		$this->leafletPage = $leafletPage;
	}

	/**
	 * @return mixed
	 */
	public function getLeafletPageCoordinates()
	{
		return $this->leafletPageCoordinates;
	}

	/**
	 * @param mixed $leafletPageCoordinates
	 */
	public function setLeafletPageCoordinates($leafletPageCoordinates): void
	{
		$this->leafletPageCoordinates = $leafletPageCoordinates;
	}

	/**
	 * @return string
	 */
	public function getDiscountType(): string
	{
		return $this->discountType;
	}

	/**
	 * @param string $discountType
	 */
	public function setDiscountType(string $discountType): void
	{
		$this->discountType = $discountType;
	}

	/**
	 * @return mixed
	 */
	public function getDiscountAmount()
	{
		return $this->discountAmount;
	}

	/**
	 * @param mixed $discountAmount
	 */
	public function setDiscountAmount($discountAmount): void
	{
		$this->discountAmount = $discountAmount;
	}

	/**
	 * @return mixed
	 */
	public function getCode()
	{
		return $this->code;
	}

	/**
	 * @param mixed $code
	 */
	public function setCode($code): void
	{
		$this->code = $code;
	}

	/**
	 * @return mixed
	 */
	public function getCurrentPrice()
	{
		return $this->currentPrice;
	}

	/**
	 * @param mixed $currentPrice
	 */
	public function setCurrentPrice($currentPrice): void
	{
		$this->currentPrice = $currentPrice;
	}

	/**
	 * @return mixed
	 */
	public function getCommonPrice()
	{
		return $this->commonPrice;
	}

	/**
	 * @param mixed $commonPrice
	 */
	public function setCommonPrice($commonPrice): void
	{
		$this->commonPrice = $commonPrice;
	}

	/**
	 * @return mixed
	 */
	public function getExitUrl()
	{
		return $this->exitUrl;
	}

	/**
	 * @param mixed $exitUrl
	 */
	public function setExitUrl($exitUrl): void
	{
		$this->exitUrl = $exitUrl;
	}

	/**
	 * @return int
	 */
	public function getPriorityLevel(): int
	{
		return $this->priorityLevel;
	}

	/**
	 * @param int $priorityLevel
	 */
	public function setPriorityLevel(int $priorityLevel): void
	{
		$this->priorityLevel = $priorityLevel;
	}

	/**
	 * @return DateTime
	 */
	public function getValidSince(): DateTime
	{
		return $this->validSince;
	}

	/**
	 * @param DateTime $validSince
	 */
	public function setValidSince(DateTime $validSince): void
	{
		$this->validSince = $validSince;
	}

	/**
	 * @return DateTime
	 */
	public function getValidTill(): DateTime
	{
		return $this->validTill;
	}

	/**
	 * @param DateTime $validTill
	 */
	public function setValidTill(DateTime $validTill): void
	{
		$this->validTill = $validTill;
	}

	/**
	 * @return mixed
	 */
	public function getAuthor()
	{
		return $this->author;
	}

	/**
	 * @param mixed $author
	 */
	public function setAuthor($author): void
	{
		$this->author = $author;
	}

	/**
	 * @return mixed
	 */
	public function getImageUrl()
	{
		return $this->imageUrl;
	}

	/**
	 * @param mixed $imageUrl
	 */
	public function setImageUrl($imageUrl): void
	{
		$this->imageUrl = $imageUrl;
	}

	public function confirm()
	{
		$this->confirmedAt = new DateTime();
	}

	public function archive()
	{
		$this->archivedAt = new DateTime();
	}

	public function waitingToConfirm(): bool
	{
		return $this->archivedAt === null && $this->confirmedAt === null;
	}

	/**
	 * @return mixed
	 */
	public function getOcrOutput()
	{
		return $this->ocrOutput;
	}

	/**
	 * @param mixed $ocrOutput
	 */
	public function setOcrOutput($ocrOutput): void
	{
		$this->ocrOutput = $ocrOutput;
	}

	/**
	 * @return mixed
	 */
	public function getTimeToCreate()
	{
		return $this->timeToCreate;
	}

	/**
	 * @param mixed $timeToCreate
	 */
	public function setTimeToCreate($timeToCreate): void
	{
		$this->timeToCreate = $timeToCreate;
	}

	/**
	 * @param string $slug
	 */
	public function setSlug(string $slug): void
	{
		$this->slug = $slug;
	}

	/**
	 * @return bool
	 */
	public function isExpired(): bool
	{
		return $this->validTill <= new DateTime();
	}

	/**
	 * @return mixed
	 */
	public function getTags()
	{
		return $this->tags;
	}

	public function getFirstTag()
	{
		return $this->tags->first();
	}

	public function hasTag(Tag $tag)
	{
		return $this->tags->contains($tag);
	}

	public function addTag(Tag $tag)
	{
		if (!$this->hasTag($tag)) {
			$this->tags->add($tag);
		}
	}

	public function clearTags()
	{
		$this->tags->clear();
	}

	public function isCoupon()
	{
		return $this->type === self::TYPE_COUPON;
	}

	/**
	 * @return mixed
	 */
	public function getProduct()
	{
		return $this->product;
	}

	/**
	 * @param mixed $product
	 */
	public function setProduct($product): void
	{
		$this->product = $product;
	}

	/**
	 * @param mixed $offerId
	 */
	public function setOfferId($offerId): void
	{
		$this->offerId = $offerId;
	}

	public function getPercentageDiscount(): float
	{
		if ($this->getCommonPrice() == 0) {
			return 0;
		}

		return round((($this->getCommonPrice() - $this->getCurrentPrice()) / $this->getCommonPrice()) * 100);
	}

	public function isInStock(): ?bool
	{
		return $this->inStock;
	}

	public function setInStock(bool $inStock): void
	{
		$this->inStock = $inStock;
	}

	public function isFutureOffer(): bool
	{
		return $this->validSince > new DateTime();
	}

	public function getValidTillDays()
	{
		$now = new DateTime();
		$validTill = (clone $this->validTill)->setTime(23, 59);
		$diff = $validTill->diff($now);
		return $diff->days;
	}

	public function getValidSinceDays()
	{
		$now = new DateTime();
		$validSince = (clone $this->validSince)->setTime(23, 59);
		$diff = $validSince->diff($now);
		return $diff->days;
	}

	public function remove(): void
	{
		$this->removedAt = new DateTime();
	}

	public function unArchive(): void
	{
		$this->archivedAt = null;
	}

	public function isArchived(): bool
	{
		return $this->archivedAt !== null;
	}

	public function getArchivedAt()
	{
		return $this->archivedAt;
	}
}

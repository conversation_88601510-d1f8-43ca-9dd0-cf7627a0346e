<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Offers;

use <PERSON><PERSON>ino\Model\EntityManager;
use <PERSON><PERSON>ino\Model\Images\ImageStorage;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Offers\Entities\Offer;

class OfferManager
{
	/** @var EntityManager */
	private $em;

	/** @var ImageStorage */
	private $imageStorage;

	public function __construct(EntityManager $em, ImageStorage $imageStorage)
	{
		$this->em = $em;
		$this->imageStorage = $imageStorage;
	}

	public function createOffer(Localization $localization, string $name, string $slug, $shop, string $type, string $discountType, \DateTime $validSince, \DateTime $validTill): Offer
	{
		$offer = new Offer($localization, $name, $slug, $shop, $type, $discountType, $validSince, $validTill);

		return $this->saveOffer($offer);
	}

	public function saveOffer(Offer $offer): Offer
	{
		$this->em->persist($offer);
		$this->em->flush();

		return $offer;
	}

	public function saveOfferImage(Offer $offer, \Nette\Utils\Image $image, $imageType)
	{
		$oldImage = $offer->getImageUrl();
		$newImage = $this->imageStorage->saveImagev2($image, $imageType, ImageStorage::NAMESPACE_OFFER_IMAGE, rand(500, 1000), $offer->getSlug());
		$offer->setImageUrl($newImage);

		if ($oldImage != $newImage) {
			$this->imageStorage->removeImage($oldImage);
		}

		$this->saveOffer($offer);
	}
}

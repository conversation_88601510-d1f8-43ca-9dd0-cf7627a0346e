<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Offers;

use Doctrine\ORM\Query;
use Doctrine\ORM\QueryBuilder;
use Ka<PERSON>ino\Model\EntityManager;
use <PERSON><PERSON>ino\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\Offers\Entities\Offer;
use Ka<PERSON>ino\Model\Offers\Repositories\OfferRepository;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Tags\Entities\Tag;
use Nette\Utils\Image;
use Nette\Utils\Strings;
use Tracy\Debugger;

class OfferFacade
{
	/** @var EntityManager */
	private $em;

	/** @var OfferManager */
	private $offerManager;

	/** @var OfferRepository */
	private $offerRepository;

	public function __construct(EntityManager $em, OfferManager $offerManager, OfferRepository $offerRepository)
	{
		$this->em = $em;
		$this->offerRepository = $offerRepository;
		$this->offerManager = $offerManager;
	}

	public function createOffer(Localization $localization, string $name, string $slug, Shop $shop, string $type, string $discountType, \DateTime $validSince, \DateTime $validTill): Offer
	{
		return $this->offerManager->createOffer($localization, $name, $slug, $shop, $type, $discountType, $validSince, $validTill);
	}

	public function saveOffer(Offer $offer)
	{
		return $this->offerManager->saveOffer($offer);
	}

	public function getOffers(Localization $localization = null, $current = true): QueryBuilder
	{
		return $this->offerRepository->getOffers($localization, $current);
	}

	public function findOffer($id): ?Offer
	{
		/** @var ?Offer $offer */
		$offer = $this->offerRepository->find($id);

		return $offer;
	}

	public function findOfferBySlug(Localization $localization, string $slug): ?Offer
	{
		return $this->offerRepository->findOfferBySlug($localization, $slug);
	}

	public function findOfferByOfferId($offerId): ?Offer
	{
		return $this->offerRepository->findOneBy(['offerId' => $offerId]);
	}

	public function findOffers(Localization $localization, int $limit = null): array
	{
		return $this->offerRepository->findBy(['localization' => $localization], ['id' => 'DESC'], $limit);
	}

	public function findTopOffers(Localization $localization, int $limit = null, ?string $type = null, bool $withLeafletPage = false, ?array $exceptOffers = [], ?Tag $tag = null): array
	{
		return $this->offerRepository->findTopOffers($localization, $limit, $type, null, $withLeafletPage, $exceptOffers, $tag)
			->getResult();
	}

	public function findTopOffersWithLeafletPage(Localization $localization, $limit = null, ?array $shops = null, ?string $type = null)
	{
		return $this->offerRepository
			->findTopOffers($localization, $limit, $type, $shops, true)
			->getResult();
	}

	public function findTopCoupons(Localization $localization, ?int $limit = null): array
	{
		return $this->offerRepository->findTopCoupons($localization, $limit);
	}

	public function findTopOfferByShop(Shop $shop, ?string $discountType = null, string $type = null): ?Offer
	{
		return $this->offerRepository->findTopOfferByShop($shop, $discountType, $type);
	}

	public function saveOfferImage(Offer $offer, Image $image, $imageType)
	{
		$this->offerManager->saveOfferImage($offer, $image, $imageType);
	}

	public function findOffersToConfirm($limit = 3, ?Localization $localization = null): array
	{
		$query = $this->offerRepository->findOffersToConfirm($localization);
		$query->setMaxResults($limit);

		return $query->getResult();
	}

	public function getCountOfOffersToConfirm(?Localization $localization = null): int
	{
		return $this->offerRepository->getCountOfOffersToConfirm($localization);
	}

	public function findOfferToAssignProduct(Localization $localization): ?Offer
	{
		$query = $this->offerRepository->findOfferToAssignProduct($localization);

		return $query->getOneOrNullResult();
	}

	public function getCountOfOffersToAssignProduct(Localization $localization): int
	{
		return $this->offerRepository->getCountOfOffersToAssignProduct($localization);
	}

	public function findOffersByShop(Shop $shop, ?int $limit = 10, $onlyCurrent = true, $type = null): array
	{
		$query = $this->offerRepository->getOffersByShop($shop, $onlyCurrent, $type);

		if ($limit) {
			$query->setMaxResults($limit);
		}

		return $query->getResult();
	}

	public function findOffersByShops(array $shops, ?int $limit = 10, $onlyCurrent = true, $type = null): array
	{
		$query = $this->offerRepository->getOffersByShops($shops, $onlyCurrent, $type);

		if ($limit) {
			$query->setMaxResults($limit);
		}

		return $query->getResult();
	}

	public function findOffersByLeaflet(Leaflet $leaflet, ?int $limit = 10, $onlyCurrent = true, $type = null)
	{
		return $this->offerRepository->findOffersByLeaflet($leaflet, $limit, $onlyCurrent, $type);
	}

	public function findProducts(Localization $localization, int $limit = 10): array
	{
		$query = $this->offerRepository->findProducts($localization)
			->setMaxResults($limit);

		return $query->getResult();
	}

	public function findOffersByFulltext($keyword, Localization $localization, $current = true, ?int $limit = null, bool $onlyWithLeafletPage = false, string $websiteType = null)
	{
		$query = $this->offerRepository->findOffersByFulltext($keyword, $localization, $current, $onlyWithLeafletPage, $websiteType);

		if ($limit) {
			$query->setMaxResults($limit);
		}

		return $query->getResult();
	}

	public function findCurrentOffersByFulltext($keyword, Localization $localization, ?string $websiteType = null)
	{
		return $this->offerRepository->findCurrentOffersByFulltext($keyword, $localization, $websiteType);
	}

	public function findFutureOffersByFulltext($keyword, Localization $localization, ?string $websiteType = null)
	{
		return $this->offerRepository->findFutureOffersByFulltext($keyword, $localization, $websiteType);
	}

	public function findTopCouponOffers(Localization $localization, int $limit = 30, int $offset = 0)
	{
		return $this->offerRepository->findTopCouponOffers($localization, $limit, $offset);
	}

	public function findTopOffersByShops(Localization $localization, array $shops, int $limit): array
	{
		$offers = [];

		$limitPerShop = $limit;

		if (count($shops) !== 0) {
			$limitPerShop = (int) ceil($limit / count($shops));
		}

		foreach ($shops as $shop) {
			$shopOffers = $this->findTopOffersWithLeafletPage($shop->getLocalization(), $limitPerShop, [$shop]);
			$offers = array_merge($offers, $shopOffers);
		}

		if (count($offers) < $limit) {
			$offers = array_merge($offers, $this->findTopOffers($localization, $limit - count($offers), null, true, $offers));
		}

		return $offers;
	}

	public function findTopOffersByTags(Localization $localization, array $tags, int $limitPerTag = 3, int $limitTotal = 100): array
	{
		$offers = [];

		foreach ($tags as $tag) {
			$tagOffers = $this->offerRepository->findTopOffers($localization, $limitPerTag, Offer::TYPE_LEAFLET, null, true, [], $tag)
				->getResult();

			$offers = array_merge($offers, $tagOffers);
		}

		if (count($offers) < $limitTotal) {
			$offers = array_merge($offers, $this->findTopOffers($localization, $limitTotal - count($offers), Offer::TYPE_LEAFLET, true, $offers));
		}

		return array_slice($offers, 0, $limitTotal);
	}

	public function findCountOfOffersToAnnotateByLocalization()
	{
		return $this->offerRepository->findCountOfOffersToAnnotateByLocalization();
	}

	public function findOffersToRemove()
	{
		return $this->offerRepository->findOffersToRemove();
	}

	public function findAvgPriceFromOffers(string $keyword, Localization $localization, ?string $websiteType = null): array
	{
		return $this->offerRepository->findAvgPriceFromOffers($keyword, $localization, $websiteType);
	}

	public function findAvgPriceByFulltext(string $keyword, Localization $localization, ?string $websiteType = null)
	{
		return $this->offerRepository->findAvgDiscountFromOffers($keyword, $localization, $websiteType);
	}

	public function findCountOfOffersByShop(string $keyword, Localization $localization, ?string $websiteType = null)
	{
		return $this->offerRepository->findCountOfOffersByShop($keyword, $localization, $websiteType);
	}
}

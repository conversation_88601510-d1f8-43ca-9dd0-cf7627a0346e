<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Articles;

use Doctrine\ORM\QueryBuilder;
use Kaufino\Model\Articles\Entities\Article;
use Ka<PERSON>ino\Model\Articles\Repositories\ArticleRepository;
use <PERSON><PERSON><PERSON>\Model\EntityManager;
use Ka<PERSON>ino\Model\Geo\Entities\City;
use Kaufino\Model\Localization\Entities\Localization;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Repositories\ShopRepository;
use Kaufino\Model\Tags\Entities\Tag;
use Kaufino\Model\Users\Entities\User;
use Ka<PERSON>ino\Model\Websites\Entities\Website;
use Nette\Utils\Strings;
use Tracy\Debugger;

class ArticleFacade
{
	/** @var EntityManager */
	private $em;

	/** @var ArticleManager */
	private $articleManager;

	/** @var ArticleRepository */
	private $articleRepository;

	public function __construct(EntityManager $em, ArticleManager $articleManager, ArticleRepository $articleRepository)
	{
		$this->em = $em;
		$this->articleRepository = $articleRepository;
		$this->articleManager = $articleManager;
	}

	public function createArticle(Website $website, string $name, string $slug): Article
	{
		return $this->articleManager->createArticle($website, $name, $slug);
	}

	public function saveArticle(Article $article)
	{
		return $this->articleManager->saveArticle($article);
	}

	public function getArticles(Website $website = null): QueryBuilder
	{
		return $this->articleRepository->getArticles($website);
	}

	public function findArticle($id): ?Article
	{
		/** @var ?Article $article */
		$article = $this->articleRepository->find($id);

		return $article;
	}

	public function findArticleBySlug(Website $website, string $slug): ?Article
	{
		return $this->articleRepository->findArticleBySlug($website, $slug);
	}

	public function findArticles(Website $website, ?int $limit = null, ?Article $exceptArticle = null): array
	{
		return $this->articleRepository->findArticles($website, $limit, $exceptArticle);
	}

	public function findPreviousArticle(Article $article)
	{
		return $this->articleRepository->findPreviousArticle($article);
	}

	public function findNextArticle(Article $article)
	{
		return $this->articleRepository->findNextArticle($article);
	}

	public function findArticlesByWebsiteAndShops(Website $website, array $shops, ?int $limit = null, ?Article $exceptArticle = null): array
	{
		return $this->articleRepository->findArticlesByWebsiteAndShops($website, $shops, $limit, $exceptArticle);
	}

	public function findArticlesByAuthor(User $author, Website $website, ?int $limit = null): array
	{
		return $this->articleRepository->findArticlesByAuthor($author, $website, $limit);
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Articles\Repositories;

use Doctrine\ORM\QueryBuilder;
use Kaufino\Model\Articles\Entities\Article;
use Kaufino\Model\EntityRepository;
use Ka<PERSON>ino\Model\Users\Entities\User;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

class ArticleRepository extends EntityRepository
{
	public function getArticles(Website $website = null): QueryBuilder
	{
		$qb = $this->createQueryBuilder('a')
			->addSelect('author')
			->leftJoin('a.author', 'author')
		;

		if ($website) {
			$qb->andWhere('a.website = :website')
				->setParameter('website', $website);
		}

		return $qb;
	}

	public function findArticleBySlug(Website $website, string $slug)
	{
		$qb = $this->getArticles($website)
			->andWhere('a.slug = :slug')
			->setParameter('slug', $slug);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findPreviousArticle(Article $article)
	{
		$qb = $this->getArticles($article->getWebsite())
			->andWhere('a.publishedAt <  :articlePublishedAt')
			->andWhere('a.active = 1')
			->setParameter('articlePublishedAt', $article->getPublishedAt())
			->addOrderBy('a.publishedAt', 'DESC')
			->setFirstResult(0)
			->setMaxResults(1);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findNextArticle(Article $article)
	{
		$qb = $this->getArticles($article->getWebsite())
			->andWhere('a.publishedAt >  :articlePublishedAt')
			->andWhere('a.active = 1')
			->setParameter('articlePublishedAt', $article->getPublishedAt())
			->addOrderBy('a.publishedAt', 'ASC')
			->setFirstResult(0)
			->setMaxResults(1);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findArticlesByWebsiteAndShops(Website $website, array $shops, int $limit = 10, ?Article $exceptArticle = null)
	{
		$qb = $this->getArticles($website)
			->innerJoin('a.shops', 's')
			->andWhere('s IN (:shops)')
			->setParameter('shops', $shops)
			->addOrderBy('a.publishedAt', 'DESC')
			->andWhere('a.publishedAt <= :now')
			->setParameter('now', new \DateTime())
			->setMaxResults($limit)
		;

		if ($exceptArticle) {
			$qb->andWhere('a.id != :exceptArticleId')
				->setParameter('exceptArticleId', $exceptArticle->getId());
		}

		return $qb->getQuery()->getResult();
	}

	public function findArticles(Website $website, ?int $limit = null, ?Article $exceptArticle = null): array
	{
		$qb = $this->getArticles($website)
			->andWhere('a.active = 1')
			->andWhere('a.publishedAt <= :now')
			->setParameter('now', new \DateTime())
			->addOrderBy('a.publishedAt', 'DESC')
		;

		if ($exceptArticle) {
			$qb->andWhere('a.id != :exceptArticleId')
				->setParameter('exceptArticleId', $exceptArticle->getId());
		}

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		return $qb->getQuery()->getResult();
	}

	public function findArticlesByAuthor(User $author, Website $website, ?int $limit = null): array
	{
		$qb = $this->getArticles($website)
			->andWhere('a.author = :author')
			->setParameter('author', $author)
			->andWhere('a.active = 1')
			->andWhere('a.publishedAt <= :now')
			->setParameter('now', new \DateTime())
			->addOrderBy('a.publishedAt', 'DESC')
		;

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		return $qb->getQuery()->getResult();
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Articles\Entities;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Kaufino\Model\Users\Entities\User;
use Kaufino\Model\Websites\Entities\Website;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Articles\Repositories\ArticleRepository")
 * @ORM\Table(name="kaufino_articles_article", uniqueConstraints={
 *     @ORM\UniqueConstraint(name="article_unique", columns={"website_id", "slug"})}
 * )
 */
class Article
{
	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Websites\Entities\Website")
	 * @ORM\JoinColumn(name="website_id", referencedColumnName="id")
	 */
	protected $website;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Users\Entities\User", inversedBy="articles")
	 * @ORM\JoinColumn(name="author_id", referencedColumnName="id")
	 */
	protected $author;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $slug;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $imageUrl;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $shortDescription;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $content;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $active = false;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $publishedAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	/**
	 * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Shops\Entities\Shop", inversedBy="articles")
	 * @ORM\JoinTable(name="kaufino_articles_article_shop")
	 */
	protected $shops;

	public function __construct(Website $website, string $name, string $slug, ?User $author = null)
	{
		$this->website = $website;
		$this->name = $name;
		$this->slug = $slug;
		$this->author = $author;

		$this->createdAt = new \DateTime();
		$this->publishedAt = new \DateTime();

		$this->shops = new ArrayCollection();
	}

	/**
	 * @return int
	 */
	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * @return Website
	 */
	public function getWebsite(): Website
	{
		return $this->website;
	}

	/**
	 * @param Website $website
	 */
	public function setWebsite(Website $website): void
	{
		$this->website = $website;
	}

	/**
	 * @return string
	 */
	public function getName(): string
	{
		return $this->name;
	}

	/**
	 * @param string $name
	 */
	public function setName(string $name): void
	{
		$this->name = $name;
	}

	/**
	 * @return string
	 */
	public function getSlug(): string
	{
		return $this->slug;
	}

	/**
	 * @param string $slug
	 */
	public function setSlug(string $slug): void
	{
		$this->slug = $slug;
	}

	public function getImageUrl(): ?string
	{
		return $this->imageUrl;
	}

	/**
	 * @param string $imageUrl
	 */
	public function setImageUrl(string $imageUrl): void
	{
		$this->imageUrl = $imageUrl;
	}

	/**
	 * @return mixed
	 */
	public function getShortDescription()
	{
		return $this->shortDescription;
	}

	/**
	 * @param mixed $shortDescription
	 */
	public function setShortDescription($shortDescription): void
	{
		$this->shortDescription = $shortDescription;
	}

	/**
	 * @return mixed
	 */
	public function getContent()
	{
		return $this->content;
	}

	/**
	 * @param mixed $content
	 */
	public function setContent($content): void
	{
		$this->content = $content;
	}

	/**
	 * @return bool
	 */
	public function isActive(): bool
	{
		return $this->active;
	}

	/**
	 * @param bool $active
	 */
	public function setActive(bool $active): void
	{
		$this->active = $active;
	}

	/**
	 * @return mixed
	 */
	public function getPublishedAt()
	{
		return $this->publishedAt;
	}

	/**
	 * @param mixed $publishedAt
	 */
	public function setPublishedAt($publishedAt): void
	{
		$this->publishedAt = $publishedAt;
	}

	/**
	 * @return mixed
	 */
	public function getCreatedAt()
	{
		return $this->createdAt;
	}

	/**
	 * @param mixed $createdAt
	 */
	public function setCreatedAt($createdAt): void
	{
		$this->createdAt = $createdAt;
	}

	public function setShops(array $shops)
	{
		$this->shops->clear();

		foreach ($shops as $shop) {
			$this->shops->add($shop);
		}
	}

	public function getShops(): Collection
	{
		return $this->shops;
	}

	public function getAuthor(): ?User
	{
		return $this->author;
	}

	public function setAuthor(?User $author): void
	{
		$this->author = $author;
	}
}

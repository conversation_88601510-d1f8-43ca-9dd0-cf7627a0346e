<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Articles;

use <PERSON><PERSON><PERSON>\Model\Articles\Entities\Article;
use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\ResponseCacheManager;
use <PERSON><PERSON>ino\Model\Websites\Entities\Website;

class ArticleManager
{
	/** @var EntityManager */
	private $em;

	/** @var ResponseCacheManager */
	private $responseCacheManager;

	public function __construct(EntityManager $em, ResponseCacheManager $responseCacheManager)
	{
		$this->em = $em;
		$this->responseCacheManager = $responseCacheManager;
	}

	public function createArticle(Website $website, string $name, string $slug): Article
	{
		$article = new Article($website, $name, $slug);

		return $this->saveArticle($article);
	}

	public function saveArticle(Article $article): Article
	{
		$this->em->persist($article);
		$this->em->flush();

		$this->responseCacheManager->clearCacheByTag('articles');

		return $article;
	}
}

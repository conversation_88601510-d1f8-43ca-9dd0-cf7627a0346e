<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Geo\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Entities\Store;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Geo\Repositories\CityRepository")
 * @ORM\Table(name="kaufino_geo_city", uniqueConstraints={
 *     @ORM\UniqueConstraint(name="city_unique", columns={"localization_id", "slug"})}
 * )
 */
class City
{
	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

	/**
	 * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Shops\Entities\Shop", mappedBy="cities")
	 */
	protected $shops;

	/**
	 * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Leaflets\Entities\Leaflet", mappedBy="cities")
	 */
	protected $leaflets;

	/**
	 * @ORM\Column(type="integer", unique=true, nullable=true)
	 */
	protected $cityId;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $slug;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $district;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $lat;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $lng;

	/**
	 * @ORM\Column(type="integer")
	 */
	protected $population = 0;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $active = false;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $activeBrandsKaufino = false;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $activeBrandsOferto = false;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected bool $noIndex = false;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $activatedAt;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $activeOferto = false;

	/**
	 * @ORM\OneToMany(targetEntity="Kaufino\Model\Shops\Entities\Store", mappedBy="city", cascade={"persist"})
	 * @var Store[]|Collection
	 */
	private $stores;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $activatedOfertoAt;

	public function __construct(Localization $localization, string $name, string $slug, $cityId, ?string $district, ?string $lat, ?string $lng, $population)
	{
		$this->localization = $localization;
		$this->name = $name;
		$this->slug = $slug;
		$this->cityId = $cityId;
		$this->district = $district;
		$this->lat = $lat;
		$this->lng = $lng;
		$this->population = $population;

		$this->shops = new ArrayCollection();
		$this->leaflets = new ArrayCollection();
		$this->stores = new ArrayCollection();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getLocalization(): Localization
	{
		return $this->localization;
	}

	public function getShops()
	{
		return $this->shops;
	}

	public function getLeaflets()
	{
		return $this->leaflets;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function getSlug(): string
	{
		return $this->slug;
	}

	public function getDistrict(): ?string
	{
		return $this->district;
	}

	public function getLat(): string
	{
		return $this->lat;
	}

	public function getLng(): string
	{
		return $this->lng;
	}

	public function getPopulation(): int
	{
		return $this->population;
	}

	public function isActive(): bool
	{
		return $this->active;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function setDistrict(?string $district): void
	{
		$this->district = $district;
	}

	public function setLat(string $lat): void
	{
		$this->lat = $lat;
	}

	public function setLng(string $lng): void
	{
		$this->lng = $lng;
	}

	public function setPopulation(int $population): void
	{
		$this->population = $population;
	}

	/**
	 * @return string
	 */
	public function getCityId()
	{
		return $this->cityId;
	}

	public function isActiveOferto(): bool
	{
		return $this->activeOferto;
	}

	public function activate(): void
	{
		$this->active = true;
		$this->activatedAt = new DateTime();
	}

	public function activateOferto(): void
	{
		$this->activeOferto = true;
		$this->activatedOfertoAt = new DateTime();
	}

	public function deactivate(): void
	{
		$this->active = false;
		$this->activatedAt = null;
	}

	public function deactivateOferto(): void
	{
		$this->activeOferto = false;
		$this->activatedOfertoAt = null;
	}

	public function getActivatedAt(): ?DateTime
	{
		return $this->activatedAt;
	}

	public function getActivatedOfertoAt(): ?DateTime
	{
		return $this->activatedOfertoAt;
	}

	public function isActiveBrandsKaufino(): bool
	{
		return $this->activeBrandsKaufino;
	}

	public function isActiveBrands(string $websiteType): bool
	{
		if ($websiteType === 'kaufino') {
			return $this->activeBrandsKaufino;
		} elseif ($websiteType === 'oferto') {
			return $this->activeBrandsOferto;
		}

		return false;
	}

	public function isActiveBrandsOferto(): bool
	{
		return $this->activeBrandsOferto;
	}

	public function activateBrandsKaufino(): void
	{
		$this->activeBrandsKaufino = true;
	}

	public function activateBrandsOferto(): void
	{
		$this->activeBrandsOferto = true;
	}

	public function deactivateBrandsKaufino(): void
	{
		$this->activeBrandsKaufino = false;
	}

	public function deactivateBrandsOferto(): void
	{
		$this->activeBrandsOferto = false;
	}

	public function getStores()
	{
		return $this->stores;
	}

	public function isNoIndex(): bool
	{
		return $this->noIndex;
	}
}

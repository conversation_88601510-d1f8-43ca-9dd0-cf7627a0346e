<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Geo;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON>ino\Model\Geo\Entities\City;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Geo\Repositories\CityRepository;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Entities\Store;
use Kaufino\Model\Shops\Repositories\StoreRepository;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

class GeoFacade
{
	/** @var EntityManager */
	private $em;

	/** @var GeoManager */
	private $geoManager;

	/** @var CityRepository */
	private $cityRepository;

	/** @var StoreRepository */
	private $storeRepository;

	public function __construct(EntityManager $em, GeoManager $geoManager, CityRepository $cityRepository, StoreRepository $storeRepository)
	{
		$this->em = $em;
		$this->cityRepository = $cityRepository;
		$this->geoManager = $geoManager;
		$this->storeRepository = $storeRepository;
	}

	public function createCity(Localization $localization, string $name, string $slug, $cityId, string $district, string $lat, string $lng, $population): City
	{
		return $this->geoManager->createCity($localization, $name, $slug, $cityId, $district, $lat, $lng, $population);
	}

	public function saveCity(City $city)
	{
		return $this->geoManager->saveCIty($city);
	}

	public function getCities(Localization $localization = null): QueryBuilder
	{
		return $this->cityRepository->getCities($localization);
	}

	public function findCity($id): ?City
	{
		/** @var ?City $city */
		return $this->cityRepository->find($id);
	}

	public function findCityBySlug(Localization $localization, string $slug): ?City
	{
		return $this->cityRepository->findCityBySlug($localization, $slug);
	}

	public function findCityByCityId($cityId): ?City
	{
		/** @var ?City $city */
		return $this->cityRepository->findOneBy(['cityId' => $cityId]);
	}

	public function findCities(Localization $localization, ?int $limit = 10, ?string $websiteType = null)
	{
		return $this->cityRepository->findCities($localization, $limit, $websiteType);
	}

	public function findCitiesByShop(Shop $shop, int $limit = 10, ?string $websiteType = null)
	{
		return $this->cityRepository->findCitiesByShop($shop, $limit, $websiteType);
	}

	public function findNearestCitiesByCity(City $city, int $limit = 10, ?string $websiteType = null)
	{
		return $this->cityRepository->findNearestCitiesByCity($city, $limit, $websiteType);
	}

	public function findCitiesByShops(array $shops, ?int $limit = 10, ?string $websiteType = null)
	{
		return $this->cityRepository->findCitiesByShops($shops, $limit, $websiteType);
	}

	public function findCitiesToActivate(Localization $localization, string $websiteType, int $limit)
	{
		return $this->cityRepository->findCitiesToActivate($localization, $websiteType, $limit);
	}

	public function getStores(): QueryBuilder
	{
		return $this->storeRepository->getStores();
	}

	public function findStore(int $storeId)
	{
		return $this->storeRepository->find($storeId);
	}

	public function saveStore($store): Store
	{
		return $this->geoManager->saveStore($store);
	}

	public function findStores(Localization $localization, ?int $limit = 10, ?string $websiteType = null)
	{
		return $this->storeRepository->findStores($localization, $limit, $websiteType);
	}

	public function findStoresByCity(City $city, int $limit = 10)
	{
		return $this->storeRepository->findStoresByCity($city, $limit);
	}

	public function findStoresByShop(Shop $shop, ?int $limit = 10, ?string $websiteType = null)
	{
		return $this->storeRepository->findStoresByShop($shop, $limit, $websiteType);
	}

	public function findStoresByShopAndCity(Shop $shop, City $city, int $limit = 10, string $websiteType = Website::MODULE_KAUFINO)
	{
		return $this->storeRepository->findStoresByShopAndCity($shop, $city, $limit, $websiteType);
	}

	public function findStoresToActivate(Localization $localization, int $limit = 50)
	{
		return $this->storeRepository->findStoresToActivate($localization, $limit);
	}

	public function findStoresToActivateOferto(Localization $localization, int $limit = 50)
	{
		return $this->storeRepository->findStoresToActivateOferto($localization, $limit);
	}

	public function findCitiesToActivateBrands(Localization $localization, string $websiteType, int $limit)
	{
		return $this->cityRepository->findCitiesToActivateBrands($localization, $websiteType, $limit);
	}

	public function findCitiesByLocalization(Localization $localization)
	{
		return $this->cityRepository->findCitiesByLocalization($localization);
	}

	public function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
	{
		$theta = $lon1 - $lon2;
		$dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) +  cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
		$dist = acos($dist);
		$dist = rad2deg($dist);
		$miles = $dist * 60 * 1.1515;
		return floor(($miles * 1.609344) * 10) / 10;
	}

	public function findClosestCityId(Localization $localization, float $lat, float $lon)
	{
		return $this->cityRepository->findClosestCityId($localization, $lat, $lon);
	}

	public function findActiveCities(Localization $localization, string $websiteType)
	{
		return $this->cityRepository->findActiveCities($localization, $websiteType);
	}

	public function findTopCityByPopulation(Localization $localization): ?City
	{
		return $this->cityRepository->findTopCityByPopulation($localization);
	}
}

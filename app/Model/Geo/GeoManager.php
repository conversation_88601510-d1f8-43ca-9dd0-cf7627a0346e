<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Geo;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON>ino\Model\Geo\Entities\City;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\Shops\Entities\Store;

class GeoManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function createCity(Localization $localization, $name, $slug, $cityId, $district, $lat, $lng, $population): City
	{
		$city = new City($localization, $name, $slug, $cityId, $district, $lat, $lng, $population);

		return $this->saveCity($city);
	}

	public function saveCity(City $city): City
	{
		$this->em->persist($city);
		$this->em->flush();

		return $city;
	}

	public function saveStore(Store $store): Store
	{
		$this->em->persist($store);
		$this->em->flush();

		return $store;
	}
}

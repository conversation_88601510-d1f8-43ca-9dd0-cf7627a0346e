<?php

namespace <PERSON><PERSON><PERSON>\Model;

use Psr\Log\LoggerInterface;
use <PERSON>\Debugger;

class Logger implements LoggerInterface
{
	public function emergency($message, array $context = [])
	{
	}

	public function alert($message, array $context = [])
	{
	}

	public function critical($message, array $context = [])
	{
	}

	public function error($message, array $context = [])
	{
	}

	public function warning($message, array $context = [])
	{
	}

	public function notice($message, array $context = [])
	{
		Debugger::log($message . json_encode($context), 'error-logger');
	}

	public function info($message, array $context = [])
	{
	}

	public function debug($message, array $context = [])
	{
	}

	public function log($level, $message, array $context = [])
	{
	}
}

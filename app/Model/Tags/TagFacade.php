<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Tags;

use Codeception\Lib\Interfaces\Web;
use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Tags\Repositories\TagRepository;
use Kaufino\Model\Websites\Entities\Website;

class TagFacade
{
	/** @var TagRepository */
	private $tagRepository;

	/** @var TagManager */
	private $tagManager;

	public function __construct(TagManager $tagManager, TagRepository $tagRepository)
	{
		$this->tagManager = $tagManager;
		$this->tagRepository = $tagRepository;
	}

	public function createTag(Localization $localization, string $name, string $slug): Tag
	{
		return $this->tagManager->createTag($localization, $name, $slug);
	}

	public function saveTag(Tag $tag, bool $resetCache = true)
	{
		return $this->tagManager->saveTag($tag, $resetCache);
	}

	public function getTags(Localization $localization = null, string $type = null, bool $onlyActive = true): QueryBuilder
	{
		return $this->tagRepository->getTags($localization, $type, $onlyActive);
	}

	public function findTagBySlug(Localization $localization, string $slug, ...$withTypes): ?Tag
	{
		return $this->tagRepository->findTagBySlug($localization, $slug, $withTypes);
	}

	public function findTagsbyTag(Tag $tag, int $limit = 10): array
	{
		return $this->tagRepository->getTags()
			->andWhere('t.parentTag = :parentTagId')
			->orderBy('t.id', 'ASC')
			->setParameter('parentTagId', $tag->getId())
			->getQuery()->setMaxResults($limit)->getResult();
	}

	public function findRootTags(Localization $localization, string $type = null, int $limit = 10): array
	{
		return $this->tagRepository->getTags($localization, $type)
			->andWhere('t.parentTag IS NULL')
			->orderBy('t.id', 'ASC')
			->getQuery()->setMaxResults($limit)->getResult();
	}

	public function findTags(Localization $localization, string $type = null, ?int $limit = 10, ?Tag $parentTag = null, $exceptTag = null, ?bool $showInMenu = null, ?string $websiteType = null): array
	{
		$qb = $this->tagRepository->getTags($localization, $type);

		if ($parentTag) {
			$qb->andWhere('t.parentTag = :parentTag')
				->setParameter('parentTag', $parentTag);
		}

		if ($exceptTag) {
			if (is_array($exceptTag)) {
				$qb->andWhere('t NOT IN (:exceptTags)')
					->setParameter('exceptTags', $exceptTag);
			} else {
				$qb->andWhere('t != :exceptTag')
					->setParameter('exceptTag', $exceptTag);
			}
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('t.activeOferto = true');
		}

		if ($showInMenu !== null) {
			$qb->andWhere('t.showInMenu = :showInMenu')
				->setParameter('showInMenu', $showInMenu);
		}

		$qb->addOrderBy('t.activeTill', 'DESC');
		$qb->addOrderBy('t.priority', 'DESC');

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		return $qb
			->getQuery()
			->getResult();
	}

	public function findPairs(Localization $localization, $type = null): array
	{
		return $this->tagRepository->findPairsList($localization, $type);
	}

	public function findTag($id): ?Tag
	{
		return $this->tagRepository->find($id);
	}

	public function findTagsToUpdateStats($type = null, int $limit = 10): array
	{
		$qb = $this->tagRepository->getTags()
			->orderBy('t.updatedStatsAt', 'ASC');

		if ($type) {
			$qb->andWhere('t.type = :type')
				->setParameter('type', $type);
		}

		return $qb->getQuery()->setMaxResults($limit)->getResult();
	}

	public function findOffersTagByMatchRule(Localization $localization, string $matchRule)
	{
		return $this->tagRepository->findOffersTagByMatchRule($localization, $matchRule);
	}
}

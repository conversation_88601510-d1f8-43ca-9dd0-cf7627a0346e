<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ino\Model\Tags\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Shops\Entities\ContentBlock;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Tags\Repositories\TagRepository")
 * @ORM\Table(name="kaufino_tags_tag", uniqueConstraints={
 *      @ORM\UniqueConstraint(name="tag_unique", columns={"localization_id", "slug", "type"})}
 * )
 */
class Tag
{
	public const TYPE_SHOPS = 'shops';
	public const TYPE_REGION = 'region';
	public const TYPE_OFFERS = 'offers';
	public const TYPE_LABELS = 'labels';
	public const TYPE_MANUFACTURER = 'manufacturer';
	public const TYPE_PARENT_PRODUCT = 'parent_product';

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Tags\Entities\Tag", inversedBy="childTags")
	 * @ORM\JoinColumn(name="parent_tag_id", referencedColumnName="id")
	 */
	protected $parentTag;

	/**
	 * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Offers\Entities\Offer", mappedBy="tags")
	 */
	protected $offers;

	/**
	 * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Products\Entities\Product", mappedBy="tags")
	 */
	protected $products;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $englishName;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $slug;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $perex;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $description;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $type;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	protected $matchRule;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected $priority;

	/**
	 * @ORM\Column(type="integer")
	 */
	protected $score = 0;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected $countOfLeaflets;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected $countOfOffers;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	protected $countOfExpiredOffers;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $active = true;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $activeKaufino = false;

	/**
	 * @ORM\Column(type="boolean")
	 */
	protected $activeLetado = false;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $activeOferto = false;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $showInMenu = true;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $activeTill;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $productImageUrl;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $updatedSitemapAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $updatedStatsAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $archivedAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $createdAt;

	/**
	 * @ORM\ManyToMany(targetEntity="\Kaufino\Model\Shops\Entities\Shop", mappedBy="tags")
	 */
	protected $shops;

	/**
	 * @ORM\OneToMany(targetEntity="Tag", mappedBy="parentTag")
	 */
	protected $childTags;

	/**
	 * @ORM\OneToMany(targetEntity="\Kaufino\Model\Shops\Entities\ContentBlock", mappedBy="tag", cascade={"persist"})
	 * @var ContentBlock[]|ArrayCollection|PersistentCollection
	 */
	private $contentBlocks;

	public function __construct(Localization $localization, string $name, string $slug)
	{
		$this->localization = $localization;
		$this->name = $name;
		$this->slug = $slug;
		$this->updatedSitemapAt = new DateTime();
		$this->createdAt = new DateTime();
		$this->childTags = new ArrayCollection();
		$this->contentBlocks = new ArrayCollection();
	}

	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * @return string
	 */
	public function getName()
	{
		return $this->name;
	}

	/**
	 * @return string
	 */
	public function getSlug()
	{
		return $this->slug;
	}

	public function getUrlPath(): string
	{
		$slug = '';

		if ($parentTag = $this->getParentTag()) {
			$slug .= $parentTag->getSlug() . '/';
		}

		return $slug . $this->getSlug();
	}

	/**
	 * @return mixed
	 */
	public function getLocalization()
	{
		return $this->localization;
	}

	/**
	 * @param mixed $localization
	 */
	public function setLocalization($localization): void
	{
		$this->localization = $localization;
	}

	/**
	 * @return mixed
	 */
	public function getParentTag()
	{
		return $this->parentTag;
	}

	/**
	 * @param mixed $parentTag
	 */
	public function setParentTag($parentTag): void
	{
		$this->parentTag = $parentTag;
	}

	/**
	 * @param mixed $name
	 */
	public function setName($name): void
	{
		$this->name = $name;
	}

	/**
	 * @return mixed
	 */
	public function getEnglishName()
	{
		return $this->englishName;
	}

	/**
	 * @param mixed $englishName
	 */
	public function setEnglishName($englishName): void
	{
		$this->englishName = $englishName;
	}

	/**
	 * @param mixed $slug
	 */
	public function setSlug($slug): void
	{
		$this->slug = $slug;
	}

	/**
	 * @return mixed
	 */
	public function getPerex()
	{
		return $this->perex;
	}

	/**
	 * @param mixed $perex
	 */
	public function setPerex($perex): void
	{
		$this->perex = $perex;
	}

	/**
	 * @return mixed
	 */
	public function getDescription()
	{
		return $this->description;
	}

	/**
	 * @param mixed $description
	 */
	public function setDescription($description): void
	{
		$this->description = $description;
	}

	/**
	 * @return mixed
	 */
	public function getType()
	{
		return $this->type;
	}

	/**
	 * @param mixed $type
	 */
	public function setType($type): void
	{
		$this->type = $type;
	}

	/**
	 * @return mixed
	 */
	public function getMatchRule()
	{
		return $this->matchRule;
	}

	/**
	 * @param mixed $matchRule
	 */
	public function setMatchRule($matchRule): void
	{
		$this->matchRule = $matchRule;
	}

	/**
	 * @return mixed
	 */
	public function getPriority()
	{
		return $this->priority;
	}

	/**
	 * @param mixed $priority
	 */
	public function setPriority($priority): void
	{
		$this->priority = $priority;
	}

	public function isShopsType(): bool
	{
		return $this->type == self::TYPE_SHOPS;
	}

	public function isOffersType()
	{
		return $this->type == self::TYPE_OFFERS;
	}

	/**
	 * @param bool $active
	 */
	public function setActive(bool $active): void
	{
		$this->active = $active;
	}

	/**
	 * @return bool
	 */
	public function isActive(): bool
	{
		return $this->active;
	}

	public static function getTypes(): array
	{
		return [
			self::TYPE_SHOPS => 'Shops',
			self::TYPE_OFFERS => 'Offers',
			self::TYPE_LABELS => 'Labels',
		];
	}

	public function getUpdatedSitemapAt()
	{
		return $this->updatedSitemapAt;
	}

	public function updateSitemap(): void
	{
		$this->updatedSitemapAt = new \DateTime();
	}

	/**
	 * @param mixed $countOfLeaflets
	 */
	public function setCountOfLeaflets($countOfLeaflets): void
	{
		$this->countOfLeaflets = $countOfLeaflets;
	}

	/**
	 * @param mixed $countOfOffers
	 */
	public function setCountOfOffers($countOfOffers): void
	{
		$this->countOfOffers = $countOfOffers;
	}

	/**
	 * @param mixed $countOfExpiredOffers
	 */
	public function setCountOfExpiredOffers($countOfExpiredOffers): void
	{
		$this->countOfExpiredOffers = $countOfExpiredOffers;
	}

	public function updateStats(): void
	{
		$this->updatedStatsAt = new DateTime();
	}

	public function getScore(): int
	{
		return $this->score;
	}

	public function getProductImageUrl()
	{
		return $this->productImageUrl;
	}

	public function setProductImageUrl($productImageUrl): void
	{
		$this->productImageUrl = $productImageUrl;
	}

	public function getActiveTill(): ?DateTime
	{
		return $this->activeTill;
	}

	public function setActiveTill(?DateTime $activeTill): void
	{
		$this->activeTill = $activeTill ? $activeTill->setTime(23, 59, 59) : null;
	}

	public function isSeasonal(): bool
	{
		return $this->activeTill !== null;
	}

	public function getChildTags()
	{
		return $this->childTags;
	}

	public function isShowInMenu(): bool
	{
		return $this->showInMenu;
	}

	public function setShowInMenu(bool $showInMenu): void
	{
		$this->showInMenu = $showInMenu;
	}

	public function isActiveOferto(): bool
	{
		return $this->activeOferto;
	}

	public function setActiveOferto(bool $activeOferto): void
	{
		$this->activeOferto = $activeOferto;
	}

	public function isActiveKaufino(): bool
	{
		return $this->activeKaufino;
	}

	public function setActiveKaufino(bool $activeKaufino): void
	{
		$this->activeKaufino = $activeKaufino;
	}

	public function isActiveLetado(): bool
	{
		return $this->activeLetado;
	}

	public function setActiveLetado(bool $activeLetado): void
	{
		$this->activeLetado = $activeLetado;
	}

	/**
	 * @return ArrayCollection|PersistentCollection|ContentBlock[]
	 */
	public function getContentBlocks()
	{
		return $this->contentBlocks;
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Tags\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityRepository;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;

class TagRepository extends EntityRepository
{
	public function getTags(Localization $localization = null, string $type = null, bool $onlyActive = true): QueryBuilder
	{
		$qb = $this->createQueryBuilder('t');

		if ($localization) {
			$qb->andWhere('t.localization = :localization')
				->setParameter('localization', $localization);
		}

		if ($type) {
			$qb->andWhere('t.type = :type')
				->setParameter('type', $type);
		}

		if ($onlyActive === true) {
			$qb->andWhere('t.active = true AND (t.activeTill IS NULL OR t.activeTill > :now)')
				->setParameter('now', new \DateTime())
			;
		}

		return $qb;
	}

	public function findTagBySlug(Localization $localization, string $slug, ?array $withTypes = null)
	{
		$qb = $this->getTags($localization, null, false)
			->andWhere('t.slug = :slug')
			->setParameter('slug', $slug);

		if ($withTypes) {
			$qb->andWhere('t.type IN (:withTypes)')
				->setParameter('withTypes', $withTypes);
		}

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findPairsList(Localization $localization, $type = null): array
	{
		$list = [];

		foreach ($this->getTags($localization, $type, false)->getQuery()->getResult() as $tag) {
			$name = $tag->getName();

			if ($tag->getEnglishName()) {
				$name .= ' (' . $tag->getEnglishName() . ')';
			}

			$list[$tag->getId()] = $name;
		}

		return $list;
	}

	public function findOffersTagByMatchRule(Localization $localization, string $matchRule): ?Tag
	{
		$qb = $this->getTags($localization, Tag::TYPE_OFFERS)
			->andWhere('t.matchRule = :matchRule')
			->setParameter('matchRule', $matchRule)
			->setMaxResults(1)
		;

		return $qb->getQuery()->getOneOrNullResult();
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Translations;

use Nette\Http\FileUpload;
use Nette\Utils\Strings;
use <PERSON><PERSON><PERSON>\InvalidArgumentException;

class DictionaryConverter
{
	public const MOBILE_PLATFORM_ANDROID = 'android';
	public const MOBILE_PLATFORM_IOS = 'ios';

	/**
	 * @return string[]
	 */
	public static function getPlatformsSelectList(): array
	{
		return [
			self::MOBILE_PLATFORM_ANDROID => 'android',
			self::MOBILE_PLATFORM_IOS => 'iOS',
		];
	}

	/**
	 * @param string $platform
	 * @return string
	 */
	public static function getPlatformName(string $platform): string
	{
		if (!isset(self::getPlatformsSelectList()[$platform])) {
			throw new \InvalidArgumentException('Neznámá platforma!');
		}

		return self::getPlatformsSelectList()[$platform];
	}

	/**
	 * @return string[]
	 */
	public static function getDictionaryExtensions(): array
	{
		return [
			self::MOBILE_PLATFORM_ANDROID => 'xml',
			self::MOBILE_PLATFORM_IOS => 'strings',
		];
	}

	/**
	 * @param string $platform
	 * @return string
	 */
	public static function getDictionaryExtension(string $platform): string
	{
		if (!isset(self::getDictionaryExtensions()[$platform])) {
			throw new \InvalidArgumentException('Neznámá platforma!');
		}

		return self::getDictionaryExtensions()[$platform];
	}

	/**
	 * @return string[]
	 */
	public static function getDictionaryContentTypes(): array
	{
		return [
			self::MOBILE_PLATFORM_ANDROID => 'application/xml',
			self::MOBILE_PLATFORM_IOS => 'text/plain',
		];
	}

	/**
	 * @param string $platform
	 * @return string
	 */
	public static function getDictionaryContentType(string $platform): string
	{
		if (!isset(self::getDictionaryContentTypes()[$platform])) {
			throw new \InvalidArgumentException('Neznámá platforma!');
		}

		return self::getDictionaryContentTypes()[$platform];
	}

	/**
	 * @param string $platform
	 * @param FileUpload $sourceFile
	 * @return string[]
	 */
	public function convertMobileDictionaryToArray(string $platform, FileUpload $sourceFile): array
	{
		$sourceDictionary = $sourceFile->getContents();

		if ($platform === self::MOBILE_PLATFORM_IOS) {
			return $this->convertMobileIosDictionaryToArray($sourceDictionary);
		} elseif ($platform === self::MOBILE_PLATFORM_ANDROID) {
			return $this->convertMobileAndroidDictionaryToArray($sourceDictionary);
		} else {
			throw new \InvalidArgumentException('Neznámá platforma!');
		}
	}

	/**
	 * @param string $sourceDictionary
	 * @return string[]
	 */
	private function convertMobileIosDictionaryToArray(string $sourceDictionary): array
	{
		$result = [];
		$lines = explode("\n", $sourceDictionary);

		foreach ($lines as $line) {
			if (Strings::startsWith($line, '"') && Strings::endsWith($line, '";')) {
				$key = Strings::trim(Strings::before($line, '" = "'), Strings::TRIM_CHARACTERS . '"');
				$text = Strings::trim(Strings::after($line, '" = "'), Strings::TRIM_CHARACTERS . '";');

				$translation = &$result;
				foreach (explode('.', $key) as $code) {
					if (!is_array($translation)) {
						throw new \InvalidArgumentException('Chyba v datech - \'' . $key . '\'');
					}
					if (!isset($translation[$code])) {
						$translation[$code] = [];
					}
					$translation = &$translation[$code];
				}

				$translation = Strings::trim($text);
			}
		}

		return $result;
	}

	/**
	 * @param string $sourceDictionary
	 * @return string[]
	 */
	private function convertMobileAndroidDictionaryToArray(string $sourceDictionary): array
	{
		$result = [];
		$xml = new \SimpleXMLElement($sourceDictionary);

		foreach ($xml as $element) {
			$tagName = $element->getName();
			if ($tagName === 'string') {
				$result[(string) $element['name']] = (string) $element;
			} elseif ($tagName === 'plurals') {
				$plurals = [];
				foreach ($element->children() as $child) {
					$plurals[(string) $child['quantity']] = (string) $child;
				}

				$result[(string) $element['name']] = $plurals;
			}
		}

		return $result;
	}

	/**
	 * @param string $platform
	 * @param array $data
	 * @return string
	 */
	public function convertArrayToMobileDictionary(string $platform, array $data): string
	{
		if ($platform === self::MOBILE_PLATFORM_IOS) {
			return $this->convertArrayToMobileIosDictionary($data);
		} elseif ($platform === self::MOBILE_PLATFORM_ANDROID) {
			return $this->convertArrayToMobileAndroidDictionary($data);
		} else {
			throw new \InvalidArgumentException('Neznámá platforma!');
		}
	}

	/**
	 * @param array $data
	 * @return string
	 */
	private function convertArrayToMobileIosDictionary(array $data): string
	{
		$result = '';
		$this->createMobileIosTranslation('', $data, $result);

		return $result;
	}

	/**
	 * @param string $key
	 * @param string|array $item
	 * @param string $result
	 */
	private function createMobileIosTranslation(string $key, $item, string &$result): void
	{
		if (!is_array($item)) {
			$result .= '"' . $key . '" = "' . $item . '";' . PHP_EOL;
		} else {
			foreach ($item as $childKey => $childItem) {
				$this->createMobileIosTranslation(($key ? $key . '.' : '') . $childKey, $childItem, $result);
			}
		}
	}

	/**
	 * @param array $data
	 * @return string
	 */
	private function convertArrayToMobileAndroidDictionary(array $data): string
	{
		$result = '<resources xmlns:tools="http://schemas.android.com/tools">' . PHP_EOL;

		foreach ($data as $key => $item) {
			if (is_array($item)) {
				$result .= "\t" . '<plurals name="' . $key . '">' . PHP_EOL;
				foreach ($item as $quantityKey => $translation) {
					$result .= "\t\t" . '<item quantity="' . $quantityKey . '">' . $translation . '</item>' . PHP_EOL;
				}
				$result .= "\t" . '</plurals>' . PHP_EOL;
			} else {
				$result .= "\t" . '<string name="' . $key . '">' . $item . '</string>' . PHP_EOL;
			}
		}

		$result .= '</resource>';

		return $result;
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Model\Content;

use <PERSON><PERSON><PERSON>\Model\Images\ImageFilter;

class ContentFilter
{
	/**
	 * @var ImageFilter
	 */
	private $imageFilter;

	public function __construct(ImageFilter $imageFilter)
	{
		$this->imageFilter = $imageFilter;
	}

	public function __invoke(?string $content, ?array $params = null)
	{
		if (!$content) {
			return null;
		}

		if ($params) {
			foreach ($params as $key => $value) {
				$replacement = is_array($value) ? implode('', $value) : (string) $value;
				$content = str_replace('{' . $key . '}', $replacement, $content);
			}
		}

		// Create a new DOMDocument instance
		$dom = new \DOMDocument();
		@$dom->loadHTML('<?xml encoding="UTF-8">' . $content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

		// Find all <img> elements
		$images = $dom->getElementsByTagName('img');

		// Iterate through each <img> element
		foreach ($images as $image) {
			$src = $image->getAttribute('src');
			$alt = $image->getAttribute('alt');

			// Update the src attribute
			$image->setAttribute('src', 'https://www.kaufino.com/' . $src);
			$parent = $image->parentNode;

			// Create <picture>, <source>, and <img> elements
			$picture = $dom->createElement('picture');
			$source = $dom->createElement('source');
			$img = $dom->createElement('img');

			// Set attributes for <source>
			$source->setAttribute('srcset', $this->image($src, 785, null, 'fit', 'webp'));
			$source->setAttribute('type', 'image/webp');

			// Set attributes for <img>
			$img->setAttribute('src', $this->image($src, 785, null, 'fit', 'jpg'));
			$img->setAttribute('width', '785');
			$img->setAttribute('height', '281');
			$img->setAttribute('alt', $alt);
			$img->setAttribute('loading', 'lazy');
			$img->setAttribute('class', 'lazyload');

			// Append <source> and <img> to <picture>
			$picture->appendChild($source);
			$picture->appendChild($img);

			// Replace the original <img> with the new <picture>
			$parent->replaceChild($picture, $image);
		}

		// Return the modified HTML as a string
		return $dom->saveHTML();
	}

	private function image(string $url, $width = null, $height = null, $flag = 'fit', $extension = 'null'): string
	{
		return ($this->imageFilter)($url, $width, $height, $flag, $extension);
	}
}

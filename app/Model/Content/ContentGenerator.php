<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Content;

use Contributte\Translation\Translator;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use Nette\Application\LinkGenerator;

class ContentGenerator
{
	/**
	 * @var Translator
	 */
	private $translator;

	/**
	 * @var LinkGenerator
	 */
	private $linkGenerator;

	public function __construct(Translator $translator, LinkGenerator $linkGenerator)
	{
		$this->translator = $translator;
		$this->linkGenerator = $linkGenerator;
	}

	public function generateLeafletDescription(Leaflet $leaflet): string
	{
		return $leaflet->getName() . "let<PERSON> leták leták";
	}
}

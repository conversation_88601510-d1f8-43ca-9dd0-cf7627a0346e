<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model;

use Nette\Application\Responses\TextResponse;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Http\Request;
use Nette\Utils\Strings;

final class ResponseCacheManager
{
	/** @var Cache */
	private $cache;
	private Request $request;

	public function __construct(Storage $storage, Request $request)
	{
		$this->cache = new Cache($storage, 'response');
		$this->request = $request;
	}

	public function cacheResponse(string $cacheKey, TextResponse $response, array $tags = []): void
	{
		$this->cache->save($cacheKey, $response->getSource()->__toString(), [
			Cache::EXPIRE => '30 minutes',
			Cache::TAGS => array_merge($tags, ['response']),
		]);
	}

	public function loadResponseFromCache(string $cacheKey): ?TextResponse
	{
		$responseHtml = $this->cache->load($cacheKey);

		if ($responseHtml !== null) {
			$source = $responseHtml;
			$variables = [
				'VARIABLE_CITY_ID' => $this->request->getCookie('cityId') ?? null,
			];

			$responseHtml = str_replace(array_keys($variables), array_values($variables), $source);

			return new TextResponse($responseHtml);
		}

		return null;
	}

	public function clearCacheByTag(string $tag, bool $pingAllowed = true): void
	{
		$currentServer = (PHP_SAPI === 'cli' || (isset($_SERVER['SERVER_ADDR']) && Strings::contains($_SERVER['SERVER_ADDR'], '121'))) ? 1 : 2;

		$pingServer = $currentServer === 1 ? 2 : 1;

//		Debugger::log($currentServer . ' clear cache by tag: ' . $tag, 'response-cache-manager');
		$this->cache->clean([
			Cache::TAGS => [$tag],
		]);

		if ($pingAllowed === true) { // prasarnicka, ja vim, zatim neumime vyresetovat cache na serveru2 jinak nez http requestem
//			Debugger::log($currentServer . ' sending ping to clear cache on server' . $pingServer, 'response-cache-manager-pings');
			$context = stream_context_create(["http" => ["header" => "Cookie: cookie=server" . $pingServer . "\r\n"]]);
			file_get_contents("https://www.kaufino.com/api/v1/clear-response-cache?token=MlaPXRji59&t=" . $tag, false, $context);
//		} else {
//			Debugger::log($currentServer . ' ping received', 'response-cache-manager-pings');
		}
	}
}

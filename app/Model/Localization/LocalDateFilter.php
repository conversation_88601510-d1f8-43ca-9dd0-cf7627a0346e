<?php

namespace <PERSON><PERSON><PERSON>\Model\Localization;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;

class LocalDateFilter
{
	public const FLAG_SHORT = 'short';
	public const FLAG_LONG = 'long';

	/**
	 * @var LocalizationFacade
	 */
	private $localizationFacade;

	public function __construct(LocalizationFacade $localizationFacade)
	{
		$this->localizationFacade = $localizationFacade;
	}

	public function __invoke(\DateTime $date, string $flag = 'short')
	{
		$localization = $this->localizationFacade->getCurrentLocalization();

		if ($flag === self::FLAG_SHORT && $localization->getShortDateFormat()) {
			return $date->format($localization->getShortDateFormat());
		} elseif ($flag === self::FLAG_LONG && $localization->getLongDateFormat()) {
			return $date->format($localization->getLongDateFormat());
		}

		$format = $flag == self::FLAG_SHORT ? 'd.m.' : 'd.m.Y';

		if ($localization->isHungarian()) {
			$format = $flag == self::FLAG_SHORT ? 'm.d.' : 'Y.m.d.';
		}

		if ($localization->isGreecian()) {
			$format = $flag == self::FLAG_SHORT ? 'd/m' : 'd/m/Y';
		}

		if ($localization->isJar()) {
			$format = $flag == self::FLAG_SHORT ? 'd M' : 'd M Y';
		}

		if ($localization->getFullLocale() === 'en_US') {
			$format = $flag == self::FLAG_SHORT ? 'm/d' : 'm/d/Y';
		}

		if ($localization->isItaly()) {
			$format = $flag == self::FLAG_SHORT ? 'd/m' : 'd/m/Y';
		}

		if ($localization->isFrancian()) {
			$format = $flag == self::FLAG_SHORT ? 'd/m' : 'd/m/Y';
		}

		if ($localization->isSerbian()) {
			$format = $flag == self::FLAG_SHORT ? 'd/m' : 'd/m/Y';
		}

		if ($localization->isBelgian()) {
			$format = $flag == self::FLAG_SHORT ? 'd/m' : 'd/m/Y';
		}

		if ($localization->isEstonian()) {
			$format = $flag == self::FLAG_SHORT ? 'd/m' : 'd/m/Y';
		}

		if ($localization->isNetherlandian()) {
			$format = $flag == self::FLAG_SHORT ? 'd-m' : 'd-m-Y';
		}

		if ($localization->isGreecian()) {
			$format = $flag == self::FLAG_SHORT ? 'd/m' : 'd/m/Y';
		}

		if ($localization->isBrazilian()) {
			$format = $flag == self::FLAG_SHORT ? 'd/m' : 'd/m/Y';
		}

		if ($localization->isPortuguesian()) {
			$format = $flag == self::FLAG_SHORT ? 'd/m' : 'd/m/Y';
		}

		return $date->format($format);
	}
}

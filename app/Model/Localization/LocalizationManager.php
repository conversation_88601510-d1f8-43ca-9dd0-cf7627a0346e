<?php

namespace <PERSON><PERSON><PERSON>\Model\Localization;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;

class LocalizationManager
{
	private EntityManager $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function saveLocalization(Localization $localization): Localization
	{
		$this->em->persist($localization);
		$this->em->flush();

		return $localization;
	}
}

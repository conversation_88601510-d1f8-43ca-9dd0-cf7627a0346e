<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Localization\Repositories;

use <PERSON><PERSON><PERSON>\Model\EntityRepository;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;

class LocalizationRepository extends EntityRepository
{
	public function getLocalizations()
	{
		return $this->createQueryBuilder('l')
			->addOrderBy('l.id');
	}

	public function findPairsList(): array
	{
		$list = [];

		foreach ($this->getLocalizations()->getQuery()->getResult() as $localization) {
			$list[$localization->getId()] = $localization->getName();
		}

		return $list;
	}

	public function findLocalizationsToProcessLeaflets()
	{
		return $this->createQueryBuilder('l')
			->addOrderBy('l.leafletsSynchronizationStartedAt', 'ASC')
			->setMaxResults(10)
			->getQuery()
			->getResult();
	}
}

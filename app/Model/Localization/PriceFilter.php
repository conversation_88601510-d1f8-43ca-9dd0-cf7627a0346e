<?php

namespace <PERSON><PERSON><PERSON>\Model\Localization;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;

class PriceFilter
{
	public function __invoke($price, ?Localization $localization)
	{
		if ($localization) {
			$price = number_format($price, 2, ',', ' ') . ' ' . $this->getCurrencySymbol($localization);
			return str_replace(',00', '', $price);
		}

		return $price;
	}

	private function getCurrencySymbol(Localization $localization)
	{
		$symbols = [
			'cz' => 'Kč',
			'sk' => '€',
			'pl' => 'zł',
			'ro' => 'Lei',
			'hu' => 'Ft',
			'it' => '€',
			'de' => '€',
			'nl' => '€',
		];

		return isset($symbols[$localization->getRegion()]) ? $symbols[$localization->getRegion()] : null;
	}
}

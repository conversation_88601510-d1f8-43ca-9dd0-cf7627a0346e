<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Localization;

use Contributte;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Nette;

class GeoResolver
{
	public const DEFAULT_REGION = 'cz';

	/**
	 * @var LocalizationFacade
	 */
	private $localizationFacade;

	public function __construct(LocalizationFacade $localizationFacade)
	{
		$this->localizationFacade = $localizationFacade;
	}

	public function resolveGeoFromIP()
	{
		if (isset($_SERVER["HTTP_CF_IPCOUNTRY"])) {
			return Nette\Utils\Strings::lower($_SERVER["HTTP_CF_IPCOUNTRY"]);
		}

		return null;
	}

	public function resolveLocalizationFromRequest()
	{
		$geo = $this->resolveGeoFromIP();

		if ($geo) {
			return $this->localizationFacade->findLocalizationByRegion($geo);
		}

		return null;
	}

	public function resolveRegionFromRequest(): ?string
	{
		$geo = $this->resolveGeoFromIP();

		if (!$geo) {
			return self::DEFAULT_REGION;
		}

		$localization = $this->localizationFacade->findLocalizationByRegion($geo);

		if ($localization && $localization->isActive()) {
			return $localization->getRegion();
		}

		return self::DEFAULT_REGION;
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Model\Conditions\Entities;

use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Conditions\Repositories\DocumentRepository")
 * @ORM\Table(name="kaufino_conditions_document")
 */
class Document
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 * @var int
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	private ?Localization $localization;

	/**
	 * @ORM\Column(type="string")
	 */
	private ?string $type;

	/**
	 * @ORM\Column(type="string")
	 */
	private ?string $name;

	/**
	 * @ORM\Column(type="string")
	 */
	private ?string $slug;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private ?string $content;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function __construct(Localization $localization, $type, $name, $content)
	{
		$this->localization = $localization;
		$this->type = $type;
		$this->name = $name;
		$this->content = $content;
		$this->createdAt = new \DateTime();
	}

	public function getName(): ?string
	{
		return $this->name;
	}

	public function getSlug(): ?string
	{
		return $this->slug;
	}

	public function getContent(): ?string
	{
		return $this->content;
	}
}

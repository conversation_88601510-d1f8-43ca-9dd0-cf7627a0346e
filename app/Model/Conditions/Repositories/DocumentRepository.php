<?php

namespace <PERSON><PERSON><PERSON>\Model\Conditions\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\Conditions\Entities\Document;
use <PERSON><PERSON><PERSON>\Model\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;

class DocumentRepository extends EntityRepository
{
	public function getDocuments(): QueryBuilder
	{
		return $this->createQueryBuilder('d');
	}

	public function findByLocalization(Localization $localization)
	{
		return $this->getDocuments()
			->andWhere('d.localization = :localization')
			->setParameter('localization', $localization)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findBySlug(Localization $localization, string $slug): ?Document
	{
		return $this->getDocuments()
			->andWhere('d.localization = :localization')
			->setParameter('localization', $localization)
			->andWhere('d.slug = :slug')
			->setParameter('slug', $slug)
			->getQuery()
			->getOneOrNullResult();
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model;

use DateTime;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Nette\SmartObject;
use Nette\Utils\Json;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Nette\Utils\JsonException;
use Psr\Http\Message\ResponseInterface;
use Tracy\Debugger;

class TipliClient
{
	use SmartObject;

	/** @var Configuration */
	private $configuration;

	/** @var EntityManager */
	private $em;

	public function __construct(Configuration $configuration, EntityManager $em)
	{
		$this->configuration = $configuration;
		$this->em = $em;
	}

	/**
	 * @return mixed
	 * @throws GuzzleException
	 * @throws JsonException
	 */
	public function getCoupons()
	{
		$response = $this->sendRequest('/coupons', null);

		//dump($response->getStatusCode()); // 200
		//dump((string) $response->getBody()); // '{"id": 1420053, "name": "guzzle", ...}'

		return Json::decode((string)$response->getBody()->getContents());
	}

	/**
	 * @throws GuzzleException
	 */
	private function sendRequest(string $path, ?string $locale = null, array $params = []): ResponseInterface
	{
		$query = [
			'token' => $this->configuration->getTipliApiToken(),
			'locale' => $locale,
		];

		$response = (new Client(['verify' => !$this->configuration->isDevelopmentMode()]))->request('GET', $this->configuration->getTipliApiUrl() . $path, [
			'query' => array_merge($query, $params),
		]);

		//dump($response); // 200
		//dump($response->getStatusCode()); // 200
		//dump((string) $response->getBody()->getContents()); // '{"id": 1420053, "name": "guzzle", ...}'

		return $response;
	}
}

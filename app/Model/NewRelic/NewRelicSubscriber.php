<?php

namespace <PERSON><PERSON><PERSON>\Model\NewRelic;

use Contributte\Events\Extra\Event\Application\ErrorEvent;
use Contributte\Events\Extra\Event\Application\RequestEvent;
use Nette;
use Nette\SmartObject;
use Nette\Utils\Strings;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final class NewRelicSubscriber implements EventSubscriberInterface
{
	use SmartObject;

	public static function getSubscribedEvents(): array
	{
		return [
			ErrorEvent::class => 'onError',
			RequestEvent::class => 'onRequest',
		];
	}

	public function onRequest(RequestEvent $event): void
	{
		if (!extension_loaded('newrelic')) {
			return;
		}

		if (PHP_SAPI === 'cli') {
			newrelic_background_job(true);
			newrelic_set_appname('kaufino');
			newrelic_name_transaction($this->getCliCommandName());

			return;
		}

		$request = $event->getRequest();
		$presenterName = $request->getPresenterName();
		$params = $request->getParameters();

		newrelic_set_appname('kaufino');
		newrelic_name_transaction($request->getPresenterName() . (isset($params['action']) ? ':' . $params['action'] : ''));


		if ($presenterName && (Strings::startsWith($presenterName, 'Api:') || Strings::startsWith($presenterName, 'Cron:'))) {
			newrelic_ignore_apdex();
		}
	}

	public function onError(ErrorEvent $errorEvent)
	{
		if (!extension_loaded('newrelic')) {
			return;
		}

		if ($errorEvent->getThrowable() instanceof Nette\Application\BadRequestException) {
			return;
		}

		if ($errorEvent->getThrowable() instanceof \Exception) {
			if (PHP_SAPI === 'cli') {
				newrelic_set_appname('kaufino');
				newrelic_name_transaction($this->getCliCommandName());
			}
			newrelic_set_appname('kaufino');
			newrelic_notice_error($errorEvent->getThrowable()->getMessage(), $errorEvent->getThrowable());
		}
	}

	private function getCliCommandName(): string
	{
		$argv = ($_SERVER['argv']) ? implode(' ', $_SERVER['argv']) : '';
		$commandName = Strings::contains($argv, 'bin/console') ? trim(explode('bin/console', $argv)[1]) : '';

		if (Strings::contains($commandName, 'rabbitmq:consumer') && isset($_SERVER['argv'][2])) {
			$commandName = 'consumer/' . ($_SERVER['argv'][2]);
		}

		return $commandName;
	}
}

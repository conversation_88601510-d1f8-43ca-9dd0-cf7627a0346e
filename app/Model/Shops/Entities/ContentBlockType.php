<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Shops\Entities;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Shops\Repositories\ContentBlockTypeRepository")
 * @ORM\Table(name="kaufino_shops_content_block_type", uniqueConstraints={
 *     @ORM\UniqueConstraint(name="content_block_type_unique", columns={"type", "website_type", "entity"})}
 * )
 */
class ContentBlockType
{
	public const TYPE_COUPONS = 'coupons';
	public const TYPE_DESCRIPTION = 'description';

	public const CONTENT_TYPE_HTML = 'html';
	public const CONTENT_TYPE_PLAIN_TEXT = 'plain_text';

	public const ENTITY_SHOP = 'shop';
	public const ENTITY_TAG = 'tag';

	public const WEBSITE_TYPES = ['kaufino', 'oferto', 'letado'];

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=false)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 * @var string
	 */
	private $name;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 * @var string
	 */
	private $type;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 * @var string
	 */
	private $entity;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 * @var string
	 */
	private $contentType;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	private $websiteType;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private $instructions;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private $defaultPrompt;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $generateContent = false;

	/**
	 * @ORM\Column(type="integer", nullable=false)
	 */
	private $priority = 0;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $required = true;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private $active = true;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function getName(): string
	{
		return $this->name;
	}

	public function getType(): string
	{
		return $this->type;
	}

	public function getWebsiteType()
	{
		return $this->websiteType;
	}

	public function getInstructions()
	{
		return $this->instructions;
	}

	public function isRequired(): bool
	{
		return $this->required;
	}

	public function getContentType(): string
	{
		return $this->contentType;
	}

	public function getDefaultPrompt()
	{
		return $this->defaultPrompt;
	}

	public function setDefaultPrompt($defaultPrompt): void
	{
		$this->defaultPrompt = $defaultPrompt;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getEntity(): string
	{
		return $this->entity;
	}
}

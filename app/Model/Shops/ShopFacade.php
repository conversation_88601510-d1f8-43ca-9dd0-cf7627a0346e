<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Shops;

use Doctrine\ORM\QueryBuilder;
use Ka<PERSON>ino\Model\EntityManager;
use Ka<PERSON>ino\Model\Geo\Entities\City;
use Kaufino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Offers\Entities\Offer;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Entities\Store;
use Kaufino\Model\Shops\Repositories\ShopRepository;
use Kaufino\Model\Tags\Entities\Tag;
use Kaufino\Model\Websites\Entities\Website;

class ShopFacade
{
	/** @var EntityManager */
	private $em;

	/** @var ShopManager */
	private $shopManager;

	/** @var ShopRepository */
	private $shopRepository;

	public function __construct(EntityManager $em, ShopManager $shopManager, ShopRepository $shopRepository)
	{
		$this->em = $em;
		$this->shopRepository = $shopRepository;
		$this->shopManager = $shopManager;
	}

	public function createShop(Localization $localization, string $name, string $slug): Shop
	{
		return $this->shopManager->createShop($localization, $name, $slug);
	}

	public function saveShop(Shop $shop, bool $resetCache = true)
	{
		return $this->shopManager->saveShop($shop, $resetCache);
	}

	public function getShops(Localization $localization = null): QueryBuilder
	{
		return $this->shopRepository->getShops($localization);
	}

	public function findShop($id): ?Shop
	{
		/** @var ?Shop $shop */
		$shop = $this->shopRepository->find($id);

		return $shop;
	}

	public function findShopByShopId(int $shopId): ?Shop
	{
		/** @var ?Shop $shop */
		$shop = $this->shopRepository->findOneBy(['shopId' => $shopId]);

		return $shop;
	}

	public function findShopBySlug(Localization $localization, string $slug): ?Shop
	{
		return $this->shopRepository->findShopBySlug($localization, $slug);
	}

	public function findShopByDomain(Localization $localization, string $domain): ?Shop
	{
		return $this->shopRepository->findShopByDomain($localization, $domain);
	}

	public function findLeafletShopsByFulltext(Localization $localization, ?string $search = null, ?bool $onlyStores = false, ?int $limit = 10, ?string $websiteType = null): array
	{
		return $this->findShopsByFulltext(
			$localization,
			$search,
			$onlyStores,
			$limit,
			$websiteType,
			true
		);
	}

	public function findCouponShopsByFulltext(Localization $localization, ?string $search = null, ?bool $onlyStores = false, ?int $limit = 10, ?string $websiteType = null): array
	{
		return $this->findShopsByFulltext(
			$localization,
			$search,
			$onlyStores,
			$limit,
			$websiteType,
			null,
			true
		);
	}

	public function findShopsByFulltext(Localization $localization, ?string $search = null, ?bool $onlyStores = false, ?int $limit = 10, ?string $websiteType = null, ?bool $activeLeaflets = null, ?bool $activeCoupons = null)
	{
		return $this->shopRepository->findShopsByFulltext($localization, $search, $limit, $websiteType, $onlyStores, $activeLeaflets, $activeCoupons);
	}

	public function findLeafletShops(Localization $localization, ?bool $onlyStores = false, ?int $limit = null, ?string $websiteType = null): array
	{
		return $this->findShops(
			$localization,
			$onlyStores,
			$limit,
			$websiteType
		);
	}

	public function findCouponShops(Localization $localization, ?bool $onlyStores = false, ?int $limit = null, ?string $websiteType = null): array
	{
		return $this->findShops(
			$localization,
			$onlyStores,
			$limit,
			$websiteType
		);
	}

	public function findShops(Localization $localization, ?bool $onlyStores = false, ?int $limit = null, ?string $websiteType = null): array
	{
		$params = ['localization' => $localization];

		if ($onlyStores) {
			$params['type'] = Shop::TYPE_STORE;
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$params['activeOferto'] = 1;
		} elseif ($websiteType === Website::MODULE_KAUFINO || Website::MODULE_KAUFINO_SUBDOMAIN) {
			$params['activeKaufino'] = 1;
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$params['activeLetado'] = 1;
		} elseif ($websiteType === Website::MODULE_OFERTO_COM) {
			$params['activeOfertoCom'] = 1;
		}

		if ($websiteType === Website::MODULE_KAUFINO) {
			$params['hidden'] = 0;
		}

		return $this->shopRepository->findBy($params, null, $limit);
	}

	public function findTopLeafletShops(Localization $localization, ?bool $onlyStores = false, ?int $limit = null, ?string $websiteType = null, ?bool $onlyEshops = false): array
	{
		$params = ['localization' => $localization];

		if ($onlyStores) {
			$params['type'] = Shop::TYPE_STORE;
		} elseif ($onlyEshops) {
			$params['type'] = Shop::TYPE_ESHOP;
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$params['activeOferto'] = 1;
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$params['activeKaufino'] = 1;
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$params['activeLetado'] = 1;
		} elseif ($websiteType === Website::MODULE_OFERTO_COM) {
			$params['activeOfertoCom'] = 1;
		}

		if ($websiteType === Website::MODULE_KAUFINO) {
			$params['hidden'] = 0;
		}

		return $this->shopRepository->findBy($params, ['boost' => 'DESC', 'priorityLeaflets' => 'DESC'], $limit);
	}

	public function findTopCouponShops(Localization $localization, ?int $limit = null): array
	{
		$params = ['activeCoupons' => 1, 'localization' => $localization];

		return $this->shopRepository->findBy($params, ['boostCoupons' => 'DESC', 'priorityCoupons' => 'DESC'], $limit);
	}

	public function findTopLeafletEshops(Localization $localization, ?int $limit = null, ?string $websiteType = null): array
	{
		$params = ['localization' => $localization];

		$params['type'] = Shop::TYPE_ESHOP;

		if ($websiteType === Website::MODULE_OFERTO) {
			$params['activeOferto'] = 1;
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$params['activeKaufino'] = 1;
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$params['activeLetado'] = 1;
		}

		return $this->shopRepository->findBy($params, ['priorityLeaflets' => 'DESC'], $limit);
	}

	public function findLeafletShopsByTag(Tag $tag, ?bool $onlyStores = false, ?int $limit = 10, ?string $websiteType = null, bool $withActiveCities = false): array
	{
		return $this->findShopsByTag(
			$tag,
			$onlyStores,
			$limit,
			$websiteType,
			$withActiveCities
		);
	}

	public function findShopsByLabel(Tag $label, ?int $limit = 10)
	{
		return $this->shopRepository->findShopsByLabel($label, $limit);
	}

	public function findShopsWithoutLabel(Localization $localization, ?int $limit = 10)
	{
		return $this->shopRepository->findShopsWithoutLabel($localization, $limit);
	}

	public function findShopsByTag(Tag $tag, ?bool $onlyStores = false, ?int $limit = 10, ?string $websiteType = null, bool $withActiveCities = false): array
	{
		return $this->shopRepository->findShopsByTag($tag, $onlyStores, $limit, $websiteType, $withActiveCities);
	}

	public function findLeafletShopsByCity(City $city, ?int $limit = 10, bool $onlyStores = false, ?string $websiteType = null): array
	{
		return $this->shopRepository->findLeafletShopsByCity($city, $limit, $onlyStores, $websiteType);
	}

	public function findPairs(Localization $localization = null, ?bool $activeLeaflets = null): array
	{
		return $this->shopRepository->findPairsList($localization, $activeLeaflets);
	}

	public function findLeafletShopInternationalVariants(Shop $shop, array $localizations, ?string $websiteType = null): array
	{
		return $this->shopRepository->findLeafletShopInternationalVariants($shop, $localizations, $websiteType);
	}

	public function findTopShopsWithOffers(Localization $localization, $limit = 12, string $type = Offer::TYPE_COUPON)
	{
		return $this->shopRepository->findTopShopsWithOffers($localization, $limit, $type);
	}

	public function findShopsToProcessReviews()
	{
		return $this->shopRepository->findShopsToProcessReviews();
	}

	public function findByIds(array $ids)
	{
		return $this->shopRepository->findByIds($ids);
	}

	public function findNearestShopsByStore(Store $store, int $limit = 10)
	{
		return $this->shopRepository->findNearestShopsByStore($store, $limit);
	}

	public function findShopsByWebsiteType(Localization $localization, string $websiteType)
	{
		return $this->shopRepository->findShopsByWebsiteType($localization, $websiteType);
	}

	public function getCountOfActiveShopsInCities(array $cities)
	{
		return $this->shopRepository->getCountOfActiveShopsInCities($cities);
	}

	public function getCountOfShopsInCities(array $cities)
	{
		return $this->shopRepository->getCountOfShopsInCities($cities);
	}

	public function findSimilarLeafletsWithCity(?Tag $tag, City $city, string $websiteType, int $limit = 10)
	{
		return $this->shopRepository->findSimilarLeafletsWithCity($tag, $city, $websiteType, $limit);
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\ResponseCacheManager;
use <PERSON><PERSON>ino\Model\Shops\Entities\Review;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;
use <PERSON><PERSON>ino\Model\Users\Entities\User;

class ReviewManager
{
	/** @var EntityManager */
	private $em;

	/** @var ResponseCacheManager */
	private $responseCacheManager;

	public function __construct(EntityManager $em, ResponseCacheManager $responseCacheManager)
	{
		$this->em = $em;
		$this->responseCacheManager = $responseCacheManager;
	}

	public function createReview(Shop $shop, int $rate, ?User $user = null, ?string $text = null): Review
	{
		$review = new Review($shop, $rate, $user, $text);

		return $this->saveReview($review);
	}

	public function saveReview(Review $review): Review
	{
		$this->em->persist($review);
		$this->em->flush();

		$this->responseCacheManager->clearCacheByTag('shop/' . $review->getShop()->getId());

		return $review;
	}

	public function removeReview(Review $review)
	{
		$this->em->remove($review);
		$this->em->flush();
	}
}

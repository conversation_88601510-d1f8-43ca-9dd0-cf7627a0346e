<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Review;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\Repositories\ReviewRepository;
use <PERSON><PERSON>ino\Model\Users\Entities\User;

class ReviewFacade
{
	private ReviewRepository $reviewRepository;
	private ReviewManager $reviewManager;

	public function __construct(ReviewRepository $reviewRepository, ReviewManager $reviewManager)
	{
		$this->reviewRepository = $reviewRepository;
		$this->reviewManager = $reviewManager;
	}

	public function createReview(Shop $shop, int $rate, ?User $user = null, ?string $text = null): Review
	{
		return $this->reviewManager->createReview($shop, $rate, $user, $text);
	}

	public function getReviewsByShop(Shop $shop): QueryBuilder
	{
		return $this->reviewRepository->getReviewsByShop($shop);
	}

	public function find(int $id)
	{
		return $this->reviewRepository->find($id);
	}

	public function removeReview(Review $review)
	{
		$this->reviewManager->removeReview($review);
	}

	public function findAverageShopReview(Shop $shop)
	{
		return $this->reviewRepository->findAverageShopReview($shop);
	}

	public function findCountOfShopReviews(Shop $shop)
	{
		return $this->reviewRepository->findCountOfShopReviews($shop);
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\ResponseCacheManager;
use <PERSON><PERSON>ino\Model\Shops\Entities\ContentBlock;
use <PERSON><PERSON>ino\Model\Shops\Entities\ContentBlockType;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Users\Entities\User;

class ContentBlockManager
{
	/** @var EntityManager */
	private $em;

	/** @var ResponseCacheManager */
	private $responseCacheManager;

	public function __construct(EntityManager $em, ResponseCacheManager $responseCacheManager)
	{
		$this->em = $em;
		$this->responseCacheManager = $responseCacheManager;
	}

	public function createContentBlock(ContentBlockType $contentBlockType, ?Shop $shop, ?Tag $tag, ?User $user, ?string $heading, ?string $content, bool $generatedByAi = false): ContentBlock
	{
		$contentBlock = new ContentBlock(
			$contentBlockType,
			$shop,
			$tag,
			$user,
			$heading,
			$content,
			$generatedByAi
		);

		return $this->saveContentBlock($contentBlock);
	}

	public function saveContentBlock(ContentBlock $contentBlock): ContentBlock
	{
		$this->em->persist($contentBlock);
		$this->em->flush($contentBlock);

		if ($shop = $contentBlock->getShop()) {
			$this->responseCacheManager->clearCacheByTag('shop/' . $shop->getId());
		} elseif ($tag = $contentBlock->getTag()) {
			$this->responseCacheManager->clearCacheByTag('tag/' . $tag->getId());
		}

		return $contentBlock;
	}
}

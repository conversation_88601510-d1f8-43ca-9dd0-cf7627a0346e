<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops;

use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlockType;

class ContentBlockTypeManager
{
	/** @var EntityManager */
	private $em;

	public function __construct(EntityManager $em)
	{
		$this->em = $em;
	}

	public function saveContentBlockType(ContentBlockType $contentBlockType): ContentBlockType
	{
		$this->em->persist($contentBlockType);
		$this->em->flush($contentBlockType);

		return $contentBlockType;
	}
}

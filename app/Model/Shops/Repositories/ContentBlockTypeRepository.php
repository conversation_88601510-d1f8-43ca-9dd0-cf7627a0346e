<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\EntityRepository;

class ContentBlockTypeRepository extends EntityRepository
{
	public function getContentBlockTypes(): QueryBuilder
	{
		return $this->createQueryBuilder('cbt');
	}

	public function findContentBlockTypesByWebsiteType(string $websiteType, ?string $entity)
	{
		$qb = $this->getContentBlockTypes()
			->andWhere('cbt.websiteType = :websiteType')
			->setParameter('websiteType', $websiteType)
			->andWhere('cbt.active = 1')
			->addOrderBy('cbt.priority', 'DESC')
		;

		if ($entity) {
			$qb
				->andWhere('cbt.entity = :entity')
				->setParameter('entity', $entity)
			;
		}

		return $qb
			->getQuery()
			->getResult()
		;
	}
}

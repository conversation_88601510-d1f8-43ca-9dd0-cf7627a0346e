<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Shops\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\EntityRepository;
use <PERSON><PERSON><PERSON>\Model\Geo\Entities\City;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Entities\Store;
use Kaufino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Websites\Entities\Website;
use Tracy\Debugger;

class ShopRepository extends EntityRepository
{
	public function getShops(Localization $localization = null, ?string $websiteType = null): QueryBuilder
	{
		$qb = $this->createQueryBuilder('s');

		if ($localization) {
			$qb->andWhere('s.localization = :localization')->setParameter('localization', $localization);
		} else {
			$qb->leftJoin('s.localization', 'l');
				//->addOrderBy('l.locale', 'ASC');
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('s.activeOferto = 1');
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$qb->andWhere('s.activeKaufino = 1');
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$qb->andWhere('s.activeLetado = 1');
		}

		if ($websiteType === Website::MODULE_KAUFINO) {
			$qb->andWhere('s.hidden = 0');
		}

		return $qb;
	}

	public function findShopBySlug(Localization $localization, string $slug)
	{
		$qb = $this->getShops($localization)
			->andWhere('s.slug = :slug')
			->setParameter('slug', $slug);

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findShopByDomain(Localization $localization, string $domain)
	{
		Debugger::dump($domain);
		$qb = $this->getShops($localization)
			->andWhere('s.domain LIKE :domain')
			->setParameter('domain', '%' . $domain . '%')
			->setMaxResults(1)
		;

		return $qb->getQuery()->getOneOrNullResult();
	}

	public function findShopsByFulltext(Localization $localization = null, ?string $search = null, $limit = 10, ?string $websiteType = null, bool $onlyStores = false)
	{
		$qb = $this->getShops($localization, $websiteType)
			->setMaxResults($limit);

		if ($search) {
			$qb->andWhere('s.name LIKE :search')
				->setParameter('search', '%' . $search . '%');
		}

		if ($onlyStores === true) {
			$qb->andWhere('s.type = :store')
				->setParameter('store', Shop::TYPE_STORE);
		}

		if ($websiteType === Website::MODULE_OFERTO_COM) {
			$qb->andWhere('s.activeOfertoCom = 1')
				->addOrderBy('s.boost', 'DESC')
			;
		} else {
			$qb->addOrderBy('s.boost', 'DESC')
				->addOrderBy('s.priorityLeaflets', 'DESC')
			;
		}

		return $qb->getQuery()->getResult();
	}

	public function findPairsList(Localization $localization = null, ?bool $activeLeaflets = null): array
	{
		$list = [];

		$shops = $this->getShops($localization);

		if ($activeLeaflets !== null) {
			$shops->andWhere('s.activeLeaflets = :activeLeaflets')
				->setParameter('activeLeaflets', $activeLeaflets);
		}

		foreach ($shops->getQuery()->getResult() as $shop) {
			if ($localization) {
				$name = $shop->getName();
			} else {
				$name = $shop->getLocalization()->getRegion() . ' ' . $shop->getName();
			}

			$list[$shop->getId()] = $name;
		}

		return $list;
	}

	public function findLeafletShopsByCity(City $city, ?int $limit, bool $onlyStores = false, ?string $websiteType = null)
	{
		$qb = $this->findShopsByCity($city, $limit)
			->addOrderBy('s.priorityLeaflets', 'DESC')
			->andWhere('s.activeCities = true')
		;

		if ($onlyStores) {
			$qb->andWhere('s.type = :type')
				->setParameter('type', Shop::TYPE_STORE);
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('s.activeOferto = 1');
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$qb->andWhere('s.activeKaufino = 1');
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$qb->andWhere('s.activeLetado = 1');
		} elseif ($websiteType === Website::MODULE_OFERTO_COM) {
			$qb->andWhere('s.activeOfertoCom = 1');
		}

		if ($websiteType === Website::MODULE_KAUFINO) {
			$qb->andWhere('s.hidden = 0');
		}

		return $qb->getQuery()->getResult();
	}

	public function findShopsByCity(City $city, ?int $limit): QueryBuilder
	{
		return $this->getShops($city->getLocalization())
			->leftJoin('s.cities', 'c')
			->andWhere('c.id = :cityId')
			->addOrderBy('s.boost', 'DESC')
			->setParameter('cityId', $city->getId())
			->setMaxResults($limit);
	}

	public function findLeafletShopInternationalVariants(Shop $shop, array $localizations, ?string $websiteType = null)
	{
		$qb = $this->findShopInternationalVariants($shop, $localizations)
			->andWhere('s.activeLeaflets = true')
		;

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('s.activeOferto = 1');
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$qb->andWhere('s.activeKaufino = 1');
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$qb->andWhere('s.activeLetado = 1');
		} elseif ($websiteType === Website::MODULE_OFERTO_COM) {
			$qb->andWhere('s.activeOfertoCom = 1');
		}

		return $qb->getQuery()
		->getResult();
	}

	public function findShopInternationalVariants(Shop $shop, array $localizations)
	{
		$localizationIds = array_map(static function ($localization) {
			return $localization->getId();
		}, $localizations);

		$qb = $this->getShops()
			->andWhere('s.slug = :slug')
			->andWhere('s.localization != :localization')->setParameter('localization', $shop->getLocalization())
			->andWhere('s.localization IN (:localizationIds)')
			->setParameter('slug', $shop->getSlug())
			->setParameter('localizationIds', $localizationIds);

		return $qb;
	}

	public function findTopShopsWithOffers(Localization $localization, $limit = 12, string $type = Offer::TYPE_COUPON)
	{
		$qb = $this->getShops($localization)
			->innerJoin('s.offers', 'o')
			->andWhere('o.type = :type')
			->setParameter('type', $type)
			->andWhere('o.id IS NOT NULL')
			->andWhere('o.validTill > :now')
			->setParameter('now', new \DateTime())
			->addGroupBy('s.id')
		;

		if ($type === Offer::TYPE_COUPON) {
			$qb->andWhere('s.activeCoupons = true');
		} elseif ($type === Offer::TYPE_LEAFLET) {
			$qb->andWhere('s.activeLeaflets = true');
		}

		return $qb
			->setMaxResults($limit)
			->getQuery()
			->getResult();
	}

	public function findShopsByLabel(Tag $label, ?int $limit = 10)
	{
		$qb = $this->getShops()
			->innerJoin('s.labels', 'labels')
			->andWhere('labels = :label')
			->setParameter('label', $label)
			->andWhere('s.activeCoupons = true')
		;

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		return $qb->getQuery()
			->getResult();
	}

	public function findShopsWithoutLabel(Localization $localization, ?int $limit = 10)
	{
		$qb = $this->getShops($localization)
			->leftJoin('s.labels', 'labels')
			->andWhere('labels IS NULL')
			->andWhere('s.activeCoupons = true')
		;

		if ($limit) {
			$qb->setMaxResults($limit);
		}

		return $qb->getQuery()
			->getResult();
	}

	public function findShopsToProcessReviews()
	{
		return $this->getShops()
			->andWhere('s.activeCoupons = true OR s.activeLeaflets = true')
			->getQuery()
			->getResult();
	}

	public function findByIds(array $ids)
	{
		return $this->getShops()
			->andWhere('s.id IN (:ids)')
			->setParameter('ids', $ids)
			->getQuery()
			->getResult();
	}

	public function findShopsByTag(Tag $tag, ?bool $onlyStores = false, ?int $limit = 10, ?string $websiteType = null): array
	{
		$qb = $this->createQueryBuilder('s')
			->leftJoin('s.tags', 't')
			->andWhere('t = :tag OR s.tag = :tag')
			->setParameter('tag', $tag)
		;

		if ($onlyStores) {
			$qb->andWhere('s.type = :type')
				->setParameter('type', Shop::TYPE_STORE)
			;
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('s.activeOferto = 1');
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$qb->andWhere('s.activeKaufino = 1');
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$qb->andWhere('s.activeLetado = 1');
		} elseif ($websiteType === Website::MODULE_OFERTO_COM) {
			$qb->andWhere('s.activeOfertoCom = 1');
		}

		if ($websiteType === Website::MODULE_KAUFINO) {
			$qb->andWhere('s.hidden = 0');
		}

		$qb->addOrderBy('s.boost', 'DESC')
			->addOrderBy('s.priorityLeaflets', 'DESC')
		;

		return $qb->setMaxResults($limit)
			->getQuery()
			->getResult();
	}

	public function findNearestShopsByStore(Store $store, int $limit = 10)
	{
		$query = $this->getEntityManager()->getConnection()->executeQuery(
			sprintf("SELECT id, name, lat, lng, shop_id, SQRT(
                        POW(69.1 * (lat - %s), 2) +
                        POW(69.1 * (%s - lng) * COS(lat/ 57.3), 2)) AS distance
                    FROM kaufino_shops_store
                    WHERE localization_id = %d AND id != %d AND lat IS NOT NULL AND lng IS NOT NULL
                    HAVING distance < %s ORDER BY distance LIMIT %s", $store->getLat(), $store->getLng(), $store->getLocalization()->getId(), $store->getId(), 75, $limit)
		);

		$shopIds = [];
		foreach ($query->fetchAllAssociative() as $result) {
			$shopIds[] = $result['shop_id'];
		}

		$qb = $this->getShops()
			->andWhere('s.id IN (:shopIds)')
			->setParameter('shopIds', $shopIds)
		;

		return $qb->getQuery()->getResult();
	}

	public function findShopsByWebsiteType(Localization $localization, string $websiteType)
	{
		$qb = $this->getShops($localization, $websiteType)
			->addSelect('c')
			->leftJoin('s.cities', 'c')
		;

		return $qb->getQuery()->getResult();
	}

	public function getCountOfActiveShopsInCities(array $cities)
	{
		$cityIds = array_map(static function ($city) {
			return $city->getId();
		}, $cities);

		if (empty($cityIds)) {
			return 0;
		}

		$query = $this->getEntityManager()->getConnection()->executeQuery(
			sprintf("SELECT COUNT(sc.city_id) as cnt FROM kaufino_shops_shop_city sc INNER JOIN kaufino_shops_shop s ON s.id = sc.shop_id  WHERE s.active_cities = 1 AND sc.city_id IN (%s)", implode(',', $cityIds))
		);

		return $query->fetchOne();
	}

	public function getCountOfShopsInCities(array $cities)
	{
		$cityIds = array_map(static function ($city) {
			return $city->getId();
		}, $cities);

		if (empty($cityIds)) {
			return 0;
		}

		$query = $this->getEntityManager()->getConnection()->executeQuery(
			sprintf("SELECT COUNT(sc.city_id) as cnt FROM kaufino_shops_shop_city sc  WHERE sc.city_id IN (%s)", implode(',', $cityIds))
		);

		return $query->fetchOne();
	}

	public function findSimilarLeafletsWithCity(?Tag $tag, City $city, string $websiteType, int $limit = 10)
	{
		$qb = $this->createQueryBuilder('s')
			->innerJoin('s.leaflets', 'l')
			->innerJoin('l.cities', 'c')
			->andWhere('c = :city')
			->andWhere('s.activeCities = true')
			->setParameter('city', $city)
			->addOrderBy('s.priorityLeaflets', 'DESC')
			->setMaxResults($limit)
		;

		if ($tag) {
			$qb->innerJoin('s.tags', 't')
				->andWhere('t = :tag')
				->setParameter('tag', $tag)
			;
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('s.activeOferto = 1');
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$qb->andWhere('s.activeKaufino = 1');
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$qb->andWhere('s.activeLetado = 1');
		} elseif ($websiteType === Website::MODULE_OFERTO_COM) {
			$qb->andWhere('s.activeOfertoCom = 1');
		}

		if ($websiteType === Website::MODULE_KAUFINO) {
			$qb->andWhere('s.hidden = 0');
		}

		return $qb->getQuery()
			->getResult();
	}
}

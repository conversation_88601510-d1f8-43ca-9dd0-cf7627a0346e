<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops\Repositories;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\Model\EntityRepository;
use <PERSON><PERSON>ino\Model\Shops\Entities\ContentBlockType;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;

class ContentBlockRepository extends EntityRepository
{
	public function getContentBlocks(?Shop $shop = null, ?string $websiteType = null): QueryBuilder
	{
		$qb = $this->createQueryBuilder('cb');

		if ($shop) {
			$qb->andWhere('cb.shop = :shop')
				->setParameter('shop', $shop);
		}

		if ($websiteType) {
			$qb->andWhere('cb.websiteType = :websiteType')
				->setParameter('websiteType', $websiteType);
		}

		return $qb;
	}

	public function findContentBlocksByShop(Shop $shop, string $websiteType)
	{
		return $this->getContentBlocks($shop, $websiteType)
			->innerJoin('cb.contentBlockType', 'cbt')
			->addOrderBy('cbt.priority', 'DESC')
			->andWhere('cbt.active = true')
			->andWhere('cb.archivedAt IS NULL')
			->getQuery()
			->getResult();
	}

	public function findContentBlocksByTag(Tag $tag, string $websiteType)
	{
		return $this->getContentBlocks(null, $websiteType)
			->innerJoin('cb.contentBlockType', 'cbt')
			->addOrderBy('cbt.priority', 'DESC')
			->andWhere('cb.tag = :tag')
			->setParameter('tag', $tag)
			->andWhere('cb.archivedAt IS NULL')
			->getQuery()
			->getResult();
	}

	public function findContentBlocksToGenerate(int $limit = 1)
	{
		return $this->getContentBlocks()
			->select('cb')
			->innerJoin('cb.contentBlockType', 'cbt')
			->andWhere('cbt.generateContent = true')
			->andWhere('cbt.defaultPrompt IS NOT NULL')
			->andWhere('cb.generatedContent IS NULL')
			->andWhere('(cb.content IS NULL OR cb.content = :empty)')
			->setParameter('empty', '')
			->setMaxResults($limit)
			->getQuery()
			->getResult()
		;
	}

	public function findFaqContentBlocks(Shop $shop, string $websiteType)
	{
		return $this->getContentBlocks($shop, $websiteType)
			->innerJoin('cb.contentBlockType', 'cbt')
			->andWhere('cbt.type LIKE :faq')
			->setParameter('faq', 'faq_%')
			->andWhere('LENGTH(cb.heading) > 5')
			->andWhere('LENGTH(cb.content) > 10')
			->andWhere('cb.archivedAt IS NULL')
			->getQuery()
			->getResult();
	}

	public function findFaqContentBlocksForTag(Tag $tag, string $websiteType)
	{
		return $this->getContentBlocks(null, $websiteType)
			->innerJoin('cb.contentBlockType', 'cbt')
			->andWhere('cbt.type LIKE :faq')
			->setParameter('faq', 'faq_%')
			->andWhere('LENGTH(cb.heading) > 5')
			->andWhere('LENGTH(cb.content) > 10')
			->andWhere('cb.tag = :tag')
			->setParameter('tag', $tag)
			->getQuery()
			->getResult();
	}

	public function findContentBlockByTypeForShop(Shop $shop, ContentBlockType $contentBlockType)
	{
		return $this->getContentBlocks($shop)
			->innerJoin('cb.contentBlockType', 'cbt')
			->andWhere('cbt = :contentBlockType')
			->setParameter('contentBlockType', $contentBlockType)
			->andWhere('cb.archivedAt IS NULL')
			->getQuery()
			->getOneOrNullResult();
	}

	public function findContentBlockByTypeForTag(Tag $tag, ContentBlockType $contentBlockType)
	{
		return $this->getContentBlocks()
			->innerJoin('cb.contentBlockType', 'cbt')
			->andWhere('cbt = :contentBlockType')
			->setParameter('contentBlockType', $contentBlockType)
			->andWhere('cb.tag = :tag')
			->setParameter('tag', $tag)
			->andWhere('cb.archivedAt IS NULL')
			->getQuery()
			->getOneOrNullResult();
	}

	public function findArchivedContentBlocksByShop(Shop $shop, string $websiteType)
	{
		return $this->getContentBlocks(null, $websiteType)
			->innerJoin('cb.contentBlockType', 'cbt')
			->andWhere('cb.shop = :shop')
			->setParameter('shop', $shop)
			->andWhere('cb.archivedAt IS NOT NULL')
			->getQuery()
			->getResult();
	}

	public function findArchivedContentBlocksByTag(Tag $tag, string $websiteType)
	{
		return $this->getContentBlocks(null, $websiteType)
			->innerJoin('cb.contentBlockType', 'cbt')
			->andWhere('cb.tag = :tag')
			->setParameter('tag', $tag)
			->andWhere('cb.archivedAt IS NOT NULL')
			->getQuery()
			->getResult();
	}
}

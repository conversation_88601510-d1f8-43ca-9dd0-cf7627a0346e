<?php

namespace <PERSON><PERSON><PERSON>\Model\Shops;

use Ka<PERSON>ino\Model\Geo\Entities\City;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Entities\Store;
use Ka<PERSON>ino\Model\Shops\Repositories\StoreRepository;

class StoreFacade
{
	private StoreRepository $storeRepository;
	private StoreManager $storeManager;

	public function __construct(StoreRepository $storeRepository, StoreManager $storeManager)
	{
		$this->storeRepository = $storeRepository;
		$this->storeManager = $storeManager;
	}

	public function findByStoreId(int $storeId): ?Store
	{
		return $this->storeRepository->findByStoreId($storeId);
	}

	public function createStore(
		Localization $localization,
		Shop $shop,
		City $city,
		string $name,
		string $fullAddress,
		?int $storeId,
		?string $type,
		?string $municipality,
		?string $street,
		?string $houseNumber,
		?string $zipCode,
		?string $lat,
		?string $lng,
		?string $slug
	): Store {
		return $this->storeManager->createStore($localization, $shop, $city, $name, $fullAddress, $storeId, $type, $municipality, $street, $houseNumber, $zipCode, $lat, $lng, $slug);
	}

	public function saveStore(Store $store): Store
	{
		return $this->storeManager->saveStore($store);
	}

	public function findBySlug(Localization $localization, string $slug): ?Store
	{
		return $this->storeRepository->findBySlug($localization, $slug);
	}

	public function findNearestStores(Store $store, int $limit, string $websiteType): array
	{
		return $this->storeRepository->findNearestStores($store, $limit, $websiteType);
	}

	public function findStoreBySlug(City $city, Shop $shop, string $slug, ?string $websiteType = null): ?Store
	{
		return $this->storeRepository->findStoreBySlug($city, $shop, $slug, $websiteType);
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\GoogleOptimize;

use Nette\Http\Request;
use Nette\Http\Response;

class GoogleOptimize
{
	/**
	 * @var Request
	 */
	private $httpRequest;

	/**
	 * @var Response
	 */
	private $httpResponse;

	public function __construct(Request $httpRequest, Response $httpResponse)
	{
		$this->httpRequest = $httpRequest;
		$this->httpResponse = $httpResponse;
	}

	public function getVariant(string $experimentId, int $countOfVariants)
	{
		$variant = $this->httpRequest->getCookie($this->getCookieKey($experimentId));

		if ($variant !== null) {
			if ($variant >= 0 && $variant < $countOfVariants) {
				return $variant;
			}
		}

		$variant = $this->chooseVariant($countOfVariants);
		$this->httpResponse->setCookie($this->getCookieKey($experimentId), (string) $variant, '1 month');
		return $variant;
	}

	private function getCookieKey(string $experimentId)
	{
		return 'go-' . $experimentId;
	}

	private function chooseVariant(int $countOfVariants): int
	{
		return random_int(0, $countOfVariants - 1);
	}
}

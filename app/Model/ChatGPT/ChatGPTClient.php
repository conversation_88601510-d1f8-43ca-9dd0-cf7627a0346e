<?php

namespace <PERSON><PERSON><PERSON>\Model\ChatGPT;

use GuzzleHttp\Client;
use <PERSON><PERSON><PERSON>\Model\Configuration;
use Nette\Utils\Json;

class ChatGPTClient
{
	private const BASE_URL = 'https://api.openai.com/v1/';

	private const METHOD_POST = 'POST';

	/** @var string|null */
	private $apiToken;

	public function __construct(Configuration $configuration)
	{
		$this->apiToken = $configuration->getChatGPTApiToken();
	}

	public function getCompletion(string $content)
	{
		$body = [
			'model' => 'gpt-3.5-turbo',
			'messages' => [
				['role' => 'user', 'content' => $content],
			],
		];

		$response = $this->sendRequest('chat/completions', $body);

		return $response->choices[0]->message->content;
	}

	private function sendRequest(string $endpoint, array $body, string $method = self::METHOD_POST)
	{
		$client = new Client(['verify' => false, 'base_uri' => self::BASE_URL]);

		$response = $client->request($method, $endpoint, [
			'headers' => [
				'Authorization' => 'Bearer ' . $this->apiToken,
				'Content-Type' => 'application/json',
			],
			'body' => Json::encode($body),
		]);

		if ($response->getStatusCode() === 200) {
			return Json::decode($response->getBody()->getContents());
		}

		throw new \Exception('Error while sending request to ChatGPT API');
	}
}

<?php

declare(strict_types=1);

namespace Ka<PERSON>ino\Model\Users\Entities;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use Nette\Utils\Strings;

/**
 * @ORM\Entity(repositoryClass="Kaufino\Model\Users\Repositories\UserRepository")
 * @ORM\Table(name="kaufino_users_user", uniqueConstraints={
 *      @ORM\UniqueConstraint(name="user_unique", columns={"localization_id", "email"})}
 * )
 */
class User
{
	public const ROLE_MEMBER = "member";
	public const ROLE_ADMIN = "admin";
	public const ROLE_AUTHOR = "author";

	/**
	 * @var int
	 * @ORM\Column(type="integer", nullable=FALSE)
	 * @ORM\Id
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\Kaufino\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	protected $localization;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $email;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $password;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $role;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $name;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	protected $imageUrl;

	/**
	 * @ORM\Column(type="string", nullable=false)
	 */
	protected $surname;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $slug;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private bool $active = false;

	/**
	 * @ORM\Column(type="datetime", nullable=false)
	 */
	protected \DateTime $createdAt;

	/**
	 * @ORM\OneToMany(targetEntity="\Kaufino\Model\Articles\Entities\Article", mappedBy="author")
	 */
	protected $articles;

	public function __construct(Localization $localization, string $email, string $password, string $name, string $surname)
	{
		$this->localization = $localization;
		$this->email = $email;
		$this->password = $password;
		$this->name = $name;
		$this->surname = $surname;
		$this->slug = Strings::webalize($name . '-' . $surname);
		$this->createdAt = new \DateTime();
		$this->role = self::ROLE_AUTHOR;

		$this->articles = new ArrayCollection();
	}

	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * @return string
	 */
	public function getEmail(): string
	{
		return $this->email;
	}

	/**
	 * @return string
	 */
	public function getPassword(): string
	{
		return $this->password;
	}

	public function setPassword(string $password): void
	{
		$this->password = $password;
	}

	/**
	 * @return string
	 */
	public function getRole(): string
	{
		return $this->role;
	}

	/**
	 * @return bool
	 */
	public function isMember(): bool
	{
		return $this->role == self::ROLE_ADMIN;
	}

	/**
	 * @return bool
	 */
	public function isAdmin(): bool
	{
		return $this->role == self::ROLE_ADMIN;
	}

	public function isAuthor(): bool
	{
		return $this->role == self::ROLE_AUTHOR;
	}

	public function getCreatedAt(): \DateTime
	{
		return $this->createdAt;
	}

	public function getFullName(): ?string
	{
		return $this->name && $this->surname ? $this->name . ' ' . $this->surname : null;
	}

	public function getSlug(): ?string
	{
		return $this->slug;
	}

	public function getImageUrl(): ?string
	{
		return $this->imageUrl;
	}

	public function setImageUrl(string $imageUrl): void
	{
		$this->imageUrl = $imageUrl;
	}

	public function getName(): ?string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getSurname(): ?string
	{
		return $this->surname;
	}

	public function setSurname(string $surname): void
	{
		$this->surname = $surname;
	}

	public function getLocalization()
	{
		return $this->localization;
	}

	public function setLocalization(Localization $localization): void
	{
		$this->localization = $localization;
	}

	public function isActive(): bool
	{
		return $this->active;
	}

	public function setActive(): void
	{
		$this->active = true;
	}

	public function setInactive(): void
	{
		$this->active = false;
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Users;

use Ka<PERSON>ino\Model\EntityManager;
use Ka<PERSON>ino\Model\Users\Entities\User;
use Nette;

final class UserManager
{
	use Nette\SmartObject;

	private EntityManager $entityManager;

	public function __construct(EntityManager $entityManager)
	{
		$this->entityManager = $entityManager;
	}

	public function saveUser(User $user): User
	{
		$this->entityManager->persist($user);
		$this->entityManager->flush();

		return $user;
	}
}

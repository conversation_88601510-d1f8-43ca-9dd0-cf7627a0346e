<?php

namespace <PERSON><PERSON><PERSON>\Model\Users;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON>ino\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\Users\Entities\User;
use Ka<PERSON>ino\Model\Users\Repositories\UserRepository;

class UserFacade
{
	/** @var UserRepository */
	private $userRepository;
	private UserManager $userManager;

	public function __construct(UserRepository $userRepository, UserManager $userManager)
	{
		$this->userRepository = $userRepository;
		$this->userManager = $userManager;
	}

	public function createUser(Localization $localization, string $email, string $password, string $name, string $surname): User
	{
		$user = new User($localization, $email, $password, $name, $surname);

		$this->userManager->saveUser($user);

		return $user;
	}

	public function getUsers(): QueryBuilder
	{
		return $this->userRepository->getUsers();
	}

	public function findAuthors()
	{
		return $this->userRepository->findAuthors();
	}

	public function find(int $id)
	{
		return $this->userRepository->find($id);
	}

	public function findAuthorBySlug(string $slug)
	{
		return $this->userRepository->findOneBy(['slug' => $slug, 'role' => User::ROLE_AUTHOR]);
	}

	public function saveUser(User $user)
	{
		$this->userManager->saveUser($user);
	}

	public function findByEmail(string $email): ?User
	{
		return $this->userRepository->findUserByEmail($email);
	}
}

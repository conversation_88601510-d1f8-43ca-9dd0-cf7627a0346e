<?php

namespace <PERSON><PERSON><PERSON>\Model\Users;

use Nette\Security\AuthenticationException;
use Nette\Security\IAuthenticator;
use Nette\Security\Identity;
use Nette\Security\IIdentity;

class UserAuthenticator implements IAuthenticator
{
	private UserFacade $userFacade;

	public function __construct(UserFacade $userFacade)
	{
		$this->userFacade = $userFacade;
	}

	public function authenticate(array $credentials): IIdentity
	{
		[$email, $password] = $credentials;

		$userEntity = $this->userFacade->findByEmail($email);

		if ($userEntity && $userEntity->isAdmin() === false && $userEntity->isAuthor() === false) {
			$userEntity = null;
		}

		if (!$userEntity) {
			throw new AuthenticationException('The email is incorrect.', self::IDENTITY_NOT_FOUND);
		} elseif ($password != $userEntity->getPassword()) {
			throw new AuthenticationException('The password is incorrect.', self::INVALID_CREDENTIAL);
		} elseif ($userEntity->isActive() === false) {
			throw new AuthenticationException('The account is not active.', self::NOT_APPROVED);
		}

		return new Identity($userEntity->getId(), $userEntity->getRole(), ["email" => $userEntity->getEmail()]);
	}
}

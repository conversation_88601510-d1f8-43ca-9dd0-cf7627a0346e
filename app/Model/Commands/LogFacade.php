<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Commands;

use <PERSON><PERSON><PERSON>\Model\Commands\Entities\Log;

class LogFacade
{
	/** @var LogManager */
	private $logManager;

	public function __construct(LogManager $logManager)
	{
		$this->logManager = $logManager;
	}

	public function createLog($command, $note = null): Log
	{
		return $this->logManager->createLog($command, $note);
	}

	public function finishLog(Log $log, $note = null): Log
	{
		return $this->logManager->finishLog($log, $note);
	}

	public function getX(): string
	{
		return 'X';
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Model\Leaflets;

use Nette\Utils\Json;
use Tracy\Debugger;

class AnnotationCollection
{
	private $annotations = [];

	public function __construct(array $annotations = [])
	{
		$this->annotations = $annotations;
	}

	public function createAnnotationAbsolute($xAbsolute, $yAbsolute, $widthAbsolute, $heightAbsolute, $tag, $comment, $widthRatio, $heightRatio)
	{
		$this->annotations[] = new Annotation(
			$xAbsolute / $widthRatio,
			$yAbsolute / $heightRatio,
			$widthAbsolute / $widthRatio,
			$heightAbsolute / $heightRatio,
			$tag,
			$comment
		);

		//Debugger::dump($this->annotations);
		//Debugger::dump($widthRatio);
		//Debugger::dump($heightRatio);

		//Debugger::dump($xAbsolute);
		//Debugger::dump($yAbsolute);
		//Debugger::dump($widthAbsolute);
		//Debugger::dump($heightAbsolute);

		return $this;
	}

	public function createAnnotationRelative($x, $y, $width, $height, $tag, $comment)
	{
		$this->annotations[] = new Annotation($x, $y, $width, $height, $tag, $comment);

		return $this;
	}

	public function exportAnnotatorArray(int $widthRatio, $heightRatio)
	{
		$regions = [];

		foreach ($this->annotations as $annotation) {
			$regions[] = $this->exportAnnotationArray($annotation, $widthRatio, $heightRatio);
		}

		return $regions;
	}

	public function toJson()
	{
		$data = [];

		foreach ($this->annotations as $annotation) {
			$data['annotations'][] = $annotation->toArray();
		}

		return Json::encode($data);
	}

	public static function fromJson(string $json)
	{
		$data = Json::decode($json, Json::FORCE_ARRAY);

		$annotations = [];
		foreach ($data['annotations'] as $annotation) {
			$annotations[] = Annotation::fromArray($annotation);
		}

		return new AnnotationCollection($annotations);
	}

	private function exportAnnotationArray(Annotation $annotation, int $widthRatio, int $heightRatio)
	{
		return [
			'@context' => 'http://www.w3.org/ns/anno.jsonld',
			'id' => uniqid(),
			'type' => 'Annotation',
			'body' => [
				[
					'type' => 'TextualBody',
					'purpose' => 'tagging',
					'value' => $annotation->getTag(),
				],
				[
					'type' => 'TextualBody',
					'purpose' => 'commenting',
					'value' => $annotation->getComment(),
				],
			],
			'target' => [
				'selector' => [
					'type' => 'FragmentSelector',
					'conformsTo' => 'http://www.w3.org/TR/media-frags/',
					'value' => 'xywh=pixel:' . $annotation->getX() * $widthRatio . ',' . $annotation->getY() * $heightRatio . ',' . $annotation->getWidth() * $widthRatio . ',' . $annotation->getHeight() * $heightRatio,
				],
			],
		];
	}

	public static function importAnnotatorArray(array $input, int $widthRatio, int $heightRatio)
	{
		$annotationCollection = new AnnotationCollection();

		foreach ($input as $item) {
			$coordinates = $item['target']['selector']['value'];
			list($prefix, $values) = explode(':', $coordinates);
			list($x, $y, $width, $height) = explode(',', $values);

			$annotationCollection->createAnnotationAbsolute(
				$x,
				$y,
				$width,
				$height,
				$item['body'][0]['value'],
				'comment',
				$widthRatio,
				$heightRatio
			);
		}

		return $annotationCollection;
	}

	public static function importDetectObjectsJson(string $json, string $ocr = null)
	{
		$annotationCollection = new AnnotationCollection();

		$data = Json::decode($json);

		if ($ocr) {
			$ocrData = Json::decode($ocr);
		}

		foreach ($data->predictions as $item) {
			if ($item->probability < 0.96) {
				continue;
			}

			$comment = '';

			$left = $item->boundingBox->left;
			$top = $item->boundingBox->top;
			$width = $item->boundingBox->width;
			$height = $item->boundingBox->height;

			if ($ocr) {
				$comment = '';
				//$comment = $left . ' ' . ($left+$width) . "\n";
				//$comment .= $top . ' ' . ($top+$height) . "\n";

				foreach ($ocrData->regions as $region) {
					foreach ($region->lines as $line) {
						list ($x, $y, $width_, $height_) = explode(',', $line->boundingBox);

						if (!($x >= $left && $x < ($left + $width))) {
							continue;
						}

						if (!($y >= $top && $y < ($top + $height))) {
							continue;
						}

						foreach ($line->words as $word) {
							$comment .= $word->text . ' ';
						}
						$comment . "\n";
					}
				}
			}

			$annotationCollection->createAnnotationRelative(
				$left,
				$top,
				$item->boundingBox->width,
				$item->boundingBox->height,
				'product',
				$comment
			);
		}

		return $annotationCollection;
	}

	/**
	 * @return array
	 */
	public function getAnnotations(): array
	{
		return $this->annotations;
	}

	/**
	 * @param array $annotations
	 */
	public function setAnnotations(array $annotations): void
	{
		$this->annotations = $annotations;
	}

	public function getOcr(Annotation $annotation, $ocr)
	{
		$ocrData = Json::decode($ocr);

		$left = $annotation->getX();
		$top = $annotation->getY();
		$width = $annotation->getWidth();
		$height = $annotation->getHeight();

		$comment = '';
		//$comment = $left . ' ' . ($left+$width) . "\n";
		//$comment .= $top . ' ' . ($top+$height) . "\n";

		foreach ($ocrData->regions as $region) {
			foreach ($region->lines as $line) {
				list ($x, $y, $width_, $height_) = explode(',', $line->boundingBox);

				if (!($x >= $left && $x < ($left + $width))) {
					continue;
				}

				if (!($y >= $top && $y < ($top + $height))) {
					continue;
				}

				foreach ($line->words as $word) {
					$comment .= $word->text . ' ';
				}
				$comment . "\n";
			}
		}

		return $comment;
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ino\Model\Leaflets;

use DateTime;
use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Geo\Entities\City;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\LeafletPage;
use Ka<PERSON><PERSON>\Model\Leaflets\Repositories\LeafletPageRepository;
use Ka<PERSON>ino\Model\Leaflets\Repositories\LeafletRepository;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\ResponseCacheManager;
use Ka<PERSON><PERSON>\Model\Shops\Entities\Shop;
use Ka<PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

class LeafletFacade
{
	/** @var EntityManager */
	private $entityManager;

	/** @var LeafletRepository */
	private $leafletRepository;
	/**
	 * @var LeafletPageRepository
	 */
	private $leafletPageRepository;

	/** @var ResponseCacheManager */
	private $responseCacheManager;

	public function __construct(EntityManager $entityManager, LeafletRepository $leafletRepository, LeafletPageRepository $leafletPageRepository, ResponseCacheManager $responseCacheManager)
	{
		$this->entityManager = $entityManager;
		$this->leafletRepository = $leafletRepository;
		$this->leafletPageRepository = $leafletPageRepository;
		$this->responseCacheManager = $responseCacheManager;
	}

	public function findLeaflet(int $id): ?Leaflet
	{
		/** @var ?Leaflet $leaflet */
		$leaflet = $this->leafletRepository->find($id);

		return $leaflet;
	}

	public function findLeafletByLeafletId(int $leafletId): ?Leaflet
	{
		/** @var ?Leaflet $leaflet */
		$leaflet = $this->leafletRepository->findOneBy(['leafletId' => $leafletId]);

		return $leaflet;
	}

	public function findLeafletBySlug(Localization $localization, string $slug, $onlyValid = true): ?Leaflet
	{
		return $this->leafletRepository->findLeafletBySlug($localization, $slug, $onlyValid);
	}

	public function findPriorityLeaflets(Localization $localization, int $limit, string $websiteType)
	{
		return $this->leafletRepository->findPriorityLeaflets($localization, $limit, $websiteType);
	}

	public function findLeaflets(
		Localization $localization = null,
		?bool $onlyStores = false,
		?int $limit = null,
		?string $websiteType = null,
		$onlyPrimary = false,
		?array $exceptLeaflets = []
	): array {
		$qb = $this->leafletRepository->getLeaflets($localization, true)
			->innerJoin('l.shop', 's')
			->addOrderBy('l.validSince', 'DESC')
			->setMaxResults($limit)
		;

		if ($onlyStores) {
			$qb->andWhere('s.type = :type')
				->setParameter('type', Shop::TYPE_STORE);
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('s.activeOferto = 1');
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$qb->andWhere('s.activeKaufino = 1');
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$qb->andWhere('s.activeLetado = 1');
		} elseif ($websiteType === Website::MODULE_OFERTO_COM) {
			$qb->andWhere('s.activeOfertoCom = true');
		}

		if ($websiteType === Website::MODULE_KAUFINO) {
			$qb->andWhere('s.hidden = 0');
		}

		if ($exceptLeaflets) {
			$qb->andWhere('l NOT IN (:exceptLeaflets)')
				->setParameter('exceptLeaflets', $exceptLeaflets)
			;
		}

		if ($onlyPrimary) {
			$qb->andWhere('l.primary = true');
		}

		return $qb
			->getQuery()
			->getResult();
	}

	public function findArchivedLeaflets(Shop $shop)
	{
		return $this->leafletRepository->findArchivedLeaflets($shop);
	}

	public function findExpiredLeafletsByShop(Shop $shop)
	{
		$qb = $this->leafletRepository->getLeafletsByShop($shop, false, true, null, true);

		return $qb->getResult();
	}

	public function findLeafletsForSitemap(Localization $localization = null, ?bool $onlyStores = false, ?string $websiteType = null): array
	{
		$qb = $this->leafletRepository->getLeaflets($localization, false)
			->innerJoin('l.shop', 's')
			->andWhere('l.archivedAt IS NULL')
			->addOrderBy('l.validSince', 'DESC');

		if ($onlyStores) {
			$qb->andWhere('s.type = :type')
				->setParameter('type', Shop::TYPE_STORE);
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('s.activeOferto = 1');
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$qb->andWhere('s.activeKaufino = true');
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$qb->andWhere('s.activeLetado = 1');
		}

		return $qb
			->getQuery()
			->getResult();
	}

	public function findNewsletters(Localization $localization = null, ?bool $onlyStores = false, ?int $limit = null, ?string $websiteType = null): array
	{
		$qb = $this->leafletRepository->getLeaflets($localization, true)
			->innerJoin('l.shop', 's')
			->andWhere('l.type = :ltype')
			->setParameter('ltype', Leaflet::TYPE_NEWSLETTER)
			->setMaxResults($limit)
			->addOrderBy('l.validSince', 'DESC');

		if ($onlyStores) {
			$qb->andWhere('s.type = :stype')
				->setParameter('stype', Shop::TYPE_STORE);
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('s.activeOferto = 1');
		}

		if ($websiteType === Website::MODULE_OFERTO_COM) {
			$qb->andWhere('s.activeCoupons = true');
		} else {
			$qb->andWhere('s.activeLeaflets = true');
		}

		return $qb
			->getQuery()
			->getResult();
	}

	public function findNewslettersByShop(Shop $shop, ?int $limit = null)
	{
		return $this->leafletRepository->getLeaflets($shop->getLocalization(), true)
			->innerJoin('l.shop', 's')
			->andWhere('s = :shop')
			->setParameter('shop', $shop)
			->andWhere('l.type = :ltype')
			->setParameter('ltype', Leaflet::TYPE_NEWSLETTER)
			->addOrderBy('s.boost', 'DESC')
			->addOrderBy('s.priorityLeaflets', 'DESC')
			->addOrderBy('l.validSince', 'DESC')
			->setMaxResults($limit)
			->getQuery()
			->getResult();
	}

	public function findTopLeafletsByShop(Shop $shop, int $limit = 3)
	{
		return $this->leafletRepository->findTopLeafletsByShop($shop, $limit);
	}

	public function findTopLeaflets(Localization $localization = null, ?bool $onlyStores = false, ?int $limit = null, ?string $websiteType = null): array
	{
		$qb = $this->leafletRepository->getLeaflets($localization, true)
			->innerJoin('l.shop', 's')
			->andWhere('l.top = 1')
			->addOrderBy('s.boost', 'DESC')
			->addOrderBy('s.priorityLeaflets', 'DESC')
			->addOrderBy('l.validSince', 'DESC')
			->setMaxResults($limit);

		if ($onlyStores) {
			$qb->andWhere('s.type = :type')
				->setParameter('type', Shop::TYPE_STORE);
		}

		if ($websiteType === Website::MODULE_OFERTO) {
			$qb->andWhere('s.activeOferto = 1');
		} elseif ($websiteType === Website::MODULE_KAUFINO || $websiteType === Website::MODULE_KAUFINO_SUBDOMAIN) {
			$qb->andWhere('s.activeKaufino = 1');
		} elseif ($websiteType === Website::MODULE_LETADO) {
			$qb->andWhere('s.activeLetado = 1');
		}

		if ($websiteType === Website::MODULE_KAUFINO) {
			$qb->andWhere('s.hidden = 0');
		}

		if ($websiteType === Website::MODULE_OFERTO_COM) {
			$qb->andWhere('s.activeCoupons = true');
		}

		return $qb
			->getQuery()
			->getResult();
	}

	public function findLeafletsByShop(Shop $shop, int $limit = 10, $onlyCurrent = true, bool $withFirstPage = false, ?Leaflet $exceptLeaflet = null, bool $onlyChecked = true): array
	{
		$query = $this->leafletRepository->getLeafletsByShop($shop, $onlyCurrent, $withFirstPage, $exceptLeaflet, null, $onlyChecked)
			->setMaxResults($limit);

		return $query->getResult();
	}

	public function findLeafletsByShops(array $shops, int $limit = 10, $onlyCurrent = true, ?array $exceptLeaflets = null, ?string $websiteType = null)
	{
		$query = $this->leafletRepository->getLeafletsByShops($shops, $onlyCurrent, $exceptLeaflets, $websiteType)
			->setMaxResults($limit);

		return $query->getQuery()->getResult();
	}

	public function findLeafletsByShopsWithoutCities(array $shops, int $limit = 10, $onlyCurrent = true, ?array $exceptLeaflets = [])
	{
		$query = $this->leafletRepository->getLeafletsByShops($shops, $onlyCurrent)
			->leftJoin('l.cities', 'c')
			->andWhere('c.id IS NULL')
		;

		if ($exceptLeaflets) {
			$query->andWhere('l NOT IN (:exceptLeaflets)')
				->setParameter('exceptLeaflets', $exceptLeaflets)
			;
		}

		return $query
			->setMaxResults($limit)
			->getQuery()
			->getResult()
		;
	}

	public function findLeafletsByTag(Tag $shop, ?bool $onlyStores = false, ?int $limit = 10, ?string $websiteType = null): array
	{
		$query = $this->leafletRepository->getLeafletsByTag($shop, true, $onlyStores, $websiteType);
		$query->setMaxResults($limit);

		return $query->getResult();
	}

	public function saveLeaflet(Leaflet $leaflet, bool $resetCache = true)
	{
		$this->entityManager->flush($leaflet);

		if ($resetCache === true) {
			$this->responseCacheManager->clearCacheByTag('leaflets');
			$this->responseCacheManager->clearCacheByTag('shop/' . $leaflet->getShop()->getId());
		}
	}

	public function recomputeTop()
	{
		$queries[] = "UPDATE kaufino_leaflets_leaflet SET priority = (top * 10000) + (is_primary * 1000) + TIMESTAMPDIFF(day, now(), valid_till)";
		$queries[] = sprintf("UPDATE kaufino_leaflets_leaflet SET priority = -1000 WHERE valid_till < '%s'", date('Y-m-d H:i:s'));
		$queries[] = "UPDATE kaufino_leaflets_leaflet SET priority = -10000 WHERE archived_at IS NOT NULL";


		$connection = $this->entityManager->getConnection();
		foreach ($queries as $query) {
			$statement = $connection->prepare($query);
			$statement->execute();
		}
	}

	public function findLeafletPagesToUseOcr($limit = 3): array
	{
		$query = $this->leafletPageRepository->findLeafletPagesToUseOcr($limit);
		$query->setMaxResults($limit);

		return $query->getResult();
	}

	public function findLeafletPagesToCalculateScore($limit = 10): array
	{
		$query = $this->leafletPageRepository->findLeafletPagesToCalculateScore($limit);
		$query->setMaxResults($limit);

		return $query->getResult();
	}

	public function findLeafletPagesToUseDetectObjects($limit = 3): array
	{
		$query = $this->leafletPageRepository->findLeafletPagesToUseDetectObjects($limit);
		$query->setMaxResults($limit);

		return $query->getResult();
	}

	public function findLeafletPagesToAnnotate($limit = 3, ?Localization $localization = null): array
	{
		$query = $this->leafletPageRepository->findLeafletPagesToAnnotate($limit, $localization);
		$query->setMaxResults($limit);

		return $query->getResult();
	}

	public function findCountOfLeafletPagesToAnnotateByLocalization()
	{
		return $this->leafletPageRepository->findCountOfLeafletPagesToAnnotateByLocalization();
	}

	public function getCountOfLeafletPagesToAnnotate(?Localization $localization = null): int
	{
		return $this->leafletPageRepository->getCountOfLeafletPagesToAnnotate($localization);
	}

	public function findLeafletPagesToCreateOffers($limit = 3): array
	{
		$query = $this->leafletPageRepository->findLeafletPagesToCreateOffers($limit);
		$query->setMaxResults($limit);

		return $query->getResult();
	}

	public function findLeafletPage($id)
	{
		return $this->leafletPageRepository->find($id);
	}

	public function saveLeafletPage(LeafletPage $leafletPage)
	{
		$this->entityManager->persist($leafletPage);
		$this->entityManager->flush($leafletPage);
	}

	public function findLeafletPagesByFulltext($keyword, Localization $localization, ?string $websiteType = null, $limit = 25, ?Tag $excludedTag = null, ?bool $onlyCurrent = true)
	{
		return $this->leafletPageRepository->findLeafletPagesByFulltext($keyword, $localization, $websiteType, $limit, $excludedTag, $onlyCurrent)
			->getResult();
	}

	public function findLeafletsByCity(City $city, ?Shop $shop = null, ?int $limit = 10, bool $withoutAllWideLeaflets = false): array
	{
		return $this->leafletRepository->findLeafletsByCity($city, $shop, $limit, $withoutAllWideLeaflets);
	}

	public function findLeafletsToArchive(int $limit = 100)
	{
		return $this->leafletRepository->findLeafletsToArchive($limit);
	}

	public function removeLeafletPage(LeafletPage $leafletPage): void
	{
		$this->entityManager->remove($leafletPage);
		$this->entityManager->flush();
	}

	public function findLeafletsFromOffers(array $offers)
	{
		return $this->leafletRepository->findLeafletsFromOffers($offers);
	}

	public function getAllOcrWords(LeafletPage $leafletPage): ?array
	{
		$ocrOutput = $leafletPage->getOcrOutput();

		if ($ocrOutput === null) {
			return null;
		}

		$ocrOutput = json_decode($ocrOutput, true);

		if (!isset($ocrOutput['regions'])) {
			return null;
		}

		$words = [];

		foreach ($ocrOutput['regions'] as $region) {
			if (isset($region['lines']) === false || is_array($region['lines']) === false) {
				continue;
			}

			foreach ($region['lines'] as $line) {
				if (isset($line['words']) === false || is_array($line['words']) === false) {
					continue;
				}

				foreach ($line['words'] as $word) {
					$words[] = $word['text'];
				}
			}
		}

		return $words;
	}

	public function findTopValidLeafletPagesByScore(Shop $shop, int $limit): ?array
	{
		return $this->leafletPageRepository->findTopValidLeafletPagesByScore($shop, $limit);
	}

	public function findTrendingLeafletPages(Shop $shop): ?array
	{
		return $this->leafletPageRepository->findTrendingLeafletPages($shop);
	}

	public function findIn(array $ids): array
	{
		return $this->leafletRepository->findIn($ids);
	}

	public function findTopLeafletsByShops(array $shops, ?int $limit = null): array
	{
		return $this->leafletRepository->findTopLeafletsByShops($shops, $limit);
	}

	public function findLeafletPageByFile(string $file)
	{
		return $this->leafletPageRepository->findOneBy(['imageUrl' => $file]);
	}

	public function saveLeafletPages(array $leafletPages)
	{
		foreach ($leafletPages as $leafletPage) {
			$this->entityManager->persist($leafletPage);
		}

		$this->entityManager->flush();
	}

	public function findRelatedLeaflet(Leaflet $leaflet)
	{
		return $this->leafletRepository->findRelatedLeaflet($leaflet);
	}

	public function findRelatedLeaflets(Leaflet $leaflet, int $limit = 3)
	{
		return $this->leafletRepository->findRelatedLeaflets($leaflet, $limit);
	}

	public function findLeafletPageToRemoveAnnotatedImage()
	{
		return $this->leafletPageRepository->findLeafletPageToRemoveAnnotatedImage();
	}

	public function findLeafletsToProcessGoogleIndex(int $limit = 100): array
	{
		return $this->leafletRepository->createQueryBuilder('l')
			->addSelect('s')
			->innerJoin('l.shop', 's')
			->leftJoin('l.googleIndex', 'gi')
			->where('l.archivedAt IS NULL')
			->andWhere('l.deletedAt IS NULL')
			->andWhere('s.activeKaufino = true')
			->andWhere('l.validTill > :now')
			->andWhere('l.createdAt >= :today')
			->andWhere('l.localization = 1')
			->andWhere('gi IS NULL')
			->setParameter('now', new DateTime())
			->setParameter('today', (new DateTime())->setTime(0, 0))
			->setMaxResults($limit)
			->orderBy('l.createdAt', 'DESC')
			->getQuery()
			->getResult();
	}
}

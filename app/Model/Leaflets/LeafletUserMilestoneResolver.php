<?php

namespace <PERSON><PERSON><PERSON>\Model\Leaflets;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;

class LeafletUserMilestoneResolver
{
	public function resolveMilestone(Leaflet $leaflet, int $currentPage, int $currentMilestone): array
	{
		$totalPages = $leaflet->getCountOfPages();

		if ($totalPages <= 0) {
			return [
				'isNewMilestone' => false,
				'depthPercent' => 0,
			];
		}

		$progress = ($currentPage / $totalPages) * 100;

		if ($progress < 10) {
			$depthPercent = 0;
		} else {
			$depthPercent = (int) floor($progress / 10) * 10;
			if ($depthPercent > 100) {
				$depthPercent = 100;
			}
		}

		$isNewMilestone = false;

		if ($currentPage === 1 && $currentMilestone < 0) {
			$isNewMilestone = true;
		}

		if ($depthPercent > $currentMilestone && $depthPercent !== 0) {
			$isNewMilestone = true;
		}

		return [
			'isNewMilestone' => $isNewMilestone,
			'depthPercent' => $depthPercent,
		];
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Model\Tracy;

use <PERSON><PERSON><PERSON>\Model\Configuration;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Http\Request;
use Nette\Http\Response;
use Tracy\IBarPanel;

class DomainSwitcherPanel implements IBarPanel
{
	private ?string $domain;
	public Request $request;
	public Response $response;
	private Cache $cache;

	public function __construct(Request $request, Response $response, Storage $storage, Configuration $configuration)
	{
		$this->request = $request;
		$this->response = $response;
		$this->cache = new Cache($storage, 'response');

		$this->domain = $this->request->getCookie('domain');

		$isAdmin = strpos($this->request->getUrl()->getPath(), '/admin') === 0;

		if ($isAdmin === false && $configuration->isDevelopmentMode() && $this->request->getPost('domain')) {
			$domain = $this->request->getPost('domain');
			// $domain = str_replace(['http://', 'https://', 'www.'], '', $domain);

			$this->domain = $domain;
			$this->response->setCookie('domain', $this->domain, '30 days');

			$this->cache->clean([
				$this->cache::ALL => true,
			]);

			$this->response->redirect('/');
		}
	}

	public function getTab(): string
	{
		$domain = $this->domain ?: 'Domain';
		return '
    <span title="Current domain">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418" />
        </svg>
        <span class="tracy-label">' . htmlspecialchars($domain) . '</span>
    </span>';
	}

	public function getPanel()
	{
		ob_start();
		?>
		<h1>Domain Information</h1>
		<div>
			<strong>Domain:</strong> <?php echo htmlspecialchars($this->domain); ?>
		</div>
		<form method="post">
			<label for="domain">Set domain:</label>
			<input type="text" style="border: 1px solid black; padding: 2px 3px" name="domain" id="domain" value="<?php echo htmlspecialchars($this->domain); ?>">
			<input type="submit" value="Save" style="background: #000000; padding: 3px 4px; cursor: pointer; color: #fff">
		</form>
		<?php
		return ob_get_clean();
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Github;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\RequestOptions;
use Nette\Utils\Json;

final class GithubClient
{
	public const DEFAULT_BRANCH = 'master';
	public const GITHUB_REPOS_URL = 'https://api.github.com/repos/';
	public const REPOSITORY_KAUFINO_CONTENT = 'Tipli/kaufino-content';

	public const REPOSITORY_KAUFINO = 'Tipli/kaufino';

	private const TOKEN = 'a00a224150f97d25ec57c1fc51e45410abedd5eb';

	public function updateFile(
		string $repository,
		string $path,
		string $content,
		string $branch = self::DEFAULT_BRANCH,
		$comment = null
	): void {
		if ($content === $this->getFileContent($repository, $path, $branch)) {
			bdump("no changes");
			return; // no changes
		}

		$options = [
			'message' => $comment ? : 'Kaufino app',
			'content' => base64_encode($content),
			'branch' => $branch,
		];

		if ($sha = $this->getFileSha($repository, $path, $branch)) {
			$options['sha'] = $sha;
		}

		$client = new Client();

		$response = $client->request('PUT', self::GITHUB_REPOS_URL . $repository . '/contents/' . $path, [
			RequestOptions::HEADERS => ['Authorization' => 'token ' . self::TOKEN],
			RequestOptions::JSON => $options,
		]);

		$data = $response->getBody()->getContents();
		$data = Json::decode($data);

		if (!$data) {
			throw new \Exception('No data');
		}
	}

	public function getFileContent(
		string $repository,
		string $path,
		string $branch = self::DEFAULT_BRANCH
	): ?string {
		try {
			$client = new Client();

			$response = $client->request('GET', self::GITHUB_REPOS_URL . $repository . '/contents/' . $path . '?ref=' . $branch, [
				RequestOptions::HEADERS => ['Authorization' => 'token ' . self::TOKEN],
			]);

			$data = $response->getBody()->getContents();
			$data = Json::decode($data);

			if (!$data) {
				throw new \Exception('No data');
			}

			return base64_decode($data->content);
		} catch (ClientException $e) {
			return null;
		}
	}

	private function getFileSha(
		string $repository,
		string $path,
		string $branch = self::DEFAULT_BRANCH
	): ?string {
		$client = new Client();
		$data = null;

		try {
			$response = $client->request('GET', self::GITHUB_REPOS_URL . $repository . '/contents/' . $path . '?ref=' . $branch, [
				RequestOptions::HEADERS => ['Authorization' => 'token ' . self::TOKEN],
			]);

			$data = $response->getBody()->getContents();
			$data = Json::decode($data);
		} catch (ClientException $e) {
			if ($e->getCode() === 404) {
				return null;
			}
		}

		if (!$data) {
			throw new \Exception('No data');
		}

		return $data->sha;
	}
}

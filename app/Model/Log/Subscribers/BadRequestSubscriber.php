<?php

namespace <PERSON><PERSON><PERSON>\Model\Log\Subscribers;

use Contributte\Events\Extra\Event\Application\ErrorEvent;
use Nette\Http\Request;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use <PERSON>\Debugger;

class BadRequestSubscriber implements EventSubscriberInterface
{
	/** @var Request */
	private $httpRequest;

	public function __construct(Request $httpRequest)
	{
		$this->httpRequest = $httpRequest;
	}

	public static function getSubscribedEvents(): array
	{
		return [
			ErrorEvent::class => 'createBadRequest',
		];
	}

	public function createBadRequest(ErrorEvent $errorEvent)
	{
		$exception = $errorEvent->getThrowable();

		if (in_array($exception->getCode(), [500])) {
			$url = $this->httpRequest->getUrl();
			Debugger::log($exception, 'bad-request');
			Debugger::log($url, 'bad-request');
		}
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ino\Model\Sitemap;

class Item
{
	/** @var string */
	private $url;

	/** @var \DateTime */
	private $lastModified;

	/** @var string */
	private $frequency;

	/** @var float */
	private $priority;

	public const FREQUENCY_DEFAULT = '1';
	public const FREQUENCY_WEEKLY = 'weekly';
	public const FREQUENCY_MONTHLY = 'monthly';
	public const FREQUENCY_DAILY = 'daily';

	public const PRIORITY_DEFAULT = '1';

	public function __construct(string $url, \DateTime $lastModified = null, ?string $frequency, ?string $priority)
	{
		$this->url = $url;
		$this->lastModified = $lastModified;
		$this->frequency = $frequency;
		$this->priority = $priority;
	}

	/**
	 * @return string
	 */
	public function getUrl(): string
	{
		return $this->url;
	}

	/**
	 * @return \DateTime
	 */
	public function getLastModified(): ?\DateTime
	{
		return $this->lastModified;
	}

	/**
	 * @return string
	 */
	public function getFrequency(): ?string
	{
		return $this->frequency;
	}

	/**
	 * @return float
	 */
	public function getPriority(): ?string
	{
		return $this->priority;
	}
}

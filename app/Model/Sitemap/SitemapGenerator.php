<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Sitemap;

use <PERSON><PERSON><PERSON>\Model\Articles\ArticleFacade;
use <PERSON><PERSON><PERSON>\Model\Geo\Entities\City;
use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use Kaufino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\Entities\Store;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Tags\TagFacade;
use Ka<PERSON><PERSON>\Model\Users\UserFacade;
use Ka<PERSON><PERSON>\Model\Websites\Entities\Website;
use Nette\Application\LinkGenerator;
use Nette\Utils\Strings;

class SitemapGenerator
{
	/** @var LinkGenerator */
	private $linkGenerator;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var LeafletFacade */
	private $leafletFacade;

	/** @var TagFacade */
	private $tagFacade;

	/** @var GeoFacade */
	private $geoFacade;

	/** @var ArticleFacade */
	private $articleFacade;

	/** @var UserFacade */
	private $userFacade;

	public function __construct(LinkGenerator $linkGenerator, ShopFacade $shopFacade, LeafletFacade $leafletFacade, TagFacade $tagFacade, GeoFacade $geoFacade, ArticleFacade $articleFacade, UserFacade $userFacade)
	{
		$this->linkGenerator = $linkGenerator;
		$this->shopFacade = $shopFacade;
		$this->leafletFacade = $leafletFacade;
		$this->tagFacade = $tagFacade;
		$this->geoFacade = $geoFacade;
		$this->articleFacade = $articleFacade;
		$this->userFacade = $userFacade;
	}

	public function generateFeed(Website $website, $presenterName): SitemapFeed
	{
		if (Strings::contains($presenterName, 'KaufinoSubdomain')) {
			return $this->generateKaufinoSubdomain($website);
		} elseif (Strings::contains($presenterName, 'Kaufino')) {
			return $this->generateKaufino($website);
		} elseif (Strings::contains($presenterName, 'Letado')) {
			return $this->generateLetado($website);
		} elseif (Strings::contains($presenterName, 'OfertoCom')) {
			return $this->generateOfertoCom($website);
		} else {
			return $this->generateOferto($website);
		}
	}

	public function generateShopsFeed(Website $website): SitemapFeed
	{
		$sitemapFeed = new SitemapFeed();

		$localization = $website->getLocalization();

		$shops = $this->shopFacade->findLeafletShops($localization, false, null, $website->getModule());

		foreach ($shops as $shop) {
			$linkParams = ['shop' => $shop];

			if ($website->isOfertoCom() === false && $website->isOferto() === false) {
				$linkParams['region'] = $localization->getRegion();
			}

			$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':Shop:shop', $linkParams), new \DateTime(), Item::FREQUENCY_DAILY, Item::PRIORITY_DEFAULT));
		}

		return $sitemapFeed;
	}

	public function generateLeafletsFeed(Website $website): SitemapFeed
	{
		$sitemapFeed = new SitemapFeed();

		$localization = $website->getLocalization();

		$leaflets = $this->leafletFacade->findLeafletsForSitemap($localization, true, $website->getModule());

		foreach ($leaflets as $leaflet) {
			$linkParams = ['shop' => $leaflet->getShop(), 'leaflet' => $leaflet];

			if ($website->isOfertoCom() === false && $website->isOferto() === false) {
				$linkParams['region'] = $localization->getRegion();
			}

			$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':Leaflet:leaflet', $linkParams), $leaflet->getUpdatedSitemapAt(), Item::FREQUENCY_DAILY, $leaflet->isExpired() ? '0.3' : '1'));
		}

		return $sitemapFeed;
	}

	public function generateTagsFeed(Website $website): SitemapFeed
	{
		$sitemapFeed = new SitemapFeed();

		$localization = $website->getLocalization();

		$shopsTags = $this->tagFacade->findTags($localization, Tag::TYPE_SHOPS, 1000);
		$offersTags = $this->tagFacade->findTags($localization, Tag::TYPE_OFFERS, 1000);

		/** @var Tag $tag */
		foreach (array_merge($shopsTags, $offersTags) as $tag) {
			$linkParams = ['tag' => $tag];
			if ($website->isOfertoCom() === false && $website->isOferto() === false) {
				$linkParams['region'] = $localization->getRegion();
			}

			if ($website->isKaufino() && $tag->isOffersType()) {
				$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':Offers:tag', $linkParams), new \DateTime(), Item::FREQUENCY_WEEKLY, '0.8'));
			} else {
				$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':Tag:tag', $linkParams), new \DateTime(), Item::FREQUENCY_WEEKLY, '0.8'));
			}
		}

		return $sitemapFeed;
	}

	public function generateStoreFeed(Website $website): SitemapFeed
	{
		$sitemapFeed = new SitemapFeed();

		$localization = $website->getLocalization();

		$stores = $this->geoFacade->findStores($localization, null, $website->getModule());
		/** @var Store $store */
		foreach ($stores as $store) {
			$shop = $store->getShop();

			if ($shop->hasActiveCities() === false) {
				continue;
			}

			$city = $store->getCity();
			if ($city->isNoIndex()) {
				continue;
			}

			if (($website->isKaufino() || $website->isKaufinoSubdomain()) && $shop->isActiveKaufino() === false) {
				continue;
			}

			if ($website->isOferto() && $shop->isActiveOferto() === false) {
				continue;
			}

			if (($website->isLetado() || $website->isLetadoSubdomain()) && $shop->isActiveLetado() === false) {
				continue;
			}

			$linkParams = ['city' => $city, 'shop' => $shop, 'store' => $store];
			if ($website->isOfertoCom() === false && $website->isOferto() === false) {
				$linkParams['region'] = $localization->getRegion();
			}

			$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':City:store', $linkParams), new \DateTime(), Item::FREQUENCY_WEEKLY, '0.4'));
		}

		return $sitemapFeed;
	}

	public function generateArticlesFeed(Website $website): ?SitemapFeed
	{
		$localization = $website->getLocalization();

		$sitemapFeed = new SitemapFeed();

		if ($localization->hasArticles() === false) {
			return $sitemapFeed;
		}

		if (($articles = $this->articleFacade->findArticles($website)) === null) {
			return $sitemapFeed;
		}

		$linkParams = [];

		if ($website->isOfertoCom() === false && $website->isOferto() === false) {
			$linkParams['region'] = $localization->getRegion();
		}

		$authors = [];

		$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':Articles:articles', $linkParams), new \DateTime(), Item::FREQUENCY_WEEKLY, Item::PRIORITY_DEFAULT));

		foreach ($articles as $article) {
			$linkParams['article'] = $article;
			$authors[] = $article->getAuthor();

			$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':Article:article', $linkParams), new \DateTime(), Item::FREQUENCY_WEEKLY, Item::PRIORITY_DEFAULT));
		}

		if ($authors) {
			foreach ($authors as $author) {
				$linkParams['author'] = $author;

				$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':Articles:author', $linkParams), new \DateTime(), Item::FREQUENCY_WEEKLY, Item::PRIORITY_DEFAULT));
			}
		}


		return $sitemapFeed;
	}

	public function generateCityFeed(Website $website): SitemapFeed
	{
		$sitemapFeed = new SitemapFeed();

		$localization = $website->getLocalization();

		$cities = $this->geoFacade->findCities($localization, null, $website->getModule());

		/** @var City $city */
		foreach ($cities as $city) {
			if ($city->isNoIndex()) {
				continue;
			}

			$linkParams = ['city' => $city];
			if ($website->isOfertoCom() === false && $website->isOferto() === false) {
				$linkParams['region'] = $localization->getRegion();
			}
			$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':City:city', $linkParams), new \DateTime(), Item::FREQUENCY_WEEKLY, '0.7'));

			if ($city->isActiveBrands($website->getModule())) {
				$shops = $city->getShops();

				/** @var Shop $shop */
				foreach ($shops as $shop) {
					if ($shop->hasActiveCities() === false) {
						continue;
					}

					if (($website->isKaufino() || $website->isKaufinoSubdomain()) && $shop->isActiveKaufino() === false) {
						continue;
					}

					if ($website->isOferto() && $shop->isActiveOferto() === false) {
						continue;
					}

					if (($website->isLetado() || $website->isLetadoSubdomain()) && $shop->isActiveLetado() === false) {
						continue;
					}

					$linkParams = ['city' => $city, 'shop' => $shop];
					if ($website->isOfertoCom() === false && $website->isOferto() === false) {
						$linkParams['region'] = $localization->getRegion();
					}

					if ($shop->isActiveKaufino() && $shop->isStore()) {
						$sitemapFeed->addItem(new Item($this->linkGenerator->link($this->transformModuleNameToCamelCase($website->getModule()) . ':City:shop', $linkParams), new \DateTime(), Item::FREQUENCY_WEEKLY, '0.4'));
					}
				}
			}
		}
		return $sitemapFeed;
	}

	private function transformModuleNameToCamelCase(string $input): string
	{
		$input = Strings::firstUpper($input);

		return str_replace('_', '', ucwords($input, '_'));
	}

	public function generateKaufinoSubdomain(Website $website): SitemapFeed
	{
		return $this->generateShopsFeed($website);
	}

	public function generateKaufino(Website $website): SitemapFeed
	{
		return $this->generateShopsFeed($website);
	}

	public function generateLetado(Website $website): SitemapFeed
	{
		return $this->generateShopsFeed($website);
	}

	public function generateOfertoCom(Website $website): SitemapFeed
	{
		return $this->generateShopsFeed($website);
	}

	public function generateOferto(Website $website): SitemapFeed
	{
		return $this->generateShopsFeed($website);
	}
}

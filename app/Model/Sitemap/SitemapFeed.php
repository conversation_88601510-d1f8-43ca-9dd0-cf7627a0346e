<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Model\Sitemap;

class SitemapFeed implements \Iterator
{
	/** @var int  */
	private $position = 0;

	/** @var array  */
	private $items = [];

	public function __construct()
	{
		$this->position = 0;
	}

	public function addItem(Item $item)
	{
		$this->items[] = $item;
	}

	public function rewind(): void
	{
		$this->position = 0;
	}

	public function current(): mixed
	{
		return $this->items[$this->position];
	}

	public function key(): mixed
	{
		return $this->position;
	}

	public function next(): void
	{
		++$this->position;
	}

	public function valid(): bool
	{
		return isset($this->items[$this->position]);
	}
}

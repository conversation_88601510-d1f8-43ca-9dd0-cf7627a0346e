{block scripts}
    {include parent}

    <script type="application/ld+json" n:if="count($leaflets)">
        {
            "@context": "https://schema.org",
            "@type": "OfferCatalog",
            "name": {_"$websiteType.homepage.h1"},
            "url": {$presenter->link('//this')},
            "itemListElement": [
                {foreach $leaflets as $key => $leaflet}
                    {
                        "@type": "SaleEvent",
                        "name": {$leaflet->getName()},
                        "url": {$presenter->link('//Leaflet:leaflet', $leaflet->getShop(), $leaflet)},
                        "startDate": {$leaflet->getValidSince()|date:'Y-m-d'},
                        "endDate": {$leaflet->getValidTill()|date:'Y-m-d'},
                        "location": {
                            "@type": "Place",
                            "name": {$leaflet->getShop()->getName()},
                            "url": {$presenter->link('//Shop:shop', $leaflet->getShop())}
                        }
                    }{sep},{/sep}
                {/foreach}
            ]
        }
    </script>
{/block}

{block description}{_"$websiteType.leaflets.text"}{/block}

{block title}{_"$websiteType.leaflets.title"}{/block}

{block content}
<div class="container">
    <div class="mb-6">
        <h1 class="k__title ta-center mt-5 mb-4">{if $localization->isCzech()}{_"$websiteType.leaflets.h1"}{else}{_"$websiteType.leaflets.title"}{/if}</h1>
        <p class="k__text ta-center mw-700 mb-0">{_"$websiteType.leaflets.text"}</p>
    </div>

    <div class="k-leaflets__wrapper k-leaflets__wrapper--xs-mx">
        {foreach $leaflets as $leaflet}
            <div class="k-leaflets__item mb-5">                
                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                    <picture>
                        <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                        <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                    </picture>                        
                </a>
                <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}</p>
                <div class="k-leaflets__title mt-0 mb-0">
                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                        {if $leaflet->isChecked() === false}
                            {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                        {else}
                            {$leaflet->getName()}
                        {/if}
                    </a>
                </div>                                
            </div>            
        {/foreach}
    </div>
</div>

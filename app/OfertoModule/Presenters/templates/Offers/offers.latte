{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Lists',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block scripts}
    {include parent}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "ItemList",
            "itemListElement": [
              {foreach $offers as $offer}
                  {var $offerLeaflet = $offer->getLeafletPage()->getLeaflet()}
                {
                    "@type": "Product",
                    "name": {$offer->getName()},
                    "url": {link //Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet, page => $offer->getLeafletPage()->getPageNumber()},
                    "image": {$offer->getImageUrl()|image:300,300,'fit','webp'},
                    "offers": {
                        "@type": "Offer",
                        "priceCurrency": {_oferto.currency},
                        "price": {$offer->getCurrentPrice()},
                        "availability": "https://schema.org/InStock",
                        "seller": {
                            "@type": "Organization",
                            "name": {$offer->getShop()->getName()}
                        }
                    },
                    "priceValidUntil": {$offer->getvalidTill()|date:'Y-m-d'},
                    "priceSpecification": {
                      "@type": "UnitPriceSpecification",
                      "price": {$offer->getCurrentPrice()},
                      "priceCurrency": {_oferto.currency},
                      {if $offer->getCommonPrice() && $offer->getCommonPrice() > $offer->getCurrentPrice()}
                      "referencePrice": {$offer->getCommonPrice()},
                      {/if}
                      "unitText": {_oferto.currency}
                    }
                }{sep},{/sep}
            {/foreach}
            ]
        }
    </script>
{/block}

{block description}{_kaufino.offer.metaDescription}{/block}

{block content}
<div class="container">
    <div class="mb-4">
        <h1 n:block="title" class="k__title ta-center mt-4 mt-sm-5 mb-3">{_kaufino.offer.title}</h1>
        <p class="k__text ta-center mw-700 mb-3">{_kaufino.offer.text}</p>						    
    </div>

    <div n:if="count($offersTags) > 0">                    
        <div class="k-tag mb-3">
            {foreach $offersTags as $tag}
                <div class="k-tag__inner">
                    <a n:href="Tag:tag $tag" class="k-tag__item">{$tag->getName()}</a>
                </div>
            {/foreach}
        </div>
    </div>

    <div n:if="count($offers) > 0">
        <h2 id="offers" class="k__title ta-center mt-4 mt-sm-5 mb-4">{_kaufino.offer.whatOnSale}</h2>

        <div class="k-offers">
            {foreach $offers as $offer}
                {continueIf !$offer->getLeafletPage()}
                {include '../components/offer-item.latte', offer => $offer}
            {/foreach}
        </div>
    </div>
</div>

{var $interval = 3}

<div class="k-paginator__wrapper">
    <a n:if="$currentPage > 1" n:href="this, page => $currentPage-1" class="k-paginator__button">«</a>
    <a n:if="$currentPage > 1+$interval" n:href="this, page => 1" class="k-paginator__item">1</a>
    <small n:if="max(1, $currentPage-$interval) > 1+1" class="k-paginator__separator">...</small>

    {foreach range(max(1, $currentPage-$interval), min($countOfPages, $currentPage+$interval)) as $pageNumber}
        <a n:href="this, page => $pageNumber" n:class="$currentPage == $pageNumber ? active, k-paginator__item">{$pageNumber}</a>
    {/foreach}

    <small n:if="min($countOfPages, $currentPage+$interval) < $countOfPages-1" class="k-paginator__separator">...</small>
    <a n:if="$currentPage < $countOfPages-$interval" n:href="this, page => $countOfPages" class="k-paginator__item">{$countOfPages}</a>
    <a n:if="$currentPage < $countOfPages" n:href="this, page => $currentPage+1" class="k-paginator__button">»</a>
</div>
{capture $cityLink}<a href="{link City:city $city}">{_kaufino.city.shop.cityLink, [city => $city->getName()]}</a>{/capture}
{capture $shopLink}<a href="{link Shop:shop $shop}">{_kaufino.city.shop.shopLink, [shop => $shop->getName()]}</a>{/capture}

{capture $stores}
    {var $uniqueStores = []}
    {foreach $shops as $_shop}
        {skipIf $_shop == $shop}
        {var $uniqueStores[$_shop->getId()] = $_shop}
        {breakIf $iterator->counter > 2}
    {/foreach}

    {var $countOfStores = count($uniqueStores)}
    {foreach $uniqueStores as $store}
        {if $iterator->isLast() && $countOfStores > 1} {_kaufino.city.city.generatedText.and} {/if}
        <a n:href="City:shop $city, $store">{_"$websiteType.city.shop.leafletStores.store", [brand => $store->getName(), city => $city->getName()]}</a>{if $iterator->getCounter() < $countOfStores-1 && $countOfStores > 2}, {/if}
    {/foreach}
{/capture}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_"$websiteType.city.shop.metaTitle", [city => $city->getName(), shop => $shop->getName(), brand => $shop->getName()]}
    {/if}    
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
{_"$websiteType.city.shop.metaDescription", [city => $city->getName(), shop => $shop->getName(), cityLink => $cityLink, stores =>  ($stores |spaceless|stripHtml|trim), shopLink => $shopLink]|noescape}
    {/if}
    
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_"$websiteType.navbar.leaflets"}</a> |
            <a n:href="City:city $city" class="link">{$city->getName()}</a> |
            <span class="color-grey">{$shop->getName()}</span>
        </p>
    </div>
{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">
        <div class="leaflet__content">
            <div class="w100">                
                <div class="k-profile-header k-profile-header--sm-center">                    
                    <a href="{link Shop:shop $shop}" title="{_kaufino.city.shop.shopLeaflet, [brand => $shop->getName()]}" class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
                        <picture>
                            <source 
                                data-srcset="
                                    {$shop->getLogoUrl() |image:160,140,'fit','webp'} 1x,
                                    {$shop->getLogoUrl() |image:320,280,'fit','webp'} 2x
                                " 
                                type="image/webp"
                            >                            
                            <img 
                                src="{$basePath}/images/placeholder-80x70.png" 
                                data-srcset="
                                    {$shop->getLogoUrl() |image:160,140} 1x,
                                    {$shop->getLogoUrl() |image:320,280} 2x
                                " 
                                width="160" 
                                height="140" 
                                alt="{$shop->getName()}" 
                                class="lazyload k-profile-header__logo"
                            >
                        </picture>							
                    </a>
                    
                    <div class="k-profile-header__content">
                        <h1 class="k-profile-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_"$websiteType.city.shop.title", [city => $city->getName(), shop => $shop->getName(), brand => $shop->getName()]}
                            {/if}                            
                        </h1>
                        
                        <p class="k-profile-header__text mw-800 ml-0">     
                            {if $pageExtension && $pageExtension->getShortDescription()}
                                {$pageExtension->getShortDescription()}
                            {else}
                                {_"$websiteType.city.shop.text", [city => $city->getName(), cityLink => $cityLink, shop => $shop->getName(), brand => $shop->getName(), shopLink => $shopLink, stores => ($stores |trim)]|noescape}
                            {/if}
                        </p>                        
                    </div>                                       
                </div>   

                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper">
                        {foreach $leaflets as $leaflet}

                            {if false && $iterator->counter == 1}
                                <div class="k-leaflets__item k-leaflets__item--first mb-3">                                    
                                    
                                    <!-- Vypis mesta - brand - Responsive - 2 -->
                                    <ins class="adsbygoogle adslot-1" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="7395466606" data-ad-format="auto" data-full-width-responsive="true"></ins>
                                    
                                    <script>
                                        (adsbygoogle = window.adsbygoogle || []).push({});
                                    </script>
                                </div>
                            {/if}

                            {include '../components/leaflet.latte', leaflet => $leaflet}
                        {/foreach}
                    </div>
                {else}
                    <div class="alert alert-info mx-3">{_kaufino.tag.noLeaflets}</div>
                {/if}

                {if count($similarLeaflets) > 0}
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">{_kaufino.city.store.sections.leaflets}</h2>

                    <div class="k-leaflets__wrapper k-leaflets__wrapper--5">
                        {foreach $similarLeaflets as $leaflet}
                            {breakIf $iterator->getCounter() > 10}

                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 18 ? 'hidden' : ''}
                        {/foreach}
                    </div>
                {/if}

                <div n:if="count($shops) > 1" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.city.shop.otherShops, [city => $city->getName()]}</h2>
                    <div class="k-shop">
                        {foreach $shops as $shopItem}
                            {continueIf $shopItem == $shop}

                            <a n:href="City:shop $city, $shopItem" class="k-shop__item {$iterator->counter > 24 ? 'hidden' : ''}">
                                <span class="k-shop__image-wrapper">
                                    <picture>
                                        <source data-srcset="{$shopItem->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shopItem->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                                    </picture>
                                </span>
                                <small class="k-shop__title">{$shopItem->getName()} {$city->getName()}</small>
                            </a>
                        {/foreach}
                    </div>

                    <p n:if="count($shops) > 25" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
                    </p>
                </div>

                <div n:if="count($cityStores) > 0">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_'kaufino.city.shop.storesTitle', ['brand' => $shop->getName(), 'city' => $city->getName()]}</h2>

                    <div class="k-tag">
                        {foreach $cityStores as $store}
                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:store $city, $store->getShop(), $store" class="k-tag__item">{$shop->getName()} {$store->getFullStreet()}</a>
                            </span>
                        {/foreach}
                    </div>

                    <p n:if="count($cityStores) > 11" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-tag">{_'kaufino.city.city.storesMoreButton'}</button>
                    </p>
                </div>

                {*
                <div n:if="count($nearestCities)">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_kaufino.city.city.nearestCityWithShop, [shopName =>$shop->getName()]}
                    </h2>

                    <p class="k-tag mb-5">
                        {foreach $nearestCities as $nearestCity}
                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:shop $nearestCity, $shop" class="k-tag__item">{$shop->getName()} {$nearestCity->getName()}</a>
                            </span>

                            {breakIf $iterator->getCounter() > 12}
                        {/foreach}
                    </p>
                </div>

                <div n:if="count($nearestCities)">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_kaufino.city.city.nearestCity}
                    </h2>

                    <p class="k-tag mb-4">
                        {foreach $nearestCities as $nearestCity}
                            {breakIf $iterator->getCounter() > 24}

                            <span class="k-tag__inner {$iterator->counter > 12 ? 'hidden'}">
                                <a n:href="City:city $nearestCity" class="k-tag__item">{$nearestCity->getName()}</a>
                            </span>
                        {/foreach}
                    </p>
                    <p n:if="count($nearestCities) > 11" class="d-flex">
                        <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
                        <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
                    </p>
                </div>
                *}

                <div class="k-content ml-0">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_"$websiteType.city.shop.h2", [shop => $shop->getName(), brand => $shop->getName(), city => $city->getName()]}
                    </h2>

                    {var $currentLeaflet = count($leaflets) ? $leaflets[0] : null}
                    {if $currentLeaflet}
                        {capture $currentLeafletValidSince}{$currentLeaflet->getValidSince()|localDate:'long'}{/capture}
                        {capture $currentLeafletValidTill}{$currentLeaflet->getValidTill()|localDate:'long'}{/capture}
                        {capture $currentLeafletUrl}{link Leaflet:leaflet $currentLeaflet->getShop(), $currentLeaflet}{/capture}
                    {/if}

                    {capture $citiesInText |spaceless|trim}
                        {var $citiesToUse = []}
                        {foreach $nearestCities as $_city}
                            {skipIf $_city == $city}
                            {skipIf $shop->hasCity($_city) === false}

                            {breakIf $iterator->getCounter() >= 4}

                            {var $citiesToUse[] = $_city}
                        {/foreach}

                        {foreach $citiesToUse as $_city}
                            <a n:href="City:shop $_city, $shop" title="{_"$websiteType.city.shop.citiesInText", [brand => $shop->getName(), city => $city->getName()]}">{$shop->getName()} {$_city->getName()}</a>{sep}, {/sep}
                        {/foreach}
                    {/capture}

                    {capture $storesInText}
                        {var $uniqueStores = []}
                        {foreach $shops as $_shop}
                            {skipIf $_shop == $shop}
                            {skipIf !$_shop->hasCity($city)}
                            {var $uniqueStores[$_shop->getId()] = $_shop}
                            {breakIf $iterator->counter > 3}
                        {/foreach}

                        {foreach $uniqueStores as $store}
                            <a n:href="Shop:shop $store">{$store->getName()}</a>{sep}, {/sep}
                        {/foreach}
                    {/capture}

                    {capture $leafletsUrl}{link Leaflets:leaflets}{/capture}

                    <p>{_"$websiteType.city.shop.generatedText.1", [shop => $shop->getName(), brand => $shop->getName(), city => $city->getName()]|noescape}</p>
                    
                    <p n:if="$currentLeaflet">{_"$websiteType.city.shop.generatedText.2", [shop => $shop->getName(), brand => $shop->getName(), city => $city->getName(), leafletValidSince => $currentLeafletValidSince, leafletValidTill => $currentLeafletValidTill, actualLeafletUrl => $currentLeafletUrl]|noescape}</p>
                    
                    <p>{_"$websiteType.city.shop.generatedText.3", [city => $city->getName(), shop => $shop->getName(), brand => $shop->getName()] |noescape}</p>

                    <p>
                        {_"$websiteType.city.shop.generatedText.4", [shop => $shop->getName(), brand => $shop->getName(), cities => trim($citiesInText)]|noescape}
                        {_"$websiteType.city.shop.generatedText.5", [stores => trim($storesInText),  leafletsUrl => $leafletsUrl]|noescape}
                    </p>

                    {if $pageExtension && $pageExtension->getLongDescription()}
                        {$pageExtension->getLongDescription()|content|noescape}
                    {/if}
                </div>
            </div>
        </div>

    </div>

    <div class="float-wrapper__stop"></div>
</div>

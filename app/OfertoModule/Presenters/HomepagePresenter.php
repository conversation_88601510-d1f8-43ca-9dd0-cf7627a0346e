<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class HomepagePresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var GeoFacade @inject */
	public $geoFacade;

	public function renderDefault(): void
	{

		$leaflets = $this->leafletFacade->findTopLeaflets($this->localization, true, 10, $this->website->getModule());

		if (count($leaflets) < 10) {
			$leaflets = array_merge($leaflets, $this->leafletFacade->findLeaflets($this->localization, true, 10, Website::MODULE_OFERTO, false, $leaflets));
		}

		$this->template->leaflets = array_slice($leaflets, 0, 10);
		$this->template->shops = $this->shopFacade->findTopLeafletShops($this->localization, true, 12, $this->website->getModule());

		$this->template->articles = $this->articleFacade->findArticles($this->website->getParentWebsite(), 8);

		$this->template->cities = $this->geoFacade->findCities($this->localization, 36, Website::MODULE_OFERTO);
	}

	public function actionOther($other): void
	{
		$this->redirectPermanent('Homepage:default', ['region' => 'cz']);
	}
}

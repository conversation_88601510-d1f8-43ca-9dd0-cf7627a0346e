<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ContentBlockFacade;
use <PERSON><PERSON><PERSON>\Model\Seo\Entities\PageExtension;
use Ka<PERSON>ino\Model\Shops\Entities\ContentBlock;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Websites\Entities\Website;

final class ShopPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var ContentBlockFacade @inject */
	public $contentBlockFacade;

	/** @var GeoFacade @inject */
	public $geoFacade;

	public function actionShop(Shop $shop): void
	{
		if ($shop->isActiveOferto() === false && !$this->getUser()->isLoggedIn()) {
			$this->redirectPermanent(':Oferto:Homepage:default');
		}

		$this->responseCacheTags[] = 'shop/' . $shop->getId();

		if ($shop->isEshop()) {
			$this->setView('shopEshop');
		}

		$this->template->shop = $shop;
		$this->template->leaflets = $this->leafletFacade->findLeafletsByShop($shop, 40, true, true, null, false);

		$this->template->expiredLeaflets = $this->leafletFacade->findExpiredLeafletsByShop($shop);

		if ($shop->getTag()) {
			$this->template->similarShops = $this->shopFacade->findLeafletShopsByTag($shop->getTag(), true, null, $this->website->getModule());
		} else {
			$this->template->similarShops = $this->shopFacade->findTopLeafletShops($shop->getLocalization(), true, 10, $this->website->getModule());
		}

		$this->template->getHeadingFromPageExtension = function (PageExtension $pageExtension) use ($shop) {
			return $this->seoGenerator->generateShopHeadingFromPageExtension($pageExtension, $shop, $this->website);
		};

		$this->template->heading1 = $this->seoGenerator->generateShopHeading1($shop, $this->website);
		$this->template->metaTitle = $this->seoGenerator->generateShopMetaTitle($shop, $this->website);
		$this->template->metaDescription = $this->seoGenerator->generateShopMetaDescription($shop, $this->website);
		$this->template->seoGenerator = $this->seoGenerator;
		$this->template->faqContentBlocks = $this->contentBlockFacade->findFaqContentBlocks($shop, Website::MODULE_OFERTO);
		$this->template->cities = $shop->hasActiveCities() ? $this->geoFacade->findCitiesByShop($shop, 48, Website::MODULE_OFERTO) : [];

		$this->template->articles = $this->articleFacade->findArticles($this->website, 4);

		$contentBlocks = $this->contentBlockFacade->findContentBlocksByShop($shop, Website::MODULE_OFERTO);
		$this->template->contentBlocks = $contentBlocks;

		$length = 0;
		/** @var ContentBlock $contentBlock */
		foreach ($contentBlocks as $contentBlock) {
			if ($contentBlock->getType() === 'legacy') {
				continue;
			}

			if ($contentBlock->getContent() === null) {
				continue;
			}

			$length += strlen($contentBlock->getContent());

			if ($length >= 50) {
				break;
			}
		}

		$this->template->contentBlocksAllowed = $length > 50;
	}

	public function renderOffers(Shop $shop)
	{
		$offers = $this->offerFacade->findOffersByShop($shop, 30);

		$this->template->shop = $shop;
		$this->template->offers = $offers;
	}

	public function renderStores(Shop $shop)
	{
		$this->template->shop = $shop;
		$this->template->stores = $this->geoFacade->findStoresByShop($shop, 100, Website::MODULE_OFERTO);
	}
}

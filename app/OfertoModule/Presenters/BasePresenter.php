<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Forms\CityPickerControl\CityPickerControl;
use <PERSON><PERSON><PERSON>\Forms\CityPickerControl\CityPickerControlFactory;
use <PERSON><PERSON><PERSON>\Model\Articles\ArticleFacade;
use <PERSON><PERSON><PERSON>\Model\Conditions\DocumentFacade;
use Ka<PERSON>ino\Model\Content\ContentGenerator;
use Ka<PERSON>ino\Model\EntityManager;
use Ka<PERSON><PERSON>\Model\Geo\Entities\City;
use Ka<PERSON><PERSON>\Model\GoogleOptimize\GoogleOptimize;
use <PERSON><PERSON><PERSON>\Model\GoogleOptimize\GoogleOptimizeExperiment;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use Ka<PERSON><PERSON>\Model\Seo\SeoFacade;
use <PERSON><PERSON><PERSON>\Model\Seo\SeoGenerator;
use Ka<PERSON><PERSON>\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Websites\Entities\Website;
use <PERSON><PERSON><PERSON>\Model\Websites\WebsiteFacade;
use Nette\Localization\ITranslator;
use Nette\Utils\Strings;
use Tracy\Debugger;

abstract class BasePresenter extends \Kaufino\Presenters\BasePresenter
{
	/** @var string @persistent */
	public ?string $channel = null;

	/** @var EntityManager @inject */
	public $entityManager;

	/** @var DocumentFacade @inject */
	public $documentFacade;

	/** @var WebsiteFacade @inject */
	public $websiteFacade;

	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var SeoGenerator @inject */
	public $seoGenerator;

	/** @var ContentGenerator @inject */
	public $contentGenerator;

	/** @var ITranslator @inject */
	public $translator;

	/** @var Localization */
	public $localization;

	/** @var Website */
	public $website;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var ArticleFacade @inject */
	public $articleFacade;

	/** @var SeoFacade @inject */
	public $seoFacade;

	/** @var GoogleOptimize @inject */
	public $googleOptimize;

	/** @var GoogleOptimizeExperiment|null */
	protected $googleOptimizeExperiment;

	/** @var CityPickerControlFactory @inject */
	public $cityPickerControlFactory;

	protected function startup()
	{
		parent::startup();

		$this->website = $this->websiteFacade->resolveCurrentWebsite();
		$this->localization = $this->website->getLocalization();

		if (!$this->localization) {
			# Debugger::log('Region does not exist.');
			$this->redirect('this', ['region' => 'cz']);
		}

		if (!$this->website->isActive() && !$this->getUser()->isLoggedIn()) {
			# Debugger::log('Website is not active.');
			$this->redirect('this', ['region' => 'cz']);
		}

		if ($utmSource = $this->getParameter('utm_source')) {
			//Debugger::barDump($utmSource);
			if ($utmSource === 'google' || $utmSource === 'seznam') {
				$this->getHttpResponse()->setCookie('isP', 'yes', '1 day');
			}
		}

		if ($this->getParameter('cookie') && $this->getParameter('cookie') === 'wVPkTDuR8QSQXKsU') {
			$this->getHttpResponse()->setCookie('d2s0KZA1rp9pwsRI9n0l', 'Rj1Z53FM17fL6nskc5NG', new \DateTime('+ 1 month'));

			$this->redirect('this');
		}

		if ($this->getUser()->isLoggedIn()) {
			$this->template->userLoggedIn = true; // only for starting session purpose
		}

		$this->template->channel = $this->channel;

		$pageExtensionSlug = Strings::substring($this->getHttpRequest()->getUrl()->getPath(), 1);
		$this->template->pageExtension = $this->seoFacade->findPageExtensionBySlug($this->website, $pageExtensionSlug);
		$this->template->pageExtensionSlug = $pageExtensionSlug;

		$this->template->isTrafficPaid = $utmSource === 'google' || $utmSource === 'seznam' || $this->getHttpRequest()->getCookie('isP') === 'yes';
		$this->template->allPageCacheAllowed = false;
		$this->template->localization = $this->localization;
		$this->template->canonicalUrl = $this->seoGenerator->generateCanonicalUrl(
			$this->link('//this', ['channel' => null])
		);
		$this->template->translator = $this->translator;
		$this->template->headerShops = function () {
			return $this->shopFacade->findTopLeafletShops($this->localization, true, 10, $this->website->getModule());
		};
		$this->template->footerShops = function () {
			return $this->shopFacade->findTopLeafletShops($this->localization, true, 10, $this->website->getModule());
		};
		$this->template->footerArticles = function () {
			return $this->articleFacade->findArticles($this->website->getParentWebsite(), 10);
		};
		$this->template->footerWebsites = function () {
			$footerWebsites = $this->websiteFacade->findActiveWebsites($this->website->getModule());

			if ($ofertoComWebsite = $this->websiteFacade->findActiveWebsiteByLocalization($this->localization, Website::MODULE_OFERTO_COM)) {
				$footerWebsites[] = $ofertoComWebsite;
			}

			return $footerWebsites;
		};

		if ($this->localization->isCzech()) {
			$this->initGoogleOptimize();
		}

		$this->template->websiteType = $this->website->getModule();

		$this->template->conditions = $this->documentFacade->findByLocalization($this->localization);
	}

	protected function initGoogleOptimize()
	{
		$this->googleOptimizeExperiment = new GoogleOptimizeExperiment('-mFWxwvNQbWAJEbf5SaBxg', 3);

		$group = $this->googleOptimize->getVariant('group', 3);
		$this->googleOptimizeExperiment->setVariant($group % $this->googleOptimizeExperiment->getCountOfVariants());

		$this->template->googleOptimizeExperiment = $this->googleOptimizeExperiment;

		Debugger::barDump('experimentId: ' . $this->googleOptimizeExperiment->getExperimentId() . ';group: ' . $group . ';variant: ' . $this->googleOptimizeExperiment->getVariant());
	}

	protected function getGoogleOptimizeVariant()
	{
		if (!$this->googleOptimizeExperiment) {
			return null;
		}

		return $this->googleOptimizeExperiment->getVariant();
	}

	protected function createComponentCityPickerControl(): CityPickerControl
	{
		$control = $this->cityPickerControlFactory->create($this->localization, Website::MODULE_KAUFINO);

		$control->onSuccess[] = function (City $city) {
			$this->redirect('this');
		};

		return $control;
	}
}

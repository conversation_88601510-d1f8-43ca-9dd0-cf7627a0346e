{var $currentLeaflet = $shop->getCurrentLeaflet()}
{var $nextLeaflet = $shop->getNextLeaflet()}

{var $parameters = [
'currentLeafletFromDate' => $currentLeaflet ? $currentLeaflet->getValidSince() : null,
'currentLeafletToDate' => $currentLeaflet ? $currentLeaflet->getValidTill() : null,
'nextLeafletFromDate' => $nextLeaflet ? $nextLeaflet->getValidSince() : null,
'shopName' => $shop->getName(),
]}

{block head}
    {include parent}
    {dump $relatedLeaflets}
    {if $website->getId() === 100 && count($relatedLeaflets) >= 2}
        {var $relatedLeaflet1 = $relatedLeaflets[0] ?? null}
        {var $relatedLeaflet2 = $relatedLeaflets[1] ?? null}

        {*
            {link //Leaflet:leaflet $relatedLeaflet1->getShop(), $relatedLeaflet1}            
            {link //Leaflet:leaflet $relatedLeaflet2->getShop(), $relatedLeaflet2};

            {$leaflet->getShop()->getName()} }
            const targetTitle1 = {$relatedLeaflet1->getName()}; // Název pro URL 1            
            const targetTitle2 = {$relatedLeaflet2->getName()}; // Název pro URL 2
        *}

        <script>
            var relatedLeaflet1Url = {link //Leaflet:leaflet $relatedLeaflet1->getShop(), $relatedLeaflet1};
            var relatedLeaflet2Url = {link //Leaflet:leaflet $relatedLeaflet2->getShop(), $relatedLeaflet2};
        </script>

        <script n:syntax="off">                                    
            (function() {
            if (!history.pushState) return;

            const cfg = {
                debug: false,
                paramName: 'backButtonFunnelSource',
                sessionKey: 'backButtonFunnelDone',
                leaflets: [
                { id: 'letakB', title: 'Leták B', url: relatedLeaflet1Url },
                { id: 'letakC', title: 'Leták C', url: relatedLeaflet2Url }
                ]
            };
            if (cfg.debug) console.info('🎛️ funnel-builder DEBUG ON');

            document.addEventListener('DOMContentLoaded', () => {
                // v debug režimu ignorujeme filtr referrer a sessionStorage
                if (!cfg.debug) {
                if (sessionStorage.getItem(cfg.sessionKey)) {
                    console.log('⛔️ Funnel už injectnut, nekontinuuji.');
                    return;
                }
                const ref = document.referrer;
                if (!ref) {
                    console.log('⛔️ Žádný referrer, nelze injectovat funnel.');
                    return;
                }
                }

                // 1) Save original
                const origin = { url: location.href, title: document.title };
                history.replaceState({ type: 'orig', url: origin.url }, origin.title, origin.url);
                if (cfg.debug) console.debug('[builder] replaceState orig →', origin.url);

                // 2) Inject each leaflet as funnel-state
                cfg.leaflets.forEach((leaflet, i) => {
                const sep = leaflet.url.includes('?') ? '&' : '?';
                const u = leaflet.url + sep + cfg.paramName + '=' + encodeURIComponent(leaflet.id);
                history.pushState(
                    { type: 'funnel', url: u, leafletId: leaflet.id },
                    leaflet.title,
                    u
                );
                console.log(`[builder] pushState [${i+1}/${cfg.leaflets.length}] →`, u);
                });

                // 3) Nakonec znovu origin, aby adresa zůstala /k/flop
                history.pushState({ type: 'orig', url: origin.url }, origin.title, origin.url);
                if (cfg.debug) console.debug('[builder] pushState orig →', origin.url);

                if (!cfg.debug) {
                sessionStorage.setItem(cfg.sessionKey, '1');
                console.log('✅ Funnel injectnut a označen v sessionStorage.');
                } else {
                console.info('🎛️ Debug: sessionStorage neukládám, ref-filter ignoruji.');
                }
            });
            })();           
        </script>
    {/if}

    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Shop',
            'country' : {{$localization->getRegion()}}
        });
    </script>

    {if ($channel === 'l')}
        <script>
        // Definování Out-of-page slotů (sticky a interstitial)
        googletag.cmd.push(function() {
            googletag.defineOutOfPageSlot('/23037269705/kaufino-com_top/kaufino-com_popup_testgroup1B', 'sticky').addService(googletag.pubads());
            googletag.defineOutOfPageSlot('/23037269705/kaufino-com_top/kaufino-com_interstitial_testgroup1B', 'interstitial').addService(googletag.pubads());
            googletag.enableServices();
        });
        </script>
    {elseif ($channel === 'm')}
        <script async src="https://cdn.performax.cz/yi/adsbypx/px_autoads.js?aab=ulite"></script>
        <link rel="stylesheet" href="https://cdn.performax.cz/yi/adsbypx/px_autoads.css"/>
    {elseif ($channel === 'n')}
        <script async src="https://cdn.performax.cz/yi/adsbypx/px_autoads.js?aab=ulite"></script>
        <link rel="stylesheet" href="https://cdn.performax.cz/yi/adsbypx/px_autoads.css"/>
    {/if}    
{/block}

{block title}{if $pageExtension && $pageExtension->getTitle()}{$seoGenerator->renderInSandbox($pageExtension->getTitle(), $parameters)}{else}{$metaTitle}{/if}{/block}
{block description}{if $pageExtension && $pageExtension->getDescription()}{$seoGenerator->renderInSandbox($pageExtension->getDescription(), $parameters)}{else}{$metaDescription}{/if}{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">            
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a> |
            <a n:href="Tag:tag $shop->getTag()" class="link" n:if="$shop->getTag() !== null">{$shop->getTag()->getName()}</a>            
        </p>
    </div>
{/block}

{block scripts}
    {include parent}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": {_oferto.navbar.home},
                "item": {link //Homepage:default}
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": {_kaufino.navbar.leaflets},
                "item": {link //Leaflets:leaflets}
            },
            {
                "@type": "ListItem",
                "position": 3,
                "name": {$shop->getTag()->getName()},
                "item": {link //Tag:tag $shop->getTag()}
            },
            {
                "@type": "ListItem",
                "position": 4,
                "name": {$shop->getName()},
                "item": {link //this}
        }
  ]
}
    </script>

    <script n:if="$faqContentBlocks" type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
                {foreach $faqContentBlocks as $faq} {
                    "@type": "Question",
                    "name": {$faq->getHeading()},
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": {strip_tags($faq->getContent())}
                    }
                }{sep},{/sep}
        {/foreach}
            ]
        }
    </script>

    <script>
        const elementsWithBrochureId = document.querySelectorAll('[data-brochure-id]');
        console.log(elementsWithBrochureId.length);

        if (elementsWithBrochureId.length > 0) {
            if (checkCookie('userLocation') === false && navigator.geolocation) {
                getLocationFromBrowser();
            }
        }

        elementsWithBrochureId.forEach(element => {
            const brochureId = element.dataset.brochureId;

            const xhr = new XMLHttpRequest();
            xhr.open('POST', {link :Api:Offerista:brochureImpression, websiteId: $presenter->website->getId()}, true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.send(JSON.stringify({ brochureId: brochureId }));
        });
    </script>        
{/block}

{define cities}
    <div n:if="$shop->isStore() && count($cities) > 0">
        <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
            {_kaufino.shop.city, [brand => $shop->getName()]}
        </h2>

        <p class="k-tag mb-1">
            {foreach $cities as $city}
                <span class="k-tag__inner {$iterator->counter > 6 ? 'hidden' : ''}">
                    <a n:href="City:shop $city, $shop" class="k-tag__item">{$shop->getName()} {$city->getName()}</a>
                </span>
            {/foreach}
        </p>

        <p n:if="$shop->isStore() && count($cities) > 5" class="d-flex mt-3 mb-5">
            <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
            <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
        </p>
    </div>
{/define}

{block content}

{if ($channel === 'l')}
    <!-- Slot pro Sticky formáty -->
    <div id='sticky' style="height:0">
        <script>
            googletag.cmd.push(function() { googletag.display('sticky'); });
        </script>
    </div>

    <!-- Block pro formát Interstitial -->
    <div id='interstitial' style="height:0">
        <script>
            googletag.cmd.push(function() { googletag.display('interstitial'); });
        </script>
    </div>
{/if}

{include adUnit, 'shop_ad_1'}
{include adUnit, 'shop_ad_1_mob', 'mobile'}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100 ta-center sm-ta-left mt-3 mb-3">

                <div class="k-profile-header k-profile-header--sm-center">
                    {*}                    
                    <div class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">
                        <picture>
                            <source 
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80,'fit','webp'} 1x,
                                    {$shop->getLogoUrl() |image:160,160,'fit','webp'} 2x
                                " 
                                type="image/webp"
                            >                            
                            <img 
                                src="{$basePath}/images/placeholder-80x70.png" 
                                srcset="
                                    {$shop->getLogoUrl() |image:80,80} 1x,
                                    {$shop->getLogoUrl() |image:160,160} 2x
                                " 
                                width="80" 
                                height="80" 
                                alt="{$shop->getName()}" 
                                class="k-profile-header__logo"
                            >
                        </picture>							
                    </div>
                    *}
                    
                    <div class="w100">
                        <h1 class="k-profile-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {var $heading = $getHeadingFromPageExtension($pageExtension)}
                                {$heading}
                            {else}
                                {capture $shopBrandName}{$shop->getName()|upper}{/capture}
                                {_kaufino.shop.title, [brand => $shopBrandName]|noescape}
                            {/if}
                        </h1>                                            
                    </div>                                       
                </div>                      

                <div class="mb-3">

                    {if $channel === 'k'}
                        <div class="ads-container">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- Kaufino - Responsive - 1 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="2105301778"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>
                            
                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        </div>
                    {elseif ($channel === 'l' || $channel === 'm')}
                        {include adUnit, 'shop_ad_2'}
                    {elseif $channel === 'n'}
                        <div class="ads-container ads-container--desktop">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- letaky.kaufino.com / billboard1-direct -->
                            <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                            <div id="PX_35670_882042459640355"></div>
                            <script>
                                window.px2 = window.px2 || { conf: {},queue: [] };
                                px2.queue.push(function () {
                                    px2.render({
                                        slot: {
                                            id: 35670
                                        },
                                        elem: "PX_35670_882042459640355"
                                    })
                                });
                            </script>  
                        </div>                      
                    {else}
                        {if false && $localization->isSlovak()}
                            <div class="ads-container">
                                <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                <!-- /23037269705/kaufino-shopdetail1 -->
                                <div id='div-gpt-ad-1720005679399-0' style='min-width: 250px; min-height: 250px; max-height: 360px;'>
                                <script>
                                    googletag.cmd.push(function() { googletag.display('div-gpt-ad-1720005679399-0'); });
                                </script>
                                </div>
                            </div>                    
                        {elseif $localization->isHungarian()}
                        {else}
                            <div class="ads-container">
                                <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                                <!-- Kaufino - Responsive - 1 -->
                                <ins class="adsbygoogle"
                                    style="display:block"
                                    data-ad-client="ca-pub-4233432057183172"
                                    data-ad-slot="2105301778"
                                    data-ad-format="auto"
                                    data-full-width-responsive="true"></ins>
                                
                                <script>
                                    (adsbygoogle = window.adsbygoogle || []).push({});
                                </script>
                            </div>
                        {/if}
                    {/if}                    
                </div>   

                {* Letáky *}
                {if count($leafletsInTop) > 0}
                    <div class="k-leaflets__wrapper mt-5">                                                                 
                        {foreach $leafletsInTop as $leaflet}                                                                                        
                            {if $localization->isPolish() === false && $iterator->counter == 1}
                                <div class="k-leaflets__large-wrapper mb-3 mb-sm-0" n:attr="data-brochure-id: $leaflet->getOfferistaBrochureId()">
                                    <div class="k-leaflets__large">
                                        {if $user->isLoggedIn()}
                                            <div class="k-leaflets__bubble">
                                                {if $leaflet->hasOfferistaId()}<span class="k-leaflets__bubble-top">Offerista: {$leaflet->getOfferistaBrochureId()}</span>{/if}
                                                {if $leaflet->isTop()}<span class="k-leaflets__bubble-top">TOP</span>{/if}
                                                {if $leaflet->isPrimary()}<span class="k-leaflets__bubble-primary">Primary</span>{/if}
                                                <small>{$leaflet->getPriority()}</small>
                                            </div>
                                        {/if}
                                        <a n:href="Leaflet:leaflet $shop, $leaflet" class="k-leaflets__large-thumbnail">
                                            <div class="img">
                                                <picture>
                                                    <source
                                                        srcset="
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:242,336,'exactTop','webp'} 1x,
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:484,672,'exactTop','webp'} 2x
                                                        "
                                                        type="image/webp"
                                                        importance="high"
                                                    >
                                                    <img
                                                        src="{$leaflet->getFirstPage()->getImageUrl() |image:242,336,'exactTop'}"
                                                        srcset="
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:242,336,'exactTop'} 1x,
                                                            {$leaflet->getFirstPage()->getImageUrl() |image:484,672,'exactTop'} 2x
                                                        "
                                                        width="242"
                                                        height="336"
                                                        alt="{if strtolower($leaflet->getName()) === strtolower($shop->getName())}{_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('N') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}{else}{$leaflet->getName()}{/if}"
                                                        importance="high"
                                                    >
                                                </picture>

                                            </div>
                                            <div class="img" n:if="$secondPage = $leaflet->getPageByNumber(2)">
                                                <picture>
                                                    <source
                                                        srcset="
                                                            {$secondPage->getImageUrl() |image:242,336,'exactTop','webp'} 1x,
                                                            {$secondPage->getImageUrl() |image:484,672,'exactTop','webp'} 2x
                                                        "
                                                        type="image/webp"
                                                        importance="high"
                                                    >
                                                    <img
                                                        src="{$secondPage->getImageUrl() |image:242,336,'exactTop'}"
                                                        srcset="
                                                            {$secondPage->getImageUrl() |image:242,336,'exactTop'} 1x,
                                                            {$secondPage->getImageUrl() |image:484,672,'exactTop'} 2x
                                                        "
                                                        width="242"
                                                        height="336"
                                                        alt="{if strtolower($leaflet->getName()) === strtolower($shop->getName())}{_'kaufino.leaflet.brandLeafletFrom', [brand => $shop->getName()]} {_'app.day.' . $leaflet->getValidSince()->format('N') . '.genitive'} {$leaflet->getValidSince()|localDate:'short'}{else}{$leaflet->getName()}{/if}"
                                                        importance="high"
                                                    >
                                                </picture>
                                            </div>
                                            <img class="chevron-right" src="{$basePath}/images/icons/chevron_right.svg" alt="">
                                        </a>
                                        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" n:if="$leaflet->isValid()" class="k-leaflets__corner"><span>{_'kaufino.leaflet.valid'}</span></a>
                                        <div class="k-leaflets__large-detail">
                                            <div class="logo">
                                                <img src="{$shop->getLogoUrl() |image:80,80}" alt="logo" loading="lazy">
                                                <div class="k-leaflets__large-detail-title">
                                                    <a n:href="Leaflet:leaflet $shop, $leaflet">
                                                        {if $leaflet->isChecked() === false}
                                                            {_kaufino.leaflet.titleUnChecked, [brand => $leaflet->getName()]|noescape}
                                                        {else}
                                                            {$leaflet->getName()}
                                                        {/if}
                                                    </a>
                                                    <span class="note">
                                                        <p class="k-leaflets__date mt-0 mb-0" n:if="$leaflet->isChecked()">{if $localization->isHungarian()}{$leaflet->getValidSince()|localDate:'long'} – {$leaflet->getValidTill()|localDate}{else}{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate:'long'}{/if}</p>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="mt-3 mt-md-0 ml-md-auto">
                                                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__button ">{_kaufino.shop.showLeaflet}</a>
                                            </div>

                                        </div>
                                    </div>

                                    {if ($channel === 'l' || $channel === 'm')}                        
                                        {include adUnit, 'shop_ad_2_mob', 'mobile'}
                                    {elseif $channel === 'n'}
                                        <div class="ads-container ads-container--mobile">
                                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>                                  
                                            <!-- letaky.kaufino.com / mobile-rectangle1-direct -->
                                            <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                                            <div id="PX_35685_778363952239437"></div>
                                            <script>
                                                window.px2 = window.px2 || { conf: {},queue: [] };
                                                px2.queue.push(function () {
                                                    px2.render({
                                                        slot: {
                                                            id: 35685
                                                        },
                                                        elem: "PX_35685_778363952239437"
                                                    })
                                                });
                                            </script>  
                                        </div>                                      
                                    {/if}
                                </div>                                

                                {continueIf $iterator->counter == 1}
                            {/if}

                            {include '../components/leaflet.latte', leaflet => $leaflet, validBadgeShow => true, buttonShow => true, offeristaId: $leaflet->getOfferistaBrochureId()}
                        {/foreach}                        
                    </div>
                {elseif (!$shop->isEshop())}
                    <div class="alert alert-info mx-3">{_kaufino.shop.noLeaflets}</div>
                {/if}                

                {* Kupony *}
                {if $shop->isEshop() && !$shop->isActive()}                    
                    {foreach $coupons as $coupon}                        
                        <div class="k-coupon">
                            <div class="k-coupon__box {if false}k-coupon__box--sale{/if}">
                                <strong class="k-coupon__box-value">{$coupon->getDiscountAmount()}{if $coupon->getDiscountType() == relative}%{/if}</strong>
                                <small class="k-coupon__box-type">{_kaufino.coupon.type.sale}</small>
                            </div>                    

                            <div class="k-coupon__content">
                                <small class="d-block mb-2">{_kaufino.coupon.valid}: {$coupon->getValidTill()|localDate:'long'}</small>
                                <h3 class="mt-0 mb-2"><a href="{$presenter->link('Shop:shop', ['shop' => $coupon->getShop(), 'oid' => $coupon->getId()])}" target="_blank" data-popup-link="{$presenter->link('Exit:offer', $coupon)}" class="color-black td-hover-underline">{$coupon->getShop()->getName()}: {$coupon->getName()}</a></h3>
                                {$coupon->getDescription()|noescape}                                
                            </div>
                            
                            <a href="{$presenter->link('Shop:shop', ['shop' => $coupon->getShop(), 'oid' => $coupon->getId()])}" target="_blank" data-popup-link="{$presenter->link('Exit:offer', $coupon)}" class="k-coupon__button k-coupon__button--code js-click-link">
                                <span class="k-coupon__button-label">{_kaufino.coupon.showCode}</span>
                                <span class="k-coupon__button-code">{$coupon->getCode()}</span>
                            </a>                    
                        </div>                                                                    
                    {/foreach}
                {/if}

                {if $channel === 'k'}
                    <div class="ads-container">
                        <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                        <!-- Kaufino - Responsive - 4 -->
                        <ins class="adsbygoogle"
                            style="display:block"
                            data-ad-client="ca-pub-4233432057183172"
                            data-ad-slot="1100074380"
                            data-ad-format="auto"
                            data-full-width-responsive="true"></ins>

                        <script>
                            (adsbygoogle = window.adsbygoogle || []).push({});
                        </script>
                    </div>    
                {elseif ($channel === 'l' || $channel === 'm')}
                    {include adUnit, 'shop_ad_3'}
                    {include adUnit, 'shop_ad_3_mob', 'mobile'}
                {elseif $channel === 'n'}
                    <div class="ads-container">
                        <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                        <!-- letaky.kaufino.com / billboard2-direct -->
                        <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                        <div id="PX_35664_194003039996025"></div>
                        <script>
                            window.px2 = window.px2 || { conf: {},queue: [] };
                            px2.queue.push(function () {
                                px2.render({
                                    slot: {
                                        id: 35664
                                    },
                                    elem: "PX_35664_194003039996025"
                                })
                            });
                        </script>             
                        
                        <!-- letaky.kaufino.com / mobile-rectangle2-direct -->
                        <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                        <div id="PX_35667_493690343150761"></div>
                        <script>
                            window.px2 = window.px2 || { conf: {},queue: [] };
                            px2.queue.push(function () {
                                px2.render({
                                    slot: {
                                        id: 35667
                                    },
                                    elem: "PX_35667_493690343150761"
                                })
                            });
                        </script>
                    </div>                            
                {else}
                    {if false && $localization->isSlovak()}
                        <div class="ads-container">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- /23037269705/kaufino/leafletdetail_2 -->
                            <div id='div-gpt-ad-1718185642329-0' style='min-width: 250px; min-height: 250px;'>
                            <script>
                                googletag.cmd.push(function() { googletag.display('div-gpt-ad-1718185642329-0'); });
                            </script>
                            </div>
                        </div>                
                    {else}
                        <div class="ads-container">
                            <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                            <!-- Kaufino - Responsive - 4 -->
                            <ins class="adsbygoogle"
                                style="display:block"
                                data-ad-client="ca-pub-4233432057183172"
                                data-ad-slot="1100074380"
                                data-ad-format="auto"
                                data-full-width-responsive="true"></ins>

                            <script>
                                (adsbygoogle = window.adsbygoogle || []).push({});
                            </script>
                        </div>
                    {/if}
                {/if}                

                {include cities}

                {include adUnit, 'shop_ad_4'}
                {include adUnit, 'shop_ad_4_mob', 'mobile'}

                {if $channel === 'n'}
                    <div class="ads-container">
                        <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                        <!-- letaky.kaufino.com / billboard3-direct -->
                        <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                        <div id="PX_35700_287765788498115"></div>
                        <script>
                            window.px2 = window.px2 || { conf: {},queue: [] };
                            px2.queue.push(function () {
                                px2.render({
                                    slot: {
                                        id: 35700
                                    },
                                    elem: "PX_35700_287765788498115"
                                })
                            });
                        </script>  
                                        
                        <!-- letaky.kaufino.com / mobile-rectangle3-direct -->
                        <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                        <div id="PX_35697_425758749948387"></div>
                        <script>
                            window.px2 = window.px2 || { conf: {},queue: [] };
                            px2.queue.push(function () {
                                px2.render({
                                    slot: {
                                        id: 35697
                                    },
                                    elem: "PX_35697_425758749948387"
                                })
                            });
                        </script>
                    </div>                                    
                {/if}

                <p class="k-profile-header__text mw-800 mt-5 ml-0">
                    {if $pageExtension && $pageExtension->getShortDescription()}
                        {$pageExtension->getShortDescription()}
                    {else}
                        {*_kaufino.shop.text, [brand => $shop->getName()]|noescape*}
                    {/if}
                </p>

                {if $contentBlocksAllowed}
                    <div class="k-tabs k-content">
                        <div class="k-tabs__buttons">
                            <div class="k-tabs__tabLink {first}active{/first}" data-tab="tab{$iterator->counter}" n:foreach="$contentBlocks as $type => $contentBlock">
                                {continueIf $contentBlock === null}

                                <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h280v-480H160v480Zm360 0h280v-480H520v480Zm-320-80h200v-80H200v80Zm0-120h200v-80H200v80Zm0-120h200v-80H200v80Zm360 240h200v-80H560v80Zm0-120h200v-80H560v80Zm0-120h200v-80H560v80ZM440-240v-480 480Z"/></svg>
                                {_'kaufino.tabs.' . $type}
                            </div>
                        </div>

                        <div id="tab{$iterator->counter}" class="k-tabs_tab {first}active{/first}" n:foreach="$contentBlocks as $contentBlock">
                            {continueIf $contentBlock === null}

                            <h2 n:if="$contentBlock->getHeading()">{$contentBlock->getHeading()}</h2>

                            {$contentBlock->getContent() |content|noescape}
                        </div>
                    </div>
                {else}
                    <div class="k-content">
                        {if $pageExtension && $pageExtension->getLongDescription()}
                            {$pageExtension->getLongDescription() |content|noescape}
                        {else}
                            {cache md5($shop->getDescription()), expire => '20 minutes'}
                                {$shop->getDescription()|content|noescape}
                            {/cache}
                        {/if}
                    </div>
                {/if}

                {if $faqContentBlocks}
                    <div class="faq-section mb-5">
                        {* <div class=faq-section-title>Frequetly asked questions about Checkers</div> *}

                        {foreach $faqContentBlocks as $contentBlock}
                            {continueIf !$contentBlock->getHeading() || !$contentBlock->getContent()}

                            <div class="faq-question">
                                <h2 class="faq-title">
                                    <div>{$contentBlock->getHeading()}</div>
                                    <svg class="arrow" width="22" height="12" viewBox="0 0 22 12" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.9823 11.5239C10.8079 11.5239 10.6374 11.4905 10.4708 11.4239C10.3041 11.3572 10.1502 11.2534 10.0092 11.1124L0.916901 2.02001C0.778434 1.88155 0.711135 1.6963 0.715001 1.46426C0.718835 1.2322 0.789985 1.04693 0.928452 0.908462C1.10795 0.728995 1.2932 0.645029 1.4842 0.656562C1.67523 0.668096 1.85665 0.755913 2.02845 0.920013L10.9823 9.87386L19.9362 0.920013C20.0746 0.781546 20.2535 0.702062 20.4727 0.681562C20.6919 0.661062 20.8836 0.740546 21.0477 0.920013C21.2272 1.08411 21.3047 1.26745 21.2804 1.47001C21.256 1.67258 21.1746 1.85976 21.0362 2.03156L11.9554 11.1124C11.8144 11.2534 11.6669 11.3572 11.5131 11.4239C11.3592 11.4905 11.1823 11.5239 10.9823 11.5239Z"/>
                                    </svg>
                                </h2>
                                <div class="faq-answer">
                                   {$contentBlock->getContent() |content|noescape}
                                </div>
                            </div>
                        {/foreach}
                    </div>
                {/if}

                <div n:if="count($similarShops)-1 > 0" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.otherShops}</h2>
                    <div class="k-shop">    
                        {foreach $similarShops as $similarShop}
                            {continueIf $similarShop->getId() == $shop->getId()}
                            {include '../components/shop-logo.latte', shop => $similarShop, cssClass => $iterator->counter > 13 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($similarShops) > 12" class="d-flex mt-3 mb-5">
                        <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
                    </p>
                </div>

                {include adUnit, 'shop_ad_5'}
                {include adUnit, 'shop_ad_5_mob', 'mobile'}

                {if $channel === 'n'}
                    <div class="ads-container ads-container--desktop">
                        <div class="ads-label">{_'kaufino.leaflet.ads'}</div>
                        <!-- letaky.kaufino.com / billboard4-direct -->
                        <script src="https://cdn.performax.cz/px2/flexo.js?1" async></script>
                        <div id="PX_35694_203222881439050"></div>
                        <script>
                            window.px2 = window.px2 || { conf: {},queue: [] };
                            px2.queue.push(function () {
                                px2.render({
                                    slot: {
                                        id: 35694
                                    },
                                    elem: "PX_35694_203222881439050"
                                })
                            });
                        </script>   
                    </div>                 
                {/if}

                {* Letáky *}
                {if count($topLeafletsBySimilarShops) > 0}
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.showMore.leaflets}</h2>
                    <div class="k-leaflets__wrapper mt-3 mb-5">                        
                        {foreach $topLeafletsBySimilarShops as $leaflet}                            
                            {include '../components/leaflet.latte', leaflet => $leaflet, offeristaId: $leaflet->getOfferistaBrochureId()}
                        {/foreach}                        
                    </div>                    
                {/if}

                {include adUnit, 'shop_ad_6'}
                {include adUnit, 'shop_ad_6_mob', 'mobile'}
                        

                {if $popupCoupon}
                    {include popup.latte, coupon => $popupCoupon}
                {/if}                 

                {* Pobočky *}
                {*
                <div class="row">
                    <div class="col-6 col-sm-3">
                        <h3>Prodejny Praha</h3>
                        <ul>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Praha, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Praha, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Praha, Na Poříčí 1068/23</a></li>                        
                        </ul>
                        <p><a href="" class="color-grey td-underline td-hover-none">Zobrazit další prodejny</a></p>
                    </div>

                    <div class="col-6 col-sm-3">
                        <h3>Prodejny Brno</h3>
                        <ul>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Brno, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Brno, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Brno, Na Poříčí 1068/23</a></li>                        
                        </ul>
                        <p><a href="" class="color-grey td-underline td-hover-none">Zobrazit další prodejny</a></p>
                    </div>

                    <div class="col-6 col-sm-3">
                        <h3>Prodejny Ostrava</h3>
                        <ul>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Ostrava, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Ostrava, Na Poříčí 1068/23</a></li>
                            <li><a href="" class="color-grey td-underline td-hover-none">Prodejna Ostrava, Na Poříčí 1068/23</a></li>                        
                        </ul>
                        <p><a href="" class="color-grey td-underline td-hover-none">Zobrazit další prodejny</a></p>
                    </div>
                </div>
                *}  
            </div>
        </div>                      
                
    </div>	    

	<div class="float-wrapper__stop"></div>	
</div>

<script type="application/ld+json" n:if="count($leafletsInTop)">
    {
        "@context": "http://schema.org",
        "itemListElement": [
            {foreach $leafletsInTop as $leaflet}
            {continueIf $leaflet->isChecked() === false}
                {
                    "endDate": {$leaflet->getValidTill()->format('Y-m-d')},
                    "startDate": {$leaflet->getValidSince()->format('Y-m-d')},
                    "location": {
                        "address": {
                            "name": {$leaflet->getName()},
                            "@type": "PostalAddress"
                        },
                        "url": {link //:KaufinoSubdomain:Shop:shop $leaflet->getShop()},
                        "image": {$shop->getLogoUrl() |image:160,140,'fit','webp'},
                        "name": {$leaflet->getShop()->getName()},
                        "@type": "Place"
                    },
                    "performer": {
                        "name": {$leaflet->getShop()->getName()},
                        "@type": "Organization"
                    },
                    "image": {$leaflet->getFirstPage() ? $leaflet->getFirstPage()->getImageUrl() |image:60,84,'exactTop','webp' : ""},
                    "name": {$leaflet->getName()},
                    "url": {link //:KaufinoSubdomain:Leaflet:leaflet, $leaflet->getShop(), $leaflet},
                    "description": "",
                    "eventAttendanceMode": ["https://schema.org/OfflineEventAttendanceMode"],
                    "eventStatus": "https://schema.org/EventScheduled",
                    "organizer": {
                        "@type": "Organization",
                        "name": {$leaflet->getShop()->getName()},
                        "url": {link //:KaufinoSubdomain:Shop:shop $leaflet->getShop()}
                    },
                    "@type": "SaleEvent"
                }{sep},{/sep}
    {/foreach}
        ],
        "@type": "OfferCatalog"
    }
</script>

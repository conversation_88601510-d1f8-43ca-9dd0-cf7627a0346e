{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'TagOffers',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {elseif $bestOffer}
        {_kaufino.tag.offers.titleWithBestOffer, [tag => $tag->getName(), 'price' => ($bestOffer->getCurrentPrice()|price:$bestOffer->getLocalization())]}
    {else}
        {_kaufino.tag.offers.title, [tag => $tag->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {elseif $bestOffer}
        {_kaufino.tag.offers.metaDescriptionWithBestOffer, [tag => $tag->getName()]|noescape}
    {else}
        {_kaufino.tag.offers.metaDescription, [tag => $tag->getName()]|noescape}
    {/if}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">            
            <a n:href="Offers:offers" class="link">{_kaufino.navbar.offers}</a> |
            {if $tag->getParentTag() && $tag->getParentTag()->isActive()}<a n:href="Tag:tag $tag->getParentTag()" class="link">{$tag->getParentTag()->getName()}</a> |{/if}
            <span class="color-grey">{$tag->getName()}</span>
        </p>
    </div>
{/block}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_kaufino.tag.offers.title, [tag => $tag->getName()]}
                            {/if}
                        </h1>
						<p class="page-header__text ml-0">
                            {if $pageExtension && $pageExtension->getDescription()}
                                {$pageExtension->getDescription()}
                            {elseif $localization->isCzech() && $bestOffer}                                
                                {capture $shopsInText}{var $items = ($frequentOfferShops|slice: 0, 3)}{foreach $items as $_shop}{last}{if count($items) > 1} {_kaufino.homepage.and} {/if}{/last}<a n:href="Shop:shop $_shop">{$_shop->getName()}</a>{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}
                                Prohlédněte si <a href="{link Offers:offers}">aktuální zboží v akci</a> z kategorie {$tag->getName()}. Najděte si nejlevnější {$tag->getName()} v akci z obchodů {$shopsInText}. Tento týden najdete {$tag->getName()} ve slevě od {$bestOffer->getCurrentPrice()|price:$bestOffer->getLocalization()}.
                            {else}
                                {_kaufino.tag.offers.text, [tag => $tag->getName(), leafletLink => $presenter->link('Leaflets:leaflets')]|noescape}
                            {/if}
                        </p>
					</div>					
				</div>

                <div class="k-tag k-tag--in-content k-tag--4 mb-5">
                    {foreach $childTags as $childTag}
                        <div class="k-tag__inner">
                            <a n:href="Tag:tag $childTag" class="k-tag__item">{$childTag->getName()}</a>
                        </div>
                    {/foreach}
                </div>

                <div n:if="count($offers) > 0">
                    <div class="k-offers">
                        {foreach $offers as $offer}
                            {continueIf !$offer->getLeafletPage()}
                            
                            {include '../components/offer-item.latte', offer => $offer, hideTags => true}
                        {/foreach}
                    </div>
                </div>                

                {if count($leafletPages) > 0}
                <h2 n:if="count($offers) > 0" class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offersLeaflets, [category => $tag->getName()]}</h2>
                    <div class="k-leaflets__wrapper">
                        {foreach $leafletPages as $leafletPage}
                            {include '../components/leafletPage.latte', leafletPage => $leafletPage}
                        {/foreach}
                    </div>
                {else}
                    <div class="alert alert-info mx-3">{_kaufino.tag.noLeaflets}</div>
                {/if}


                <div n:if="count($expiredOffers) > 0">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offersExpire, [category => $tag->getName()]}</h2>
                    <div class="k-offers">
                        {foreach $expiredOffers as $offer}
                            {continueIf !$offer->getLeafletPage()}
                            {include '../components/offer-item.latte', offer => $offer, hideTags => true}
                        {/foreach}
                    </div>
                </div>

                <div class="k-content">
                    {if $contentBlocks}
                        {foreach $contentBlocks as $contentBlock}
                            {continueIf $contentBlock->getType() === 'legacy_description'}
                            {continueIf $contentBlock->isArchived()}

                            <div class="k-content k__text mw-900 mb-5 ml-0" n:if="$contentBlock->getContent()">
                                <h2 n:if="$contentBlock->getheading()">{$contentBlock->getHeading()}</h2>

                                {$contentBlock->getContent() |content|noescape}
                            </div>
                        {/foreach}
                    {else}
                        {$tag->getDescription()|content|noescape}
                    {/if}
                </div>
            </div>           
        </div>        

    </div>    

	<div class="float-wrapper__stop"></div>	
</div>

<script type="application/ld+json" n:if="count($offers)">
    {
        "@context": "http://schema.org",
        "itemListElement": [
            {foreach $offers as $offer}
                {
                    "endDate": {$offer->getValidTill()->format('Y-m-d')},
                    "startDate": {$offer->getValidSince()->format('Y-m-d')},
                    "location": {
                        "address": {
                            "name": {$offer->getShop()->getName()},
                            "@type": "PostalAddress"
                        },
                        "url": {link //:KaufinoSubdomain:Shop:shop $offer->getShop()},
                        "image": {$offer->getShop()->getLogoUrl() |image:160,140,'fit','webp'},
                        "name": {$offer->getShop()->getName()},
                        "@type": "Place"
                    },
                    "performer": {
                        "name": {$offer->getShop()->getName()},
                        "@type": "Organization"
                    },
                    "image": {$offer->getImageUrl()|image:150,150,'fit','webp'},
                    "name": {$offer->getName()},
                    "url": {link //:KaufinoSubdomain:Tag:tag, $tag},
                    "description": "",
                    "eventAttendanceMode": ["https://schema.org/OfflineEventAttendanceMode"],
                    "eventStatus": "https://schema.org/EventScheduled",
                    "organizer": {
                        "@type": "Organization",
                        "name": {$offer->getShop()->getName()},
                        "url": {link //:KaufinoSubdomain:Shop:shop $offer->getShop()}
                    },
                    "@type": "SaleEvent"
                }{sep},{/sep}
    {/foreach}
        ],
        "@type": "OfferCatalog"
    }
</script>
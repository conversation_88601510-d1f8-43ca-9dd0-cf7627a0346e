# Apache configuration file (see httpd.apache.org/docs/current/mod/quickreference.html)

# disable directory listing
<IfModule mod_autoindex.c>
	Options -Indexes
</IfModule>

# enable cool URL
<IfModule mod_rewrite.c>
	RewriteEngine On
	# RewriteBase /

	#First rewrite any request to the wrong domain to use the correct one (here www.)
    #RewriteCond %{HTTP_HOST} !^www\.
    #RewriteRule ^(.*)$ https://www.%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    #Now, rewrite to HTTPS:
    #RewriteCond %{HTTPS} off
    #RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]


	# use HTTPS
	# RewriteCond %{HTTPS} !on
	# RewriteRule .? https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

	# prevents files starting with dot to be viewed by browser
	RewriteRule /\.|^\.(?!well-known/) - [F]

	# front controller
	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule !\.(pdf|js|ico|gif|jpg|png|css|rar|zip|tar\.gz|map)$ index.php [L]
</IfModule>

# enable gzip compression
<IfModule mod_deflate.c>
	<IfModule mod_filter.c>
		AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json application/xml image/svg+xml
	</IfModule>
</IfModule>

# Authorization header
SetEnvIf Authorization .+ HTTP_AUTHORIZATION=$0

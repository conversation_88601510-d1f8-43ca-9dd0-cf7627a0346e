<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewOfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class LeafletPresenter extends BasePresenter
{
	/** @persistent */
	public $detail;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	public function renderDefault(): void
	{
		$leaflets = $this->leafletFacade->findLeaflets($this->localization, true, null, $this->website->getModule());

		$this->template->leaflets = $leaflets;
	}

	public function actionLeaflet(Shop $shop, Leaflet $leaflet, $page = 1, $detail = false): void
	{
		if ($leaflet->getShop() != $shop) {
			$this->error('The leaflet is not owned by the shop.');
		}

		if ($leaflet->isDeleted() || $shop->isActiveOferto() === false) {
			$this->redirectPermanent("Shop:shop", ['shop' => $shop]);
		}

		if ($leaflet->isArchived()) {
			$this->redirectPermanent("Shop:shop", ['shop' => $shop]);
		}

		if (!$leaflet->hasPageByNumber($page)) {
			$this->redirectPermanent("Leaflet:leaflet", ['shop' => $shop, 'leaflet' => $leaflet]);
		}

		$this->responseCacheTags[] = 'shop/' . $shop->getId();

		$templateName = 'leaflet_noPaginator';

		if ($this->localization->isCzech()) {
			$templateName = 'leaflet_0';

			if ($this->channel === 'c5a') {
				$templateName = 'leaflet_0';
			} elseif ($this->channel === 'c5b') {
				$templateName = 'leaflet_0';
			}
		}

		if ($this->website->hasAdSense()) {
			$templateName = 'leaflet_0';
		}

		if ($templateName === 'leaflet_2') {
			$this->template->showPaginator = $detail;
		}

		$this->setView($templateName);

		$this->template->shop = $shop;
		$this->template->leaflet = $leaflet;
		$this->template->similarLeaflets = $this->leafletFacade->findLeafletsByShop($shop, 5, true, true, $leaflet);
		$this->template->recommendedLeaflets = $this->leafletFacade->findLeaflets($this->localization, true, 5, Website::MODULE_OFERTO);

		$this->template->similarShops = $this->shopFacade->findLeafletShops($this->localization, true, null, $this->website->getModule());
		$this->template->currentPage = $page;

		$this->template->leafletDescription = $this->contentGenerator->generateLeafletDescription($leaflet);

		$this->template->articles = $this->articleFacade->findArticles($this->website, 4);

		if ($leaflet->isExpired()) {
			$validLeaflet = $this->leafletFacade->findLeafletsByShop($shop, 1, true, true, null, false);
			$this->template->validLeaflet = $validLeaflet ? $validLeaflet[0] : null;
		}
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewOfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class OffersPresenter extends BasePresenter
{
	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	public function actionOffers(): void
	{
		if ($this->localization->isHungarian() === false && $this->localization->isCzech() === false) {
			$this->redirect("Homepage:default");
		}

		$offerTags = $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 100, websiteType: Website::MODULE_OFERTO);

		$this->template->offersTags = $offerTags;

		$this->template->offers = $this->offerFacade->findTopOffersByTags($this->localization, $offerTags);
	}
}

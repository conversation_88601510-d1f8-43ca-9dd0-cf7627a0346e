<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\NewOfertoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON><PERSON>ino\Model\Websites\Entities\Website;

final class HomepagePresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var GeoFacade @inject */
	public $geoFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	public function renderDefault(): void
	{

		$leaflets = $this->leafletFacade->findTopLeaflets($this->localization, true, 10, $this->website->getType());

		if (count($leaflets) < 10) {
			$leaflets = array_merge($leaflets, $this->leafletFacade->findLeaflets($this->localization, true, 10, Website::MODULE_OFERTO, false, $leaflets));
		}

		$this->template->leaflets = array_slice($leaflets, 0, 10);
		$this->template->shops = $this->shopFacade->findTopLeafletShops($this->localization, true, 12, $this->website->getType());

		$this->template->articles = $this->articleFacade->findArticles($this->website->getParentWebsite(), 8);

		$this->template->cities = $this->geoFacade->findCities($this->localization, 36, Website::MODULE_OFERTO);

		$offerTags = $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 36);

		$this->template->offersTags = $offerTags;
		$this->template->topOffers =  $this->offerFacade->findTopOffersByTags($this->localization, $offerTags, 3, 15);
	}

	public function actionOther($other): void
	{
		$this->redirectPermanent('Homepage:default', ['region' => 'cz']);
	}
}

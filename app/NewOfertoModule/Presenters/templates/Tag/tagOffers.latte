{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'TagOffers',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {elseif $bestOffer}
        {_oferto.tag.offers.titleWithBestOffer, [tag => $tag->getName(), 'price' => ($bestOffer->getCurrentPrice()|price:$bestOffer->getLocalization())]}
    {else}
        {_oferto.tag.offers.title, [tag => $tag->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {elseif $bestOffer}
        {_oferto.tag.offers.metaDescriptionWithBestOffer, [tag => $tag->getName()]|noescape}
    {else}
        {_oferto.tag.offers.metaDescription, [tag => $tag->getName()]|noescape}
    {/if}
{/block}

{block content}

<div class="container">
    <div class="mt-6 mb-6">
        <h1 class="text-[24px] leading-[34px] md:text-[33px] font-medium">
            {if $pageExtension && $pageExtension->getHeading()}
                {$pageExtension->getHeading()}
            {else}
                {_oferto.tag.offers.title, [tag => $tag->getName()]}
            {/if}
        </h1>
        <div class="flex gap-[9px] items-center font-light mt-1.5 mb-[18px]">
            <a n:href="Homepage:default">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
                    <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>
            
            <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"/>
            </svg>

            <a n:href="Offers:offers">{_"$websiteType.navbar.offers"}</a>

            <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
                <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"/>
            </svg>

            <span>{$tag->getName()}</span>
        </div>
        <p class="text-sm font-light leading-[22px] text-[#646C7C]">
            {if $pageExtension && $pageExtension->getDescription()}
                {$pageExtension->getDescription()}
            {elseif $localization->isCzech() && $bestOffer}                                
                {capture $shopsInText}{var $items = ($frequentOfferShops|slice: 0, 3)}{foreach $items as $_shop}{last}{if count($items) > 1} {_oferto.homepage.and} {/if}{/last}<a n:href="Shop:shop $_shop">{$_shop->getName()}</a>{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}
                Prohlédněte si <a href="{link Offers:offers}">aktuální zboží v akci</a> z kategorie {$tag->getName()}. Najděte si nejlevnější {$tag->getName()} v akci z obchodů {$shopsInText}. Tento týden najdete {$tag->getName()} ve slevě od {$bestOffer->getCurrentPrice()|price:$bestOffer->getLocalization()}.
            {else}
                {_oferto.tag.offers.text, [tag => $tag->getName(), leafletLink => $presenter->link('Leaflets:leaflets')]|noescape}
            {/if}
        </p>
    </div>

    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
        {foreach $childTags as $childTag}            
            <a n:href="Tag:tag $childTag" class="flex items-center justify-between font-light leading-7 pt-[14px] pb-[15px] px-4 bg-light-6 rounded-xl transition-transform duration-200 transform hover:scale-[103%] cursor-pointer">
                {$childTag->getName()}
                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                    <path d="M3.53538 11.3891L10.6064 4.31799" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M11.3139 10.6819L11.3078 3.60477L4.25505 3.59878" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>            
        {/foreach}
    </div>

    <div n:if="count($offers) > 0">
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {foreach $offers as $offer}
                {continueIf !$offer->getLeafletPage()}
                
                {include '../components/offer-item.latte', offer => $offer, hideTags => true}
            {/foreach}
        </div>
    </div>

    <div n:if="count($futureOffers) > 0">
        <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.offer.futureOffers, ['name' => $tag->getName()]}</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {foreach $futureOffers as $offer}
                {include '../components/offer-item.latte', offer => $offer, hideTags => true}
            {/foreach}
        </div>
    </div>

    {if count($leafletPages) > 0}
        <h2 n:if="count($offers) > 0" class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_oferto.shop.offersLeaflets, [category => $tag->getName()]}</h2>
        <div class="flex overflow-x-scroll">
            {foreach $leafletPages as $leafletPage}
                {var $leaflet = $leafletPage->getLeaflet()}
                <div class="k-leaflets__item mb-5">
                    <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet, $leafletPage->getPageNumber()" target="_blank" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                        <picture>
                            <source data-srcset="{$leafletPage->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                            <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                        </picture>
                    </a>
                    <div class="k-leaflets__title mt-0 mb-0">
                        <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet, $leafletPage->getPageNumber()" class="color-black">
                            {$leaflet->getName()}
                        </a>
                    </div>
                    <p class="k-leaflets__date mt-0 mb-0">{($leaflet->getValidSince())|localDate} - {($leaflet->getValidTill())|localDate:'long'}</p>
                </div>
            {/foreach}
        </div>    
    {/if}

    <div class="k-content" n:if="$contentBlocks">
        {if $contentBlocks}
            {foreach $contentBlocks as $contentBlock}
                {continueIf $contentBlock->getType() === 'legacy_description'}
                {continueIf $contentBlock->isArchived()}

                <div class="k-content k__text mw-900 mb-0 pb-0 ml-0" n:if="$contentBlock->getContent()">
                    <h2 n:if="$contentBlock->getheading()">{$contentBlock->getHeading()}</h2>

                    {$contentBlock->getContent() |content|noescape}
                </div>
            {/foreach}
        {/if}
    </div>          
</div>

<script type="application/ld+json" n:if="count($offers)">
    {
        "@context": "http://schema.org",
        "itemListElement": [
            {foreach $offers as $offer}
                {
                    "endDate": {$offer->getValidTill()->format('Y-m-d')},
                    "startDate": {$offer->getValidSince()->format('Y-m-d')},
                    "location": {
                        "address": {
                            "name": {$offer->getShop()->getName()},
                            "@type": "PostalAddress"
                        },
                        "url": {link //:Oferto:Shop:shop $offer->getShop()},
                        "image": {$offer->getShop()->getLogoUrl() |image:160,140,'fit','webp'},
                        "name": {$offer->getShop()->getName()},
                        "@type": "Place"
                    },
                    "performer": {
                        "name": {$offer->getShop()->getName()},
                        "@type": "Organization"
                    },
                    "image": {$offer->getImageUrl()|image:150,150,'fit','webp'},
                    "name": {$offer->getName()},
                    "url": {link //:Oferto:Tag:tag, $tag},
                    "description": "",
                    "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
                    "eventStatus": "https://schema.org/EventScheduled",
                    "organizer": {
                        "@type": "Organization",
                        "name": {$offer->getShop()->getName()},
                        "url": {link //:Oferto:Shop:shop $offer->getShop()}
                    },
                    "@type": "SaleEvent"
                }{sep},{/sep}
    {/foreach}
        ],
        "@type": "OfferCatalog"
    }
</script>
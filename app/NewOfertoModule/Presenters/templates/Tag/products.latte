
{block title}
    {$currentOffer->getName()} za {$currentOffer->getCurrentPrice()|price:$currentOffer->getLocalization()}
{/block}

{block description}
    {$currentOffer->getDescription()}
{/block}

{block robots}noindex,nofollow{/block}

{block head}

{/block}

{block content}

<div class="leaflet k-lf-layout o-optimize-0 oferto-ppc">
    <div class="container">
        <div class="leaflet__content">
            <div class="d-block overflow-hidden">
                <div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
                    <div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            tag.offers.title, ['tag' => $tag->getName()]}
                        </h1>

                        <p class="mw-600">
                            {_"$websiteType.tag.products.text", ['tag' => $tag->getName()]}
                        </p>
                    </div>
                </div>

                <div class=""></div>

                {include 'paginator.latte', currentPage => $currentPage, countOfPages => count($offers)}

                <div></div>

                <div class="px-3 mb-4">
                    <h2 class="page-header__title mb-3">
                        {$currentOffer->getName()} {_"$websiteType.tag.products.price", ['amount' => ($currentOffer->getCurrentPrice()|price:$currentOffer->getLocalization())]}
                    </h2>

                    <p class="mw-600">
                    {* odkaz na stranu letáku kde se offer nachází *}
                    {if !$currentOffer->isExpired()}
                        {_"$websiteType.tag.products.inLeaflet"} <a n:href="Shop:shop $currentOffer->getShop()">{$currentOffer->getShop()->getName()}</a>
                       {_"$websiteType.shop.link", [brand => $currentOffer->getShop()->getName(), page => $currentOffer->getLeafletPage()->getPageNumber()]|noescape}
                       <a href="{link Leaflet:leaflet shop => $currentOffer->getShop(), leaflet => $currentOffer->getLeafletPage()->getLeaflet()}#p-{$currentOffer->getLeafletPage()->getPageNumber()}" class="k-offers__link">{$currentOffer->getName()} {_"$websiteType.tag.products.priceFrom"}
                           {$currentOffer->getCurrentPrice()|price:$currentOffer->getLocalization()}</a>.
                    {/if}

                    {*<small n:if="$currentOffer->getCommonPrice() > 0">{$currentOffer->getCommonPrice()|price:$currentOffer->getLocalization()}</small>*}
                    </p>
                </div>

                <div class="leaflet-preview o-leaflet-preview mb-5">
                    {var $offerLeaflet = $currentOffer->getLeafletPage()->getLeaflet()}
                    <div class="k-offers__item {if $currentOffer->isExpired()}expired{/if}">
                        <div class="k-offers__inner" data-line="{_kaufino.shop.expired}">
                            {if !$currentOffer->isExpired()}
                                <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$currentOffer->getLeafletPage()->getPageNumber()}" class="k-offers__image-wrapper">
                            {else}
                                <span class="k-offers__image-wrapper">
                            {/if}

                            <picture>
                                <source data-srcset="{$currentOffer->getImageUrl()|image:300,300,'fit','webp'} 300w" type="image/webp">
                                <img
                                    src="{$basePath}/images/pixel.png"
                                    data-srcset="{$currentOffer->getImageUrl()|image:300,300,'fit'} 300w"
                                    data-sizes="auto"
                                    width="300"
                                    height="300"
                                    alt="{$currentOffer->getName()}"
                                    class="k-offers__image lazyload"
                                >
                            </picture>

                            {if !$currentOffer->isExpired()}
                                </a>
                            {else}
                                </span>
                            {/if}

                            <div class="k-offers__content">
                                <small class="k-offers__small">
                                    {if !isset($hideShop)}
                                        <a n:href="Shop:shop $offerLeaflet->getShop()">
                                            {$currentOffer->getShop()->getName()}
                                        </a>
                                    {/if}

                                    {if !isset($hideTags)}
                                        {foreach $currentOffer->getTags() as $tagItem}
                                            {if !isset($hideShop) && !isset($hideTags)}|{/if} <a n:href="Tag:products $tagItem">
                                            {$tagItem->getName()}
                                        </a>
                                            {breakIf $iterator->getCounter() > 1}
                                        {/foreach}
                                    {/if}
                                </small>
                                <p class="k-offers__title">
                                    {if !$currentOffer->isExpired()}
                                        <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$currentOffer->getLeafletPage()->getPageNumber()}">{$currentOffer->getName()}</a>
                                    {else}
                                        {$currentOffer->getName()}
                                    {/if}
                                </p>
                                <p class="k-offers__text">{$currentOffer->getDescription()}</p>
                                <div class="k-offers__price">
                                    {*
                                    {if !$currentOffer->isExpired()}
                                        <a href="{link Leaflet:leaflet shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet}#p-{$currentOffer->getLeafletPage()->getPageNumber()}" class="k-offers__link">{_kaufino.shop.link, [brand => $currentOffer->getShop()->getName(), page => $currentOffer->getLeafletPage()->getPageNumber()]|noescape}</a>
                                    {else}
                                        <span class="k-offers__link">{_kaufino.shop.link, [brand => $currentOffer->getShop()->getName(), page => $currentOffer->getLeafletPage()->getPageNumber()]|noescape}</span>
                                    {/if}
                                    *}

                                    <div class="k-offers__right">
                                        <small n:if="$currentOffer->getCommonPrice() > 0">{$currentOffer->getCommonPrice()|price:$currentOffer->getLocalization()}</small>
                                        <strong>{$currentOffer->getCurrentPrice()|price:$currentOffer->getLocalization()}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    
                </div>

                {include 'paginator.latte', currentPage => $currentPage, countOfPages => count($offers)}

                <div>
                    
                </div>
            </div>
        </div>

        <div class="leaflet__sidebar" style="height: auto !important;">
            <!-- oferto.com / halfpage1 -->
            {include "../components/halfpage1.latte"}

            <div class="lf__box" n:if="isset($similarLeaflets) && count($similarLeaflets) > 0">
                <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_"$websiteType.leaflet.similarLeaflets", [brand => $leaflet->getShop()->getName()]}</h3>
                <div class="lf__box-wrapper">
                    <div n:foreach="$similarLeaflets as $similarLeaflet" class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                        <a class="lf__box-image-wrapper {if $similarLeaflet->isExpired()}expired{/if} lf__box-image--medium mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">
                            <picture>
                                <source data-srcset="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$similarLeaflet->getName()}" class="img-responsive lazyload">
                            </picture>
                        </a>
                        <p class="fz-xxs fz-sm-xs mb-0">
                            <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">{$similarLeaflet->getName()}</a>
                            <small>{$similarLeaflet->getValidSince()|localDate} - {$similarLeaflet->getValidTill()|localDate:'long'}</small>
                        </p>
                    </div>
                </div>
            </div>

            <div class="lf__box lf__box-lg-border" n:if="isset($recommendedLeaflets)">
                <h3 class="lf__box-title px-3 px-lg-0">{_"$websiteType.leaflet.recommendedLeaflets"}</h3>
                <div class="lf__box-wrapper">
                    {foreach $recommendedLeaflets as $recommendedLeaflet}
                        <div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
                            <a class="lf__box-image-wrapper {if $recommendedLeaflet->isExpired()} expired{/if} mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">
                                <picture>
                                    <source data-srcset="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                    <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$recommendedLeaflet->getName()}" class="img-responsive lazyload">
                                </picture>
                            </a>
                            <p class="fz-xxs fz-sm-xs mb-0">
                                <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">{$recommendedLeaflet->getName()}</a>
                                <small>{$recommendedLeaflet->getValidSince()|localDate} - {$recommendedLeaflet->getValidTill()|localDate:'long'}</small>
                            </p>
                        </div>
                    {/foreach}
                </div>
            </div>

            <div class="float-wrapper">
                <!-- oferto.com / halfpage2 -->
                {include "../components/halfpage1.latte"}
            </div>
        </div>
    </div>

    <div class="float-wrapper__stop"></div>
</div>

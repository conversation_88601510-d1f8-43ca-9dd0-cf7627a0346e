{import 'components/form.latte'}

<!DOCTYPE html>
<html lang="{$localization->getLocale()}">
<head>
	<meta charset="utf-8">
	{capture $headTitle|spaceless}
		{ifset title}{include title|stripHtml}{/ifset}
	{/capture}

	<title>{if strlen($headTitle) > 55}{$headTitle}{else}{$headTitle} | MrOferto{/if}</title>
	<meta name="keywords" content="">
    <meta name="description" content="{ifset description}{include description|stripHtml|spaceless}{/ifset}">
	<meta name="author" content="MrOferto">
	{if $channel === null}
		<meta name="robots" content="{block #robots|stripHtml|trim}index,follow{/block}">
	{else}
		<meta name="robots" content="noindex,nofollow">
	{/if}
	<link rel="canonical" href="{$canonicalUrl}">

	<meta property="og:title" content="{ifset title}{include title|stripHtml|spaceless} | {/ifset}MrOferto" />
    <meta property="og:site_name" content="MrOferto"/>
    <meta property="og:url" content="{link //this}" />
    <meta property="og:description" content="{ifset description}{include description|stripHtml}{/ifset}" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="{ifset image}{include image |strip}{else}{$baseUrl}/images/1200x627_0G_oferto.jpg{/ifset}" />
    <meta property="fb:app_id" content="" />

	<meta name="twitter:card" content="summary" />

	<!-- Viewport for mobile devices -->    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=1">

	<link rel="apple-touch-icon" sizes="180x180" href="{$basePath}/images/favicon/oferto/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="{$basePath}/images/favicon/oferto/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="{$basePath}/images/favicon/oferto/favicon-16x16.png">
	<link rel="icon" type="image/png" sizes="192x192"  href="{$basePath}/images/favicon/oferto/android-chrome-192x192.png">
	<link rel="icon" type="image/png" sizes="512x512"  href="{$basePath}/images/favicon/oferto/android-chrome-512x512.png">
{*	<link rel="manifest" href="{$basePath}/images/favicon/oferto/site.webmanifest">*}
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#ffffff">

	<meta name="google-site-verification" content="7ypcHzpCB0upyEy8VKX_9akp-hXM0zMSokKA2MMVQtk" />
	<meta name="ahrefs-site-verification" content="5ec5be26cfe58d5e25899ff9921ea420afd8c7d87d68794943404438899a4f15">

	<link rel="stylesheet" href="/css/nice-select2.css">

	{ifset hreflang}{include hreflang}{/ifset}

	{if $localization->isCzech()}
		<meta name="facebook-domain-verification" content="i70fvp4k9svpfba39gf9a8lyrhrgm9" />		
		<meta name="seznam-wmt" content="77pAh8rrEpDTHwWEdBcK70rOPnbNTfip" />
	{/if}

	{if $localization->isSlovak()}
		<meta name="facebook-domain-verification" content="pxw1anm59hacu1p4c82sjv5ztoixfd" />
	{/if}

	{if $localization->isPolish()}
		<meta name="facebook-domain-verification" content="makvbqwyjp1r4iwoienhuuxcjuv5i1" />
	{/if}

	{if $localization->isRomanian()}
		<meta name="facebook-domain-verification" content="p7oq3v11nxw7xztntduhh0ezmgqvv7" />
	{/if}

	{if $localization->isHungarian()}
		<meta name="facebook-domain-verification" content="ji7c6hwij8i2ykatryxje9wbxnez2u" />
	{/if}

	{if $localization->isCroatian()}
		<meta name="facebook-domain-verification" content="jmyf4xp49c9h66py6rh5kglrjqq1xx" />
	{/if}

	{if $localization->isItaly()}
		<meta name="facebook-domain-verification" content="c4a20j56dyt4je8d3ktxc753au2ghz" />
	{/if}

	{if $localization->isCanadian()}
		<meta name="facebook-domain-verification" content="33vkclncg7zoiwxhwsq3zuopk2nt8y" />
	{/if}

	{if $localization->isNetherlandian()}
		<meta name="facebook-domain-verification" content="uzhd9ocu2cd6lxrco2dmcjn05hve54" />
	{/if}

	{if $localization->isDenmarkian()}
		<meta name="facebook-domain-verification" content="m32fiiw0rc6znj5uupj2ll082xl4yw" />
	{/if}

	{if $localization->isGermany()}
		<meta name="facebook-domain-verification" content="wtza7igp782w7k2mexj7k1icis45ek" />
	{/if}

	{if $localization->isBelgian()}
		<meta name="facebook-domain-verification" content="b4ojcsmuvak26533ltrtbbbozv3yyy" />
	{/if}

	{if $localization->isGreecian()}
		<meta name="facebook-domain-verification" content="mouit0enbes04ouumjp0leechm9chy" />
	{/if}

	{if $localization->isJar()}
		<meta name="facebook-domain-verification" content="mwqtufcs1t0ls6k4xogcetkuowv5rs" />
	{/if}

	{if $localization->isFrancian()}
		<meta name="facebook-domain-verification" content="xj9igj0w15rkrglz8hss9lcsahgqke" />
	{/if}

	{if $localization->isFinlandian()}
		<meta name="facebook-domain-verification" content="uh9gal4zufpeb75rjyn3msiabjq1x6" />
	{/if}

	{if $localization->isSwedian()}
		<meta name="facebook-domain-verification" content="zx9e96npc977iyjau2tlj9q8rifr02" />
	{/if}
	
	{if $localization->isNorwaian()	}
		<meta name="facebook-domain-verification" content="et6ubent4a3emk5jk1o8gxiot8pfzw" />
	{/if}

	{if $localization->isAustrian()}
		<meta name="facebook-domain-verification" content="dms0w979bks3mv2spfnz763mofro8t" />
	{/if}

	{if $localization->isSwitzerlandian()}
		<meta name="facebook-domain-verification" content="k6azdscy8trjrwhqac6g8zozn36mjd" />
	{/if}
	
	{if $localization->isPortuguesian()}
		<meta name="facebook-domain-verification" content="2bpqp094h5onmcug1fmh0qge4jlamu" />
	{/if}
	
	{if $localization->isSpaian()}
		<meta name="facebook-domain-verification" content="o72bpgkdnos8r4rqvg1ce3cehfm5ki" />
	{/if}

	{if $localization->isGreatbritian()}
		<meta name="facebook-domain-verification" content="kszxgvcing4ha4rjqsy9i85yrkg45b" />
	{/if}

	{if $localization->isSlovenian()}
		<meta name="facebook-domain-verification" content="0xl0eg45wyigm30w1qdyl9qh24jauc" />
	{/if}

	{if $localization->isLatvian()}
		<meta name="facebook-domain-verification" content="1pzfc3r0sgvil1kfkpqoxph96fdrcn" />
	{/if}

	{if $localization->isSerbian()}
		<meta name="facebook-domain-verification" content="tb9u6fzxfa4mtqfhnhv8avip6fbk3b" />
	{/if}

	{if $localization->isBulgarian()}
		<meta name="facebook-domain-verification" content="9izowtgq0izdb6mp0e18vna4g0ujtn" />
	{/if}

	{if $localization->isLithuanian()}
	{/if}

	{if $localization->isEstonian()}
		<meta name="facebook-domain-verification" content="e16cbvkwmasquqr6gn6qzroj3ufp8p" />
	{/if}

	{if $localization->isMoldavian()}
		<meta name="facebook-domain-verification" content="7b95isuugw0w49vn6ejdhu3mgkxibq" />
	{/if}	

	{var $version = 0.71}
	{var $newDesign = true}

	{if $newDesign}
		<link href="https://fonts.googleapis.com/css2?family=Readex+Pro:wght@160..700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
		<link rel="stylesheet" href="{$basePath}/js/swiper/swiper-bundle.min.css" />
    	<link rel="stylesheet" href="{$basePath}/css2/output.css">
	{else}
		<link rel="stylesheet" href="{$basePath}/css/main.oferto.css?v={$version}">
	{/if}

	{block head}{/block}

	<script>
		dataLayer = [{
		}];
	</script>
	<script n:if="isset($googleOptimizeExperiment)">
		dataLayer.push({
			'expId': {$googleOptimizeExperiment->getExperimentId()},
			'expVar': {$googleOptimizeExperiment->getVariant()}
		});
	</script>	

	{if $localization->isCzech()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WC5TC26');</script>
		<!-- End Google Tag Manager -->	
	{/if}

	{if $localization->isSlovak()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-KZH5WLL');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isPolish()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WGK8HS5');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isRomanian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-KH9SVTS');</script>
		<!-- End Google Tag Manager -->		
	{/if}

	{if $localization->isHungarian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-TMWCLVZ');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isCroatian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-M62XL7C');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isItaly()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-P34SLSN');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isCanadian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-T45TR7K');</script>
		<!-- End Google Tag Manager -->

	{/if}

	{if $localization->isNetherlandian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5D7XSD4');</script>
		<!-- End Google Tag Manager -->	
	{/if}	

	{if $localization->isDenmarkian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-TJHXR5R');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isGermany()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-NSPZVGJ');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isBelgian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-W28LQH9');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isGreecian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5F78CPS');</script>
		<!-- End Google Tag Manager -->
	{/if}	

	{if $localization->isJar()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-NB6GBBR');</script>
		<!-- End Google Tag Manager -->
	{/if}	

	{if $localization->isFrancian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-MN3J8GX');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isFinlandian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-N46B9J5');</script>
		<!-- End Google Tag Manager -->

	{/if}

	{if $localization->isSwedian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WVTSWR7');</script>
		<!-- End Google Tag Manager -->
	{/if}
	
	{if $localization->isNorwaian()	}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WX79TQ2');</script>
		<!-- End Google Tag Manager -->

	{/if}

	{if $localization->isAustrian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-MC85Q98');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isSwitzerlandian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-P8HLMSR');</script>
		<!-- End Google Tag Manager -->
	{/if}
	
	{if $localization->isPortuguesian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-T6P59VF');</script>
		<!-- End Google Tag Manager -->
	{/if}
	
	{if $localization->isSpaian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-KK47MKV');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isGreatbritian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WG6SPPQ');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isSlovenian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-WW5CSBX');</script>
		<!-- End Google Tag Manager -->

	{/if}	

	{if $localization->isLatvian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-M5KPSRQ');</script>
		<!-- End Google Tag Manager -->
	{/if}	

	{if $localization->isSerbian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-NNXHJJJ');</script>
		<!-- End Google Tag Manager -->
	{/if}	

	{if $localization->isBulgarian()}
		<!-- Google Tag Manager -->
		<script  n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-NTTLTCL');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isLithuanian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5KJMDBT');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isEstonian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5HT2ZGD');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isMoldavian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5H9LNN3S');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isTurkian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-W2Z54G36');</script>
		<!-- End Google Tag Manager -->

	{/if}
	
	{if $localization->isAustralian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-52GM7JFW');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isBrazilian()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-T8TZZTVV');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if $localization->isUnitedStatesAmerican()}
		<!-- Google Tag Manager -->
		<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-5SZRBDXJ');</script>
		<!-- End Google Tag Manager -->
	{/if}

	{if !$isTrafficPaid}
		{*<script type="text/javascript" charset="UTF-8" src="//cdn.cookie-script.com/s/e3a52935254eec71b8a7cc9aba4a3426.js"></script>*}
	{/if}

	{if $channel == 'c1' && $localization->isCzech()}
		<!--Google GPT/ADM code -->
		<script type="text/javascript" async="async" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
		<script type="text/javascript">
			window.googletag = window.googletag || { cmd: [] };
			window.googletag.cmd.push(function () {
				window.googletag.pubads().enableSingleRequest();
			window.googletag.pubads().disableInitialLoad();
			});
		</script>

		<!--Site config -->
		<script type="text/javascript" async="async" src="https://protagcdn.com/s/mroferto.cz/site.js"></script>
		<script type="text/javascript">
			window.protag = window.protag || { cmd: [] };
			window.protag.config = { s:'mroferto.cz', childADM: '***********', l: 'FbM3ys2m' };
			window.protag.cmd.push(function () {
				window.protag.pageInit();
			});
		</script>
	{elseif $channel == 'c1' && $localization->isPolish()}		
		<script type="text/javascript" async="async" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
		<script type="text/javascript">
			window.googletag = window.googletag || { cmd: [] };
			window.googletag.cmd.push(function () {
				window.googletag.pubads().enableSingleRequest();
			});
		</script>

		<!--Site config -->
		<script type="text/javascript" async="async" src="https://protagcdn.com/s/mroferto.pl/site.js"></script>
		<script type="text/javascript">
			window.protag = window.protag || { cmd: [] };
			window.protag.config = { s:'mroferto.pl', childADM: '***********', l: 'FbM3ys2m' };
			window.protag.cmd.push(function () {
				window.protag.pageInit();
			});
		</script>
	{elseif $channel == 'c2' && $localization->isPolish()}
		<div id="gm-sticky"></div>
		<script src="https://ads.richmedia.cz/js/gm-mroferto-pl.js"></script>
	{/if}
	
	{if $localization->isLatvian() || $localization->isSerbian() || $localization->isBulgarian() || $localization->isFrancian() || $localization->isSpaian() || $localization->isBrazilian() || $localization->isUnitedStatesAmerican()}
		{* Kaufino adsense *}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4233432057183172" crossorigin="anonymous"></script>
		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
	{elseif $localization->isLithuanian() || $localization->isEstonian() || $localization->isCyprian()}
		{* AdSalva adsense *}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6277661924921144" crossorigin="anonymous"></script>
		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
	{elseif $localization->isMoldavian() || $localization->isTurkian()}
		{* Stario adsense *}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4363508753950122" crossorigin="anonymous"></script>
		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
	{else}	
		{* Oferto adsense *}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3454721603118795" crossorigin="anonymous"></script>
		<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>															
	{/if}	

</head>

<body>	
	{if $localization->isCzech()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WC5TC26"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->	
	{/if}

	{if $localization->isSlovak()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KZH5WLL"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isPolish()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WGK8HS5"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isRomanian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KH9SVTS"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isHungarian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TMWCLVZ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isCroatian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-M62XL7C"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isItaly()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-P34SLSN"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isCanadian()}	
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T45TR7K"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->	
	{/if}

	{if $localization->isNetherlandian()}	
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5D7XSD4"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isDenmarkian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TJHXR5R"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isGermany()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NSPZVGJ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isBelgian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W28LQH9"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isGreecian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5F78CPS"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isJar()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NB6GBBR"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isFrancian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MN3J8GX"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isFinlandian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N46B9J5"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isSwedian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WVTSWR7"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}
	
	{if $localization->isNorwaian()	}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WX79TQ2"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isAustrian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MC85Q98"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isSwitzerlandian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-P8HLMSR"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}
	
	{if $localization->isPortuguesian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T6P59VF"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}
	
	{if $localization->isSpaian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KK47MKV"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isGreatbritian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WG6SPPQ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isSlovenian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WW5CSBX"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) --> 
	{/if}	

	{if $localization->isLatvian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-M5KPSRQ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isSerbian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NNXHJJJ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isBulgarian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NTTLTCL"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}	

	{if $localization->isLithuanian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5KJMDBT"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isEstonian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5HT2ZGD"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isMoldavian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5H9LNN3S"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isTurkian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W2Z54G36"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isAustralian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-52GM7JFW"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isBrazilian()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T8TZZTVV"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	{if $localization->isUnitedStatesAmerican()}
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5SZRBDXJ"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) -->
	{/if}

	<header class="flex items-center border border-b-light-4 h-[44px] md:h-[80px]">
		<div class="container px-[15px]">
			<div class="justify-between flex items-center">
				<div class="flex items-center gap-[56px]">
					<a class="flex-shrink-0" n:href="Homepage:default">
						{if $websiteType === 'oferto'}
							{if $localization->isItaly()}
								<svg class="w-[87px] h-[14px] md:w-full md:h-[40px]" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1603.8133 345.6336" n:syntax="off"><defs><style>.g{fill:url(#e);}.h{fill:#2bb673;}.i{opacity:.53;}</style><linearGradient id="e" x1="0" y1="172.8168" x2="298.1663" y2="172.8168" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><g id="b"><g><path d="M370.9398,286.2329h-19.1896V78.4332c0-10.4255,8.4516-18.8771,18.8771-18.8771h12.0123l79.8758,169.7342,79.8743-169.7342h12.0123c10.4255,0,18.8771,8.4516,18.8771,18.8771v207.7997h-19.1881c-10.4255,0-18.8771-8.4516-18.8771-18.8771v-111.6996l-60.8417,130.5767h-23.7135l-60.8417-130.5767v111.6996c0,10.4255-8.4516,18.8771-18.8771,18.8771Z"/><path d="M721.5371,256.4358c-21.7389-21.7359-32.6054-49.6101-32.6054-83.6196,0-34.1115,10.8665-61.9857,32.6054-83.6196,21.7359-21.7359,49.6087-32.6039,83.6182-32.6039s61.8822,10.868,83.6211,32.6039c21.7359,21.6339,32.6054,49.5081,32.6054,83.6196,0,34.0095-10.8694,61.8837-32.6054,83.6196-21.7389,21.7389-49.6116,32.6054-83.6211,32.6054s-61.8822-10.8665-83.6182-32.6054Zm137.2855-144.618c-14.5616-13.5181-32.4487-20.2801-53.6673-20.2801s-39.1058,6.762-53.6644,20.2801c-16.3293,15.0818-24.4939,35.4136-24.4939,60.9984s8.1646,45.8663,24.4939,60.8432c14.5586,13.626,32.4487,20.4367,53.6644,20.4367s39.1058-6.8108,53.6673-20.4367c16.3263-14.9769,24.4939-35.2584,24.4939-60.8432s-8.1676-45.9165-24.4939-60.9984Z"/><path d="M937.9559,154.564v-44.3055c0-9.7757,1.8209-18.4606,5.4628-26.0533,4.6794-9.5673,11.8006-16.6929,21.3723-21.3723,7.8011-3.9523,16.6929-5.9284,26.6755-5.9284,13.3112,0,26.6785,3.2251,40.0931,9.6722l-16.5362,34.1647c-8.008-3.8458-15.8593-5.7717-23.5569-5.7717-4.1592,0-7.4877,.6237-9.9826,1.8712-1.7707,.8336-3.0713,2.1343-3.902,3.9005-1.0405,2.2895-1.5578,5.4598-1.5578,9.517v10.9197h27.1429c10.4255,0,18.8771,8.4516,18.8771,18.8771v14.5087h-46.02v112.7919c0,10.4255-8.4516,18.8771-18.8771,18.8771h-19.1911V154.564Z"/><path d="M1072.1676,213.5345c1.2475,10.6078,5.1997,19.6563,11.8568,27.1441,6.1338,6.9704,13.7782,11.7548,22.9302,14.3532,9.152,2.6013,18.2005,2.5496,27.1455-.1567,9.5688-2.8068,17.6802-8.1632,24.3373-16.0677h44.9292c-11.44,19.2425-24.9078,32.7103-40.4035,40.4049-12.8973,6.5521-27.1455,9.8289-42.7476,9.8289-26.105,0-47.5305-8.4248-64.2737-25.2729-16.8496-16.7446-25.2714-38.2219-25.2714-64.4304,0-26.105,8.4218-47.5291,25.2714-64.2752,16.7431-16.8481,38.1687-25.2729,64.2737-25.2729,26.2114,0,47.6843,8.4248,64.4304,25.2729,16.8496,16.7461,25.2743,38.1702,25.2743,64.2752v14.1965h-137.7525Zm95.4749-29.3301c-2.1845-13.3112-8.2179-23.6589-18.097-31.0446-9.0485-6.6541-19.5011-9.8792-31.3579-9.6722-11.8568,.3119-22.0493,3.9005-30.5775,10.7645-9.2554,7.4877-14.4049,17.4718-15.4425,29.9523h95.4749Z"/><path d="M1326.0257,146.7629v-14.352c0-10.4255,8.4516-18.8771,18.8771-18.8771h9.3592v-32.4487l19.0883-.1009c10.4644-.0553,18.9769,8.4123,18.9769,18.8768v13.6728h26.1859v14.352c0,10.4255-8.4516,18.8771-18.8771,18.8771h-7.3088v120.5929c0,10.4255-8.4516,18.8771-18.8771,18.8771h-19.1881V146.7629h-28.2363Z"/><path d="M1226.7383,160.4223c0-17.1498-.0386-35.3848-.3322-49.8209h20.1877c10.2028-.0002,18.5644,8.1057,18.8693,18.3039,.1667,5.5755,.2714,11.1938,.2714,15.0003,7.6162-19.7256,14.275-34.1453,43.635-34.304v27.1254c-.0001,9.4839-6.8211,17.8192-16.2135,19.1335-21.3797,2.9917-27.4216,16.2288-27.4216,55.0946v56.3758c0,10.4255-8.4516,18.8771-18.8771,18.8771h-20.1191v-125.7859Z"/><path d="M589.2932,160.4223c0-17.1498-.0386-35.3848-.3322-49.8209h20.1876c10.2028-.0002,18.5644,8.1057,18.8693,18.3039,.1667,5.5755,.2714,11.1938,.2714,15.0004,7.6162-19.7256,14.275-34.1453,43.635-34.304l-.0002,27.1255c0,9.4839-6.821,17.8192-16.2134,19.1335-21.3796,2.9918-27.4215,16.2288-27.4215,55.0945v56.3758c0,10.4255-8.4516,18.8771-18.8771,18.8771h-20.1191v-125.7859Z"/><path d="M1566.8913,262.7671c-2.6614,3.3276-6.3205,6.3205-13.3072,11.3103-14.9677,10.311-28.9377,14.9677-44.903,14.9677-46.9015,0-81.4935-38.2511-81.4935-90.1424,0-50.2258,36.2558-88.4786,83.4888-88.4786,21.2881,0,43.9054,10.9772,56.2149,26.9425v-2.4111c0-10.4255,8.4516-18.8771,18.8771-18.8771h18.0449v148.4352c0,10.4255-8.4516,18.8771-18.8771,18.8771h-18.0449v-20.6236Zm-49.8944-10.311c27.9401,0,48.8968-22.6189,48.8968-52.5542,0-30.6032-20.6253-52.5558-48.8968-52.5558-27.9401,0-49.563,23.2834-49.563,53.2204,0,29.2708,21.9543,51.8897,49.563,51.8897Z"/></g></g><g id="d"><path class="g" d="M175.5053,285.625c69.6613-12.0216,122.6609-70.8175,122.6609-141.6843C298.1663,64.4449,231.4143,0,149.0831,0S0,64.4449,0,143.9407c0,99.7468,78.455,187.7106,204.0452,201.6929-11.7007-3.3416-25.467-10.4569-36.5573-21.1061-12.2584-11.771-7.2333-36.2707,8.0174-38.9025ZM79.2227,111.2952c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7252-14.4372,30.1058-30.9858,30.1058s-30.9873-13.3806-30.9873-30.1058Zm18.7642,97.8607l93.7362-123.2586,7.2909,5.5295-93.5406,123.1065-7.4866-5.3774Zm58.0151-19.8673c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7269-14.4372,30.1075-30.9858,30.1075s-30.9873-13.3806-30.9873-30.1075Zm-45.792-58.4521c-10.9162,0-20.4227-8.8017-20.4227-19.5412,0-10.9162,9.5065-19.7195,20.4227-19.7195s20.4227,8.8033,20.4227,19.7195c0,10.7396-9.5082,19.5412-20.4227,19.5412Zm76.7793,77.995c-10.9162,0-20.4244-8.8032-20.4244-19.5429,0-10.9145,9.5082-19.7179,20.4244-19.7179s20.4227,8.8033,20.4227,19.7179c0,10.7397-9.508,19.5429-20.4227,19.5429Z"/></g><g id="f" class="i"><path class="h" d="M175.5053,285.625c69.6613-12.0216,122.6609-70.8175,122.6609-141.6843C298.1663,64.4449,231.4143,0,149.0831,0S0,64.4449,0,143.9407c0,99.7468,78.455,187.7106,204.0452,201.6929-11.7007-3.3416-25.467-10.4569-36.5573-21.1061-12.2584-11.771-7.2333-36.2707,8.0174-38.9025ZM79.2227,111.2952c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7252-14.4372,30.1058-30.9858,30.1058s-30.9873-13.3806-30.9873-30.1058Zm18.7642,97.8607l93.7362-123.2586,7.2909,5.5295-93.5406,123.1065-7.4866-5.3774Zm58.0151-19.8673c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7269-14.4372,30.1075-30.9858,30.1075s-30.9873-13.3806-30.9873-30.1075Zm-45.792-58.4521c-10.9162,0-20.4227-8.8017-20.4227-19.5412,0-10.9162,9.5065-19.7195,20.4227-19.7195s20.4227,8.8033,20.4227,19.7195c0,10.7396-9.5082,19.5412-20.4227,19.5412Zm76.7793,77.995c-10.9162,0-20.4244-8.8032-20.4244-19.5429,0-10.9145,9.5082-19.7179,20.4244-19.7179s20.4227,8.8033,20.4227,19.7179c0,10.7397-9.508,19.5429-20.4227,19.5429Z"/></g></svg>
							{else}
								<svg class="w-[87px] h-[14px] md:w-full md:h-[40px]" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 530.3963 114.1832" n:syntax="off"><defs><style>.a{fill:url(#a);}.b{opacity:0.53;}.c{fill:#2bb673;}</style><linearGradient id="a" x1="13.0185" y1="67.1923" x2="111.5204" y2="67.1923" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><path d="M135.5618,104.66h-6.3394V36.0118a6.2362,6.2362,0,0,1,6.2362-6.2362h3.9683l26.3877,56.0733,26.3872-56.0733H196.17a6.2362,6.2362,0,0,1,6.2362,6.2362V104.66h-6.3389a6.2363,6.2363,0,0,1-6.2363-6.2362V61.5232l-20.1,43.1372h-7.834l-20.1-43.1372v36.901A6.2362,6.2362,0,0,1,135.5618,104.66Z" transform="translate(-13.0185 -10.1007)"/><path d="M251.3848,94.8166q-10.7724-10.7709-10.7714-27.6245,0-16.9035,10.7714-27.6245,10.771-10.7709,27.6241-10.771t27.625,10.771q10.7709,10.7205,10.7714,27.6245,0,16.8531-10.7714,27.6245t-27.625,10.7715Q262.1558,105.5881,251.3848,94.8166Zm45.3536-47.7758a26.8076,26.8076,0,0,0-35.4581,0q-8.0918,7.4736-8.0918,20.1513t8.0918,20.1a26.6535,26.6535,0,0,0,35.4581,0q8.09-7.4215,8.0918-20.1Q304.83,54.514,296.7384,47.0408Z" transform="translate(-13.0185 -10.1007)"/><path d="M322.8808,61.1623V46.5256a19.6663,19.6663,0,0,1,1.8047-8.6069,14.9751,14.9751,0,0,1,7.06-7.0606A19.2313,19.2313,0,0,1,340.5585,28.9a30.3644,30.3644,0,0,1,13.2451,3.1953l-5.4629,11.2866a17.8937,17.8937,0,0,0-7.7822-1.9068,7.4282,7.4282,0,0,0-3.2978.6182,2.6134,2.6134,0,0,0-1.2891,1.2886,7.7685,7.7685,0,0,0-.5147,3.144V50.133h8.9669a6.2363,6.2363,0,0,1,6.2363,6.2363v4.793H335.4569V98.4242a6.2362,6.2362,0,0,1-6.2362,6.2362h-6.34Z" transform="translate(-13.0185 -10.1007)"/><path d="M367.2188,80.6438a15.9479,15.9479,0,0,0,3.917,8.9673,16.0561,16.0561,0,0,0,16.543,4.69,16.3474,16.3474,0,0,0,8.04-5.3081h14.8428q-5.6689,9.5353-13.3477,13.3482a30.7246,30.7246,0,0,1-14.122,3.247q-12.936,0-21.2334-8.3491-8.35-8.2976-8.3487-21.2851,0-12.9361,8.3487-21.2339,8.2968-8.349,21.2334-8.3492,12.9888,0,21.2851,8.3492,8.35,8.2983,8.35,21.2339v4.69Zm31.541-9.6895a15.35,15.35,0,0,0-5.9785-10.2558,16.513,16.513,0,0,0-10.3593-3.1953A16.327,16.327,0,0,0,372.32,61.0593a13.6289,13.6289,0,0,0-5.1016,9.895Z" transform="translate(-13.0185 -10.1007)"/><path d="M451.0832,58.5852V53.8439a6.2361,6.2361,0,0,1,6.2362-6.2362h3.0919v-10.72l6.306-.0333a6.2362,6.2362,0,0,1,6.2692,6.2361v4.517h8.6507V52.349a6.2362,6.2362,0,0,1-6.2362,6.2362h-2.4145v39.839A6.2363,6.2363,0,0,1,466.75,104.66h-6.339V58.5852Z" transform="translate(-13.0185 -10.1007)"/><path d="M513.987,46.68q12.8832,0,21.1816,8.2979,8.2457,8.2462,8.2461,21.13,0,12.9369-8.2461,21.1822-8.2983,8.2983-21.1816,8.2978-12.8848,0-21.1826-8.2978-8.2456-8.2449-8.2461-21.1822,0-12.8832,8.2461-21.13Q501.1027,46.68,513.987,46.68ZM502.3913,89.56a16.3447,16.3447,0,0,0,11.5957,4.4321A16.0587,16.0587,0,0,0,525.5827,89.56q5.2573-4.9988,5.2568-13.4517,0-8.3994-5.2568-13.4a16.353,16.353,0,0,0-11.5957-4.4321,16.06,16.06,0,0,0-11.5957,4.4321q-5.2574,5-5.2578,13.4Q497.1335,84.5618,502.3913,89.56Z" transform="translate(-13.0185 -10.1007)"/><path d="M418.2827,63.0977c0-5.6656-.0128-11.69-.11-16.4588h6.6692a6.2353,6.2353,0,0,1,6.2337,6.0469c.055,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4153-11.3326l0,8.9611a6.3352,6.3352,0,0,1-5.3562,6.3209c-7.063.9884-9.059,5.3614-9.059,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6465Z" transform="translate(-13.0185 -10.1007)"/><path d="M207.6968,63.0977c0-5.6656-.0127-11.69-.11-16.4588h6.6692a6.2351,6.2351,0,0,1,6.2336,6.0469c.0551,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4152-11.3326V55.27a6.3353,6.3353,0,0,1-5.3563,6.3209c-7.0629.9884-9.0589,5.3614-9.0589,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6466Z" transform="translate(-13.0185 -10.1007)"/><path class="a" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/><g class="b"><path class="c" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/></g></svg>
							{/if}
						{else}
							{$website->getName()}
						{/if}
					</a>
					<div class="hidden lg:flex gap-[35px]">												
						<a href="{link Shops:shops}" class="hover:text-primary">{_"$websiteType.navbar.shops"}</a>
						<a href="{link Leaflets:leaflets}" class="hover:text-primary">{_"$websiteType.navbar.leaflets"}</a>

						{if ($localization->isCzech() || $localization->isHungarian()) && $websiteType === 'oferto'}
							<a href="{link Offers:offers}" class="hover:text-primary">{_"$websiteType.navbar.offers"}</a>
						{/if}						
					</div>
				</div>

				<div class="flex justify-end items-center gap-[14px] lg:gap-[28px]">
					<div class="w-[183px] lg:w-[380px] max-w-full relative">
						<input
							id="search-input-shops"
							required type="text"
							data-search-url="{link Ajax:search}"
							placeholder="{_kaufino.navbar.search.placeholder}"
							class="text-xs md:text-sm leading-[21px] md:leading-[24.5px] rounded-[20px] pl-[18px] pr-[50px] pt-[6px] md:pt-[7px] pb-[7px] md:pb-2 border w-full border-[#E5E5E5] focus:border-[#BC2026] focus:outline-none z-50 relative"
						>
						<div class="absolute right-0 top-0 bottom-0 flex items-center z-50 text-primary">
							<button
								type="submit"
								class="js-search-submit"
								data-search-url="{link Search:search, q => 'q'}"
							>
								<svg class="w-[34px] h-[34px] md:w-[40px] h-[40px]" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none" class="cursor-pointer">
									<rect width="40" height="40" rx="20" fill="currentColor"/>
									<path d="M14 19.25C14 20.6424 14.5531 21.9777 15.5377 22.9623C16.5223 23.9469 17.8576 24.5 19.25 24.5C20.6424 24.5 21.9777 23.9469 22.9623 22.9623C23.9469 21.9777 24.5 20.6424 24.5 19.25C24.5 17.8576 23.9469 16.5223 22.9623 15.5377C21.9777 14.5531 20.6424 14 19.25 14C17.8576 14 16.5223 14.5531 15.5377 15.5377C14.5531 16.5223 14 17.8576 14 19.25Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M26.7499 26.7499L22.9619 22.9619" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
							</button>
						</div>

						<div id="overlay-search-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-40"></div>
						<div id="search-modal" class="absolute top-full mt-1 hidden bg-white rounded-lg shadow-lg w-[280px] md:w-[380px] right-0 rounded-xl z-50">
							<div class="flex flex-col items-center justify-between p-1.5">
								<div class="flex items-center gap-2 p-1.5 md:p-2.5 hover:cursor-pointer hover:bg-light-6 w-full rounded-lg">
									<img class="w-[29px] h-[29px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">
									<div>Lidl</div>
								</div>

								<div class="flex items-center gap-2 p-1.5 md:p-2.5 hover:cursor-pointer hover:bg-light-6 w-full rounded-lg">
									<img class="w-[29px] h-[29px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">
									<div>Lidl</div>
								</div>
							</div>
						</div>
					</div>

					<div class="hidden lg:block relative flex-shrink-0">
						<div class="flex flex-shrink-0 items-center gap-2.5 cursor-pointer" onclick="toggleModal()">
							<svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
								<path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							Vyber město
							<svg xmlns="http://www.w3.org/2000/svg" width="12" height="7" viewBox="0 0 12 7" fill="none">
								<path d="M1 1.00008L6.00015 6L11 1" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</div>

						<div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" onclick="hideModal()"></div>

						<div id="select-city-modal" class="absolute top-full mt-6 hidden bg-white rounded-lg shadow-lg w-[309px] right-0 rounded-xl rounded-tr-[3px] z-[60]">
							<div class="flex items-center justify-between py-4 pl-5 pr-[23px]">
								<div class="flex items-center gap-[9px]">
									<svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
										<path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
										<path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
									</svg>
									<input id="select-city-input" type="text" placeholder="Vyber město" class="text-sm leading-[24.5px] outline-none">
								</div>
								<a class="text-primary text-sm leading-[24.5px] font-light underline" href=#">Potvrdit</a>
							</div>
							<div class="h-px bg-light-4 w-full"></div>

							<div class="flex items-center gap-2.5 text-sm leading-[24.5px] font-light py-3 pl-[19px] hover:bg-light-6 hover:cursor-pointer">
								<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
									<path d="M12.5531 1.08598L1.62897 5.77063C1.44746 5.84781 1.29183 5.97536 1.18047 6.1382C1.0691 6.30105 1.00665 6.49239 1.0005 6.68962C0.994352 6.88683 1.04476 7.08169 1.14576 7.25117C1.24677 7.42064 1.39415 7.55765 1.57049 7.646L5.4234 9.57338L7.34986 13.4292C7.43822 13.6054 7.57499 13.7526 7.74413 13.8537C7.91328 13.9548 8.10776 14.0054 8.30474 13.9995C8.50161 13.9937 8.69274 13.9318 8.85571 13.821C9.01858 13.7102 9.14647 13.555 9.22433 13.374L13.9154 2.44457C13.9976 2.25405 14.0205 2.04321 13.9814 1.83943C13.9423 1.63566 13.8428 1.44837 13.696 1.30188C13.549 1.15539 13.3615 1.05647 13.1577 1.01797C12.9539 0.979466 12.7433 1.00316 12.5531 1.08598Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
								Aktuální poloha
							</div>

							<div class="result-item flex items-center gap-2.5 text-sm leading-[24.5px] font-light py-3 pl-[19px] hover:bg-light-6 hover:cursor-pointer hover:mx-1 hover:rounded-lg">
								<svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
									<path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
								<span class="city-name">České Budějovice</span>
							</div>


							<div class="result-item flex items-center gap-2.5 text-sm leading-[24.5px] font-light py-3 pl-[19px] hover:bg-light-6 hover:cursor-pointer hover:mx-1 hover:rounded-lg">
								<svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
									<path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
								<span class="city-name">Český Těšín</span>
							</div>

							<div class="result-item flex items-center gap-2.5 text-sm leading-[24.5px] font-light py-3 pl-[19px] hover:bg-light-6 hover:cursor-pointer hover:mx-1 hover:rounded-lg">
								<svg xmlns="http://www.w3.org/2000/svg" width="15" height="19" viewBox="0 0 15 19" fill="none">
									<path d="M4.25 7.35273C4.25 7.76986 4.33407 8.18289 4.49739 8.56827C4.66072 8.95364 4.90011 9.3038 5.2019 9.59875C5.5037 9.89371 5.862 10.1277 6.25623 10.2873C6.65056 10.4469 7.07317 10.5291 7.5 10.5291C7.92683 10.5291 8.34944 10.4469 8.74378 10.2873C9.138 10.1277 9.49626 9.89371 9.79808 9.59875C10.0999 9.3038 10.3393 8.95364 10.5026 8.56827C10.6659 8.18289 10.75 7.76986 10.75 7.35273C10.75 6.93559 10.6659 6.52256 10.5026 6.13718C10.3393 5.75181 10.0999 5.40166 9.79808 5.1067C9.49626 4.81174 9.138 4.57778 8.74378 4.41815C8.34944 4.25852 7.92683 4.17636 7.5 4.17636C7.07317 4.17636 6.65056 4.25852 6.25623 4.41815C5.862 4.57778 5.5037 4.81174 5.2019 5.1067C4.90011 5.40166 4.66072 5.75181 4.49739 6.13718C4.33407 6.52256 4.25 6.93559 4.25 7.35273Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
									<path d="M7.5 1C9.22391 1 10.8772 1.6693 12.0962 2.86067C13.3152 4.05204 14 5.66788 14 7.35273C14 10.1818 9.775 15.6759 8.137 17.6981C8.06095 17.7923 7.9641 17.8683 7.85371 17.9205C7.74321 17.9728 7.6222 18 7.49946 18C7.37683 18 7.25571 17.9728 7.14521 17.9205C7.03482 17.8683 6.93797 17.7923 6.86192 17.6981C5.225 15.6748 1 10.1818 1 7.35273C1 5.66788 1.68482 4.05204 2.90381 2.86067C4.1228 1.6693 5.77609 1 7.5 1Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
								<span class="city-name">Český Krumlov</span>
							</div>
						</div>
					</div>

					{* old search
					<div class="k-header__search k-header__search--city-picker">
						<form class="">
							<input type="text" class="k-header__search-input js-search-input" data-search-url="{link Ajax:search}" placeholder="{_"$websiteType.navbar.search.placeholder"}">
							<input type="submit" class="k-header__search-submit js-search-submit" data-search-url="{link Search:search, q => 'q'}" value="{_"$websiteType.navbar.search.submit"}">

							<div class="k-header__search-wrapper">					
							</div>
						</form>

						<form n:name="cityPickerControl-form" n:if="$localization->hasGeolocation()" class="order-first order-sm-1">
							<select n:name="cityId" style="display: none"></select>							
						</form>
					</div>
					*}

					<div class="lg:hidden hover:cursor-pointer" id="menuButton">
						<svg id="menuIcon" xmlns="http://www.w3.org/2000/svg" width="16" height="14" viewBox="0 0 16 14" fill="none">
							<rect width="16" height="2" rx="1" fill="#080B10"/>
							<rect width="13" height="2" rx="1" transform="matrix(1 0 0 -1 3 8)" fill="#080B10"/>
							<rect width="8" height="2" rx="1" transform="matrix(1 0 0 -1 8 14)" fill="#080B10"/>
						</svg>
						<svg id="closeIcon" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none" class="hidden">
							<path d="M0.242145 0.242149C0.565005 -0.0807163 1.08846 -0.0807163 1.41132 0.242149L13.7576 12.5887C14.0805 12.9115 14.0805 13.435 13.7576 13.7579C13.4348 14.0807 12.9113 14.0807 12.5884 13.7579L0.242146 1.41135C-0.0807146 1.08848 -0.0807155 0.565014 0.242145 0.242149Z" fill="#080B10"/>
							<path d="M13.7579 0.242149C13.435 -0.0807163 12.9115 -0.0807163 12.5887 0.242149L0.242372 12.5887C-0.0804883 12.9115 -0.0804892 13.435 0.242371 13.7579C0.565231 14.0807 1.08869 14.0807 1.41155 13.7579L13.7579 1.41135C14.0807 1.08848 14.0807 0.565014 13.7579 0.242149Z" fill="#080B10"/>
						</svg>
					</div>

					<div class="fixed inset-0 top-[44px] md:top-[80px] bg-black bg-opacity-50 hidden z-20" id="sidebar-overlay"></div>

					<div class="fixed top-[44px] md:top-[80px] right-0 h-full w-[249px] bg-white shadow-md transform translate-x-full transition-transform duration-300 z-30" id="sidebar">
						<div class="p-1.5">
							<div class="py-1.5 pl-[9px] bg-light-6 text-[10px] leading-[21px] text-light-1 uppercase font-medium rounded-md mb-[11px]">všeobecné</div>

							<div class="mb-2.5">
								<div class="flex items-center justify-between text-sm leading-[24.5px] py-1.5 px-[9px] mb-3 hover:bg-light-6 hover:rounded-md hover:cursor-pointer">
									Obchody
									<svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
										<path d="M1 6.49405L12.9164 6.50178M8.58692 12L14 6.49397L8.59864 1" stroke="#ADB3BF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
									</svg>
								</div>
								<div class="flex items-center justify-between text-sm leading-[24.5px] py-1.5 px-[9px] hover:bg-light-6 hover:rounded-md hover:cursor-pointer">
									Akční letáky
									<svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
										<path d="M1 6.49405L12.9164 6.50178M8.58692 12L14 6.49397L8.59864 1" stroke="#ADB3BF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
									</svg>
								</div>
							</div>
							<div class="py-1.5 pl-[9px] bg-light-6 text-[10px] leading-[21px] text-light-1 uppercase font-medium rounded-md mb-[11px]">obchody</div>
							{foreach range(1, 11) as $i}
								<div class="flex items-center justify-between text-sm leading-[24.5px] py-1.5 px-[9px] mb-3 hover:bg-light-6 hover:rounded-md hover:cursor-pointer">
									Penny
									<svg xmlns="http://www.w3.org/2000/svg" width="15" height="13" viewBox="0 0 15 13" fill="none">
										<path d="M1 6.49405L12.9164 6.50178M8.58692 12L14 6.49397L8.59864 1" stroke="#ADB3BF" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
									</svg>
								</div>
							{/foreach}
						</div>
					</div>

				</div>
			</div>
		</div>
	</header>
	
	<div n:foreach="$flashes as $flash" n:class="alert, 'alert-' . $flash->type">{$flash->message}</div>				

	{var $footerShopsTags = $footerShopsTags()}
	<div n:if="$footerShopsTags && count($footerShopsTags) >= 5" class="container">
		<div class="mt-3 hidden md:flex flex-wrap gap-3 lg:gap-0 lg:justify-between mb-10">
			<a n:foreach="$footerShopsTags as $footerTag" n:href="Tag:tag $footerTag" class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl {if $footerTag->isSeasonal() === true}font-bold{/if}">{$footerTag->getName()}</a>
		</div>
	</div>

	{block breadcrumb}{/block}

	{include content}

	{if in_array($presenterName, ['Oferto:Shop', 'Oferto:Tag', 'Oferto:Article']) && $user->isLoggedIn()}
		<div class="k-page-extension">			
			<span n:class="$pageExtension && $pageExtension->getTitle() ? k-page-extension__tag--green ,k-page-extension__tag">MT</span>
			<span n:class="$pageExtension && $pageExtension->getDescription() ? k-page-extension__tag--green ,k-page-extension__tag">MD</span>			
			<span n:class="$pageExtension && $pageExtension->getHeading() ? k-page-extension__tag--green ,k-page-extension__tag">H1</span>			
			<span n:class="$pageExtension && $pageExtension->getKeywords() ? k-page-extension__tag--green ,k-page-extension__tag">KW</span>			
			<span n:class="$pageExtension && $pageExtension->getShortDescription() ? k-page-extension__tag--green ,k-page-extension__tag">SD</span>
			<span n:class="$pageExtension && $pageExtension->getLongDescription() ? k-page-extension__tag--green ,k-page-extension__tag">LD</span>

			{if $presenterName == 'Oferto:Shop'}
				<span n:class="$contentBlocksAllowed ? k-page-extension__tag--green ,k-page-extension__tag">CB</span>
				<a n:href=":Admin:Shop:shop $shop->getId()" class="k-page-extension__btn" target="_blank">Edit shop</a>
			{/if}

			{if $presenterName == 'Oferto:Tag'}
				<a n:href=":Admin:Tag:tag $tag->getId()" class="k-page-extension__btn" target="_blank">Edit tag</a>
			{/if}

			{if $presenterName == 'Oferto:Article'}
				<a n:href=":Admin:Article:article $article->getId()" class="k-page-extension__btn" target="_blank">Edit article</a>
			{/if}

			{if $pageExtension}
				<a n:href=":Admin:Seo:pageExtension $pageExtension->getId()" class="k-page-extension__btn" target="_blank">Edit page extension</a>	

				{var $shopKeywords = $pageExtension->getKeywords()}							
				<span class="k-alternative-name js-alternative-name" data-alternative-name="{$shopKeywords}"></span>				
			{else}
				<a n:href=":Admin:Seo:pageExtension id => null, websiteId => $website->getId(), slug => $pageExtensionSlug" class="k-page-extension__btn" target="_blank">Edit page extension</a>
			{/if}

			<a n:href=":Admin:Translations:Dictionary:dictionary dictionary => 'oferto', localizationId => $localization->getId()" class="k-page-extension__btn" target="_blank">Translations</a>
		</div>
	{/if}		

	<footer class="bg-[#210B0B]">
		<div class="container">
			<div class="pt-[42px] md:pt-[70px]">
				<a class="flex-shrink-0" href="Homepage:default">					
					{if $localization->isItaly()}
						<svg class="w-[156px] h-[27px] md:w-[232px] md:h-[40px]" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1603.8133 345.6336" n:syntax="off"><defs><style>.g{fill:url(#e);}.h{fill:#2bb673;}.i{opacity:.53;}</style><linearGradient id="e" x1="0" y1="172.8168" x2="298.1663" y2="172.8168" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><g id="b"><g><path d="M370.9398,286.2329h-19.1896V78.4332c0-10.4255,8.4516-18.8771,18.8771-18.8771h12.0123l79.8758,169.7342,79.8743-169.7342h12.0123c10.4255,0,18.8771,8.4516,18.8771,18.8771v207.7997h-19.1881c-10.4255,0-18.8771-8.4516-18.8771-18.8771v-111.6996l-60.8417,130.5767h-23.7135l-60.8417-130.5767v111.6996c0,10.4255-8.4516,18.8771-18.8771,18.8771Z"/><path d="M721.5371,256.4358c-21.7389-21.7359-32.6054-49.6101-32.6054-83.6196,0-34.1115,10.8665-61.9857,32.6054-83.6196,21.7359-21.7359,49.6087-32.6039,83.6182-32.6039s61.8822,10.868,83.6211,32.6039c21.7359,21.6339,32.6054,49.5081,32.6054,83.6196,0,34.0095-10.8694,61.8837-32.6054,83.6196-21.7389,21.7389-49.6116,32.6054-83.6211,32.6054s-61.8822-10.8665-83.6182-32.6054Zm137.2855-144.618c-14.5616-13.5181-32.4487-20.2801-53.6673-20.2801s-39.1058,6.762-53.6644,20.2801c-16.3293,15.0818-24.4939,35.4136-24.4939,60.9984s8.1646,45.8663,24.4939,60.8432c14.5586,13.626,32.4487,20.4367,53.6644,20.4367s39.1058-6.8108,53.6673-20.4367c16.3263-14.9769,24.4939-35.2584,24.4939-60.8432s-8.1676-45.9165-24.4939-60.9984Z"/><path d="M937.9559,154.564v-44.3055c0-9.7757,1.8209-18.4606,5.4628-26.0533,4.6794-9.5673,11.8006-16.6929,21.3723-21.3723,7.8011-3.9523,16.6929-5.9284,26.6755-5.9284,13.3112,0,26.6785,3.2251,40.0931,9.6722l-16.5362,34.1647c-8.008-3.8458-15.8593-5.7717-23.5569-5.7717-4.1592,0-7.4877,.6237-9.9826,1.8712-1.7707,.8336-3.0713,2.1343-3.902,3.9005-1.0405,2.2895-1.5578,5.4598-1.5578,9.517v10.9197h27.1429c10.4255,0,18.8771,8.4516,18.8771,18.8771v14.5087h-46.02v112.7919c0,10.4255-8.4516,18.8771-18.8771,18.8771h-19.1911V154.564Z"/><path d="M1072.1676,213.5345c1.2475,10.6078,5.1997,19.6563,11.8568,27.1441,6.1338,6.9704,13.7782,11.7548,22.9302,14.3532,9.152,2.6013,18.2005,2.5496,27.1455-.1567,9.5688-2.8068,17.6802-8.1632,24.3373-16.0677h44.9292c-11.44,19.2425-24.9078,32.7103-40.4035,40.4049-12.8973,6.5521-27.1455,9.8289-42.7476,9.8289-26.105,0-47.5305-8.4248-64.2737-25.2729-16.8496-16.7446-25.2714-38.2219-25.2714-64.4304,0-26.105,8.4218-47.5291,25.2714-64.2752,16.7431-16.8481,38.1687-25.2729,64.2737-25.2729,26.2114,0,47.6843,8.4248,64.4304,25.2729,16.8496,16.7461,25.2743,38.1702,25.2743,64.2752v14.1965h-137.7525Zm95.4749-29.3301c-2.1845-13.3112-8.2179-23.6589-18.097-31.0446-9.0485-6.6541-19.5011-9.8792-31.3579-9.6722-11.8568,.3119-22.0493,3.9005-30.5775,10.7645-9.2554,7.4877-14.4049,17.4718-15.4425,29.9523h95.4749Z"/><path d="M1326.0257,146.7629v-14.352c0-10.4255,8.4516-18.8771,18.8771-18.8771h9.3592v-32.4487l19.0883-.1009c10.4644-.0553,18.9769,8.4123,18.9769,18.8768v13.6728h26.1859v14.352c0,10.4255-8.4516,18.8771-18.8771,18.8771h-7.3088v120.5929c0,10.4255-8.4516,18.8771-18.8771,18.8771h-19.1881V146.7629h-28.2363Z"/><path d="M1226.7383,160.4223c0-17.1498-.0386-35.3848-.3322-49.8209h20.1877c10.2028-.0002,18.5644,8.1057,18.8693,18.3039,.1667,5.5755,.2714,11.1938,.2714,15.0003,7.6162-19.7256,14.275-34.1453,43.635-34.304v27.1254c-.0001,9.4839-6.8211,17.8192-16.2135,19.1335-21.3797,2.9917-27.4216,16.2288-27.4216,55.0946v56.3758c0,10.4255-8.4516,18.8771-18.8771,18.8771h-20.1191v-125.7859Z"/><path d="M589.2932,160.4223c0-17.1498-.0386-35.3848-.3322-49.8209h20.1876c10.2028-.0002,18.5644,8.1057,18.8693,18.3039,.1667,5.5755,.2714,11.1938,.2714,15.0004,7.6162-19.7256,14.275-34.1453,43.635-34.304l-.0002,27.1255c0,9.4839-6.821,17.8192-16.2134,19.1335-21.3796,2.9918-27.4215,16.2288-27.4215,55.0945v56.3758c0,10.4255-8.4516,18.8771-18.8771,18.8771h-20.1191v-125.7859Z"/><path d="M1566.8913,262.7671c-2.6614,3.3276-6.3205,6.3205-13.3072,11.3103-14.9677,10.311-28.9377,14.9677-44.903,14.9677-46.9015,0-81.4935-38.2511-81.4935-90.1424,0-50.2258,36.2558-88.4786,83.4888-88.4786,21.2881,0,43.9054,10.9772,56.2149,26.9425v-2.4111c0-10.4255,8.4516-18.8771,18.8771-18.8771h18.0449v148.4352c0,10.4255-8.4516,18.8771-18.8771,18.8771h-18.0449v-20.6236Zm-49.8944-10.311c27.9401,0,48.8968-22.6189,48.8968-52.5542,0-30.6032-20.6253-52.5558-48.8968-52.5558-27.9401,0-49.563,23.2834-49.563,53.2204,0,29.2708,21.9543,51.8897,49.563,51.8897Z"/></g></g><g id="d"><path class="g" d="M175.5053,285.625c69.6613-12.0216,122.6609-70.8175,122.6609-141.6843C298.1663,64.4449,231.4143,0,149.0831,0S0,64.4449,0,143.9407c0,99.7468,78.455,187.7106,204.0452,201.6929-11.7007-3.3416-25.467-10.4569-36.5573-21.1061-12.2584-11.771-7.2333-36.2707,8.0174-38.9025ZM79.2227,111.2952c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7252-14.4372,30.1058-30.9858,30.1058s-30.9873-13.3806-30.9873-30.1058Zm18.7642,97.8607l93.7362-123.2586,7.2909,5.5295-93.5406,123.1065-7.4866-5.3774Zm58.0151-19.8673c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7269-14.4372,30.1075-30.9858,30.1075s-30.9873-13.3806-30.9873-30.1075Zm-45.792-58.4521c-10.9162,0-20.4227-8.8017-20.4227-19.5412,0-10.9162,9.5065-19.7195,20.4227-19.7195s20.4227,8.8033,20.4227,19.7195c0,10.7396-9.5082,19.5412-20.4227,19.5412Zm76.7793,77.995c-10.9162,0-20.4244-8.8032-20.4244-19.5429,0-10.9145,9.5082-19.7179,20.4244-19.7179s20.4227,8.8033,20.4227,19.7179c0,10.7397-9.508,19.5429-20.4227,19.5429Z"/></g><g id="f" class="i"><path class="h" d="M175.5053,285.625c69.6613-12.0216,122.6609-70.8175,122.6609-141.6843C298.1663,64.4449,231.4143,0,149.0831,0S0,64.4449,0,143.9407c0,99.7468,78.455,187.7106,204.0452,201.6929-11.7007-3.3416-25.467-10.4569-36.5573-21.1061-12.2584-11.771-7.2333-36.2707,8.0174-38.9025ZM79.2227,111.2952c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7252-14.4372,30.1058-30.9858,30.1058s-30.9873-13.3806-30.9873-30.1058Zm18.7642,97.8607l93.7362-123.2586,7.2909,5.5295-93.5406,123.1065-7.4866-5.3774Zm58.0151-19.8673c0-16.9017,14.2606-30.2825,30.9873-30.2825s30.9858,13.3808,30.9858,30.2825c0,16.7269-14.4372,30.1075-30.9858,30.1075s-30.9873-13.3806-30.9873-30.1075Zm-45.792-58.4521c-10.9162,0-20.4227-8.8017-20.4227-19.5412,0-10.9162,9.5065-19.7195,20.4227-19.7195s20.4227,8.8033,20.4227,19.7195c0,10.7396-9.5082,19.5412-20.4227,19.5412Zm76.7793,77.995c-10.9162,0-20.4244-8.8032-20.4244-19.5429,0-10.9145,9.5082-19.7179,20.4244-19.7179s20.4227,8.8033,20.4227,19.7179c0,10.7397-9.508,19.5429-20.4227,19.5429Z"/></g></svg>
					{else}
						<svg class="w-[156px] h-[27px] md:w-[232px] md:h-[40px]" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 530.3963 114.1832" n:syntax="off"><defs><style>.a{fill:url(#a);}.b{opacity:0.53;}.c{fill:#2bb673;}</style><linearGradient id="a" x1="13.0185" y1="67.1923" x2="111.5204" y2="67.1923" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><path d="M135.5618,104.66h-6.3394V36.0118a6.2362,6.2362,0,0,1,6.2362-6.2362h3.9683l26.3877,56.0733,26.3872-56.0733H196.17a6.2362,6.2362,0,0,1,6.2362,6.2362V104.66h-6.3389a6.2363,6.2363,0,0,1-6.2363-6.2362V61.5232l-20.1,43.1372h-7.834l-20.1-43.1372v36.901A6.2362,6.2362,0,0,1,135.5618,104.66Z" transform="translate(-13.0185 -10.1007)"/><path d="M251.3848,94.8166q-10.7724-10.7709-10.7714-27.6245,0-16.9035,10.7714-27.6245,10.771-10.7709,27.6241-10.771t27.625,10.771q10.7709,10.7205,10.7714,27.6245,0,16.8531-10.7714,27.6245t-27.625,10.7715Q262.1558,105.5881,251.3848,94.8166Zm45.3536-47.7758a26.8076,26.8076,0,0,0-35.4581,0q-8.0918,7.4736-8.0918,20.1513t8.0918,20.1a26.6535,26.6535,0,0,0,35.4581,0q8.09-7.4215,8.0918-20.1Q304.83,54.514,296.7384,47.0408Z" transform="translate(-13.0185 -10.1007)"/><path d="M322.8808,61.1623V46.5256a19.6663,19.6663,0,0,1,1.8047-8.6069,14.9751,14.9751,0,0,1,7.06-7.0606A19.2313,19.2313,0,0,1,340.5585,28.9a30.3644,30.3644,0,0,1,13.2451,3.1953l-5.4629,11.2866a17.8937,17.8937,0,0,0-7.7822-1.9068,7.4282,7.4282,0,0,0-3.2978.6182,2.6134,2.6134,0,0,0-1.2891,1.2886,7.7685,7.7685,0,0,0-.5147,3.144V50.133h8.9669a6.2363,6.2363,0,0,1,6.2363,6.2363v4.793H335.4569V98.4242a6.2362,6.2362,0,0,1-6.2362,6.2362h-6.34Z" transform="translate(-13.0185 -10.1007)"/><path d="M367.2188,80.6438a15.9479,15.9479,0,0,0,3.917,8.9673,16.0561,16.0561,0,0,0,16.543,4.69,16.3474,16.3474,0,0,0,8.04-5.3081h14.8428q-5.6689,9.5353-13.3477,13.3482a30.7246,30.7246,0,0,1-14.122,3.247q-12.936,0-21.2334-8.3491-8.35-8.2976-8.3487-21.2851,0-12.9361,8.3487-21.2339,8.2968-8.349,21.2334-8.3492,12.9888,0,21.2851,8.3492,8.35,8.2983,8.35,21.2339v4.69Zm31.541-9.6895a15.35,15.35,0,0,0-5.9785-10.2558,16.513,16.513,0,0,0-10.3593-3.1953A16.327,16.327,0,0,0,372.32,61.0593a13.6289,13.6289,0,0,0-5.1016,9.895Z" transform="translate(-13.0185 -10.1007)"/><path d="M451.0832,58.5852V53.8439a6.2361,6.2361,0,0,1,6.2362-6.2362h3.0919v-10.72l6.306-.0333a6.2362,6.2362,0,0,1,6.2692,6.2361v4.517h8.6507V52.349a6.2362,6.2362,0,0,1-6.2362,6.2362h-2.4145v39.839A6.2363,6.2363,0,0,1,466.75,104.66h-6.339V58.5852Z" transform="translate(-13.0185 -10.1007)"/><path d="M513.987,46.68q12.8832,0,21.1816,8.2979,8.2457,8.2462,8.2461,21.13,0,12.9369-8.2461,21.1822-8.2983,8.2983-21.1816,8.2978-12.8848,0-21.1826-8.2978-8.2456-8.2449-8.2461-21.1822,0-12.8832,8.2461-21.13Q501.1027,46.68,513.987,46.68ZM502.3913,89.56a16.3447,16.3447,0,0,0,11.5957,4.4321A16.0587,16.0587,0,0,0,525.5827,89.56q5.2573-4.9988,5.2568-13.4517,0-8.3994-5.2568-13.4a16.353,16.353,0,0,0-11.5957-4.4321,16.06,16.06,0,0,0-11.5957,4.4321q-5.2574,5-5.2578,13.4Q497.1335,84.5618,502.3913,89.56Z" transform="translate(-13.0185 -10.1007)"/><path d="M418.2827,63.0977c0-5.6656-.0128-11.69-.11-16.4588h6.6692a6.2353,6.2353,0,0,1,6.2337,6.0469c.055,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4153-11.3326l0,8.9611a6.3352,6.3352,0,0,1-5.3562,6.3209c-7.063.9884-9.059,5.3614-9.059,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6465Z" transform="translate(-13.0185 -10.1007)"/><path d="M207.6968,63.0977c0-5.6656-.0127-11.69-.11-16.4588h6.6692a6.2351,6.2351,0,0,1,6.2336,6.0469c.0551,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4152-11.3326V55.27a6.3353,6.3353,0,0,1-5.3563,6.3209c-7.0629.9884-9.0589,5.3614-9.0589,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6466Z" transform="translate(-13.0185 -10.1007)"/><path class="a" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/><g class="b"><path class="c" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/></g></svg>
					{/if}
				</a>

				<div class="w-full h-px bg-white/25 mt-[42px] mb-[15px] md:my-[42px]"></div>

				<div class="grid grid-cols-1 md:grid-cols-4">
					<div class="mb-4 border-b border-white/25 md:border-none">
						<div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
							{_"$websiteType.footer.shops"}
							<svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
								<path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</div>
						<ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 pb-[71px] md:pb-0">
							<li n:foreach="$footerShops() as $footerShop">
								<a n:href="Shop:shop $footerShop" class="">{$footerShop->getName()}</a>
							</li>
						</ul>
					</div>
					<div class="mb-4 border-b border-white/25 md:border-none">
						<div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
							O Kaufino
							<svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
								<path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</div>
						<ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 mb-[45px] pb-[71px] md:pb-0">
							<li>Letáky</li>
							<li>Letáky</li>
							<li>Letáky</li>
							<li>Letáky</li>
							<li>Letáky</li>
							<li>Letáky</li>
							<li>Letáky</li>
							<li>Letáky</li>
							<li>Letáky</li>
						</ul>
					</div>
					<div class="mb-4 border-b border-white/25 md:border-none">
						<div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
							{_"$websiteType.footer.shops"}
							<svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
								<path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</div>
						<ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 pb-[71px] md:pb-0">
							<li>Penny</li>
							<li>Penny</li>
							<li>Penny</li>
							<li>Penny</li>
							<li>Penny</li>
							<li>Penny</li>
							<li>Penny</li>
							<li>Penny</li>
							<li>Penny</li>
						</ul>
					</div>
					<div class="mb-4 border-b border-white/25 md:border-none">
						<div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
							{_"$websiteType.footer.aboutOferto"}
							<svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
								<path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</div>
						<ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 mb-[45px]">							
							<li><a n:href="Leaflets:leaflets" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.leaflets"}</a></li>
							<li><a n:href="Shops:shops" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.shops"}</a></li>
							<li><a n:href="Articles:articles" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.articles"}</a></li>
							<li><a n:href="Static:aboutUs" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_"$websiteType.footer.aboutUs"}</a></li>						
							{if $conditions}
								<li><a n:href="Conditions:default, $conditions" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$conditions->getName()}</a></li>
							{/if}
						</ul>
						<div class="hidden md:block md:text-[26px] md:leading-[39px] text-white mb-6">
							{_"$websiteType.footer.nextCountries"}
						</div>
						<div id="dropdown" class="hidden md:block relative inline-block">
							<button id="dropdownToggle" class="flex items-center gap-1.5 bg-transparent text-white py-[13px] pl-3 pr-[15px] border border-gray-600 rounded-lg text-sm">
								<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
									<path d="M9.5 19C14.7467 19 19 14.7467 19 9.5C19 4.25329 14.7467 0 9.5 0C4.25329 0 0 4.25329 0 9.5C0 14.7467 4.25329 19 9.5 19Z" fill="white"/>
									<path d="M9.5 19.0001C14.7467 19.0001 19 14.7468 19 9.50013C19 8.33809 18.791 7.22492 18.4091 6.1958H0.590893C0.209037 7.22492 0 8.33809 0 9.50013C0 14.7468 4.25333 19.0001 9.5 19.0001Z" fill="#0052B4"/>
									<path d="M9.50042 18.9999C13.5851 18.9999 17.0672 16.4218 18.4095 12.8042H0.591309C1.93359 16.4218 5.41575 18.9999 9.50042 18.9999Z" fill="#D80027"/>
									<path d="M2.45654 5.36963V10.1087C2.45654 12.8045 5.97826 13.6305 5.97826 13.6305C5.97826 13.6305 9.49994 12.8045 9.49994 10.1087V5.36963H2.45654Z" fill="white"/>
									<path d="M3.28223 5.36963V10.1087C3.28223 10.4252 3.35251 10.7241 3.49175 11.0043H8.46392C8.60315 10.7241 8.67344 10.4252 8.67344 10.1087V5.36963H3.28223Z" fill="#D80027"/>
									<path d="M7.63001 8.67404H6.39093V7.84795H7.21702V7.02186H6.39093V6.1958H5.56484V7.02186H4.73879V7.84795H5.56484V8.67404H4.32568V9.50013H5.56484V10.3262H6.39093V9.50013H7.63001V8.67404Z" fill="white"/>
									<path d="M4.61894 12.1574C5.15127 12.492 5.69819 12.6862 5.97818 12.7726C6.25817 12.6862 6.80509 12.492 7.33742 12.1574C7.87425 11.82 8.25057 11.4342 8.46425 11.0042C8.22857 10.8375 7.94101 10.7392 7.63037 10.7392C7.51726 10.7392 7.40737 10.7526 7.30172 10.7772C7.07784 10.2685 6.56963 9.91309 5.97822 9.91309C5.38681 9.91309 4.87856 10.2685 4.65471 10.7772C4.54906 10.7526 4.43914 10.7392 4.32607 10.7392C4.01543 10.7392 3.72787 10.8375 3.49219 11.0042C3.70575 11.4341 4.08208 11.82 4.61894 12.1574Z" fill="#0052B4"/>
								</svg>
								<span>Czech</span>
								<svg id="dropdownArrow" class="ml-[38px] transition-transform duration-300 ease-out" xmlns="http://www.w3.org/2000/svg" width="12" height="7" viewBox="0 0 12 7" fill="none">
									<path d="M1 1.00008L6.00015 6L11 1" stroke="white" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>
							</button>
							<div id="dropdownMenu" class="hidden absolute bg-[#210B0B] border border-gray-600 rounded-lg mt-2 min-w-[165px] z-10 py-2">
								{foreach $footerWebsites() as $footerWebsite}							
									{continueIf $footerWebsite === $website}									
									<a href="{$footerWebsite->getDomain()}" class="flex gap-1.5 items-center py-2 px-4 text-white cursor-pointer hover:bg-gray-700 text-sm">
										<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
											<path d="M9.5 19C14.7467 19 19 14.7467 19 9.5C19 4.25329 14.7467 0 9.5 0C4.25329 0 0 4.25329 0 9.5C0 14.7467 4.25329 19 9.5 19Z" fill="white"/>
											<path d="M9.5 19.0001C14.7467 19.0001 19 14.7468 19 9.50013C19 8.33809 18.791 7.22492 18.4091 6.1958H0.590893C0.209037 7.22492 0 8.33809 0 9.50013C0 14.7468 4.25333 19.0001 9.5 19.0001Z" fill="#0052B4"/>
											<path d="M9.50017 18.9999C13.5848 18.9999 17.067 16.4218 18.4093 12.8042H0.591064C1.93335 16.4218 5.41551 18.9999 9.50017 18.9999Z" fill="#D80027"/>
											<path d="M2.45679 5.36963V10.1087C2.45679 12.8045 5.9785 13.6305 5.9785 13.6305C5.9785 13.6305 9.50018 12.8045 9.50018 10.1087V5.36963H2.45679Z" fill="white"/>
											<path d="M3.28247 5.36963V10.1087C3.28247 10.4252 3.35276 10.7241 3.49199 11.0043H8.46416C8.6034 10.7241 8.67368 10.4252 8.67368 10.1087V5.36963H3.28247Z" fill="#D80027"/>
											<path d="M7.63026 8.67404H6.39118V7.84795H7.21727V7.02186H6.39118V6.1958H5.56508V7.02186H4.73903V7.84795H5.56508V8.67404H4.32593V9.50013H5.56508V10.3262H6.39118V9.50013H7.63026V8.67404Z" fill="white"/>
											<path d="M4.61894 12.1574C5.15127 12.492 5.69819 12.6862 5.97818 12.7726C6.25817 12.6862 6.80509 12.492 7.33742 12.1574C7.87425 11.82 8.25057 11.4342 8.46425 11.0042C8.22857 10.8375 7.94101 10.7392 7.63037 10.7392C7.51726 10.7392 7.40737 10.7526 7.30172 10.7772C7.07784 10.2685 6.56963 9.91309 5.97822 9.91309C5.38681 9.91309 4.87856 10.2685 4.65471 10.7772C4.54906 10.7526 4.43914 10.7392 4.32607 10.7392C4.01543 10.7392 3.72787 10.8375 3.49219 11.0042C3.70575 11.4341 4.08208 11.82 4.61894 12.1574Z" fill="#0052B4"/>
										</svg>
										{$footerWebsite->getModule() === 'oferto_com' ? 'MrOferto.com' : $footerWebsite->getLocalization()->getOriginalName()}
									</a>																	
								{/foreach}
																
							</div>
						</div>
					</div>
					<div class="md:hidden">
						<div class="mobile-footer w-full text-left md:text-[26px] md:leading-[39px] text-white mb-[15px] md:mb-[11px] flex justify-between items-center">
							<div class="flex items-center gap-2">
								<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
									<path d="M9.5 19C14.7467 19 19 14.7467 19 9.5C19 4.25329 14.7467 0 9.5 0C4.25329 0 0 4.25329 0 9.5C0 14.7467 4.25329 19 9.5 19Z" fill="white"/>
									<path d="M9.5 19.0001C14.7467 19.0001 19 14.7468 19 9.50013C19 8.33809 18.791 7.22492 18.4091 6.1958H0.590893C0.209037 7.22492 0 8.33809 0 9.50013C0 14.7468 4.25333 19.0001 9.5 19.0001Z" fill="#0052B4"/>

									<path d="M9.50017 18.9999C13.5848 18.9999 17.067 16.4218 18.4093 12.8042H0.591064C1.93335 16.4218 5.41551 18.9999 9.50017 18.9999Z" fill="#D80027"/>
									<path d="M2.45679 5.36963V10.1087C2.45679 12.8045 5.9785 13.6305 5.9785 13.6305C5.9785 13.6305 9.50018 12.8045 9.50018 10.1087V5.36963H2.45679Z" fill="white"/>
									<path d="M3.28247 5.36963V10.1087C3.28247 10.4252 3.35276 10.7241 3.49199 11.0043H8.46416C8.6034 10.7241 8.67368 10.4252 8.67368 10.1087V5.36963H3.28247Z" fill="#D80027"/>
									<path d="M7.63026 8.67404H6.39118V7.84795H7.21727V7.02186H6.39118V6.1958H5.56508V7.02186H4.73903V7.84795H5.56508V8.67404H4.32593V9.50013H5.56508V10.3262H6.39118V9.50013H7.63026V8.67404Z" fill="white"/>
									<path d="M4.61894 12.1574C5.15127 12.492 5.69819 12.6862 5.97818 12.7726C6.25817 12.6862 6.80509 12.492 7.33742 12.1574C7.87425 11.82 8.25057 11.4342 8.46425 11.0042C8.22857 10.8375 7.94101 10.7392 7.63037 10.7392C7.51726 10.7392 7.40737 10.7526 7.30172 10.7772C7.07784 10.2685 6.56963 9.91309 5.97822 9.91309C5.38681 9.91309 4.87856 10.2685 4.65471 10.7772C4.54906 10.7526 4.43914 10.7392 4.32607 10.7392C4.01543 10.7392 3.72787 10.8375 3.49219 11.0042C3.70575 11.4341 4.08208 11.82 4.61894 12.1574Z" fill="#0052B4"/>
								</svg>
								Czech
							</div>
							<svg class="footer-arrow md:hidden" xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
								<path d="M12 6L6.5 1L1 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</div>
						<ul class="hidden md:block text-sm font-light leading-[35px] text-white/50 mb-[45px] pb-[71px] md:pb-0">
							{foreach $footerWebsites() as $footerWebsite}							
								{continueIf $footerWebsite === $website}
								<li>
									<a href="{$footerWebsite->getDomain()}" class="flex items-center gap-2">
										<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
											<path d="M9.5 19C14.7467 19 19 14.7467 19 9.5C19 4.25329 14.7467 0 9.5 0C4.25329 0 0 4.25329 0 9.5C0 14.7467 4.25329 19 9.5 19Z" fill="white"/>
											<path d="M9.5 19.0001C14.7467 19.0001 19 14.7468 19 9.50013C19 8.33809 18.791 7.22492 18.4091 6.1958H0.590893C0.209037 7.22492 0 8.33809 0 9.50013C0 14.7468 4.25333 19.0001 9.5 19.0001Z" fill="#0052B4"/>
											<path d="M9.50017 18.9999C13.5848 18.9999 17.067 16.4218 18.4093 12.8042H0.591064C1.93335 16.4218 5.41551 18.9999 9.50017 18.9999Z" fill="#D80027"/>
											<path d="M2.45679 5.36963V10.1087C2.45679 12.8045 5.9785 13.6305 5.9785 13.6305C5.9785 13.6305 9.50018 12.8045 9.50018 10.1087V5.36963H2.45679Z" fill="white"/>
											<path d="M3.28247 5.36963V10.1087C3.28247 10.4252 3.35276 10.7241 3.49199 11.0043H8.46416C8.6034 10.7241 8.67368 10.4252 8.67368 10.1087V5.36963H3.28247Z" fill="#D80027"/>
											<path d="M7.63026 8.67404H6.39118V7.84795H7.21727V7.02186H6.39118V6.1958H5.56508V7.02186H4.73903V7.84795H5.56508V8.67404H4.32593V9.50013H5.56508V10.3262H6.39118V9.50013H7.63026V8.67404Z" fill="white"/>
											<path d="M4.61894 12.1574C5.15127 12.492 5.69819 12.6862 5.97818 12.7726C6.25817 12.6862 6.80509 12.492 7.33742 12.1574C7.87425 11.82 8.25057 11.4342 8.46425 11.0042C8.22857 10.8375 7.94101 10.7392 7.63037 10.7392C7.51726 10.7392 7.40737 10.7526 7.30172 10.7772C7.07784 10.2685 6.56963 9.91309 5.97822 9.91309C5.38681 9.91309 4.87856 10.2685 4.65471 10.7772C4.54906 10.7526 4.43914 10.7392 4.32607 10.7392C4.01543 10.7392 3.72787 10.8375 3.49219 11.0042C3.70575 11.4341 4.08208 11.82 4.61894 12.1574Z" fill="#0052B4"/>
										</svg>
										{$footerWebsite->getModule() === 'oferto_com' ? 'MrOferto.com' : $footerWebsite->getLocalization()->getOriginalName()}
									</a>
								</li>								
							{/foreach}							
						</ul>
					</div>
				</div>

				<div class="w-full h-px bg-white/25 mt-[2px] md:mt-[42px]"></div>

				<div class="text-center text-xs md:text-sm leading-[35px] font-light text-white/50 pt-[42px] md:pt-[38px]  pb-[52px] md:pb-[47px]">Copyright © {date('Y')} <span class="font-medium text-white">{_"$websiteType.footer.copyright"}</span></div>
			</div>
		</div>
	</footer>	

	{if $localization->isCzech() && $channel !== 'c1'}
		<div id="gm-sticky"></div>
		<script src="https://ads.richmedia.cz/js/gm-mroferto-cz.js"></script>
	{/if}

	{block scripts}		

		{if $newDesign}
			<script src="{$basePath}/js/swiper/swiper-bundle.min.js"></script>

			<script>
				function toggleModal() {
					var modal = document.getElementById('select-city-modal');
					var overlay = document.getElementById('overlay');
					if (modal.classList.contains('hidden')) {
						modal.classList.remove('hidden');
						overlay.classList.remove('hidden');
					} else {
						modal.classList.add('hidden');
						overlay.classList.add('hidden');
					}
				}

				function hideModal() {
					var modal = document.getElementById('select-city-modal');
					var overlay = document.getElementById('overlay');
					modal.classList.add('hidden');
					overlay.classList.add('hidden');
				}

				const menuButton = document.getElementById('menuButton');
				const overlay = document.getElementById('sidebar-overlay');
				const sidebar = document.getElementById('sidebar');
				const menuIcon = document.getElementById('menuIcon');
				const closeIcon = document.getElementById('closeIcon');

				function openSidebar() {
					sidebar.classList.remove('translate-x-full');
					overlay.classList.remove('hidden');
					menuIcon.classList.add('hidden');
					closeIcon.classList.remove('hidden');
				}

				function closeSidebar() {
					sidebar.classList.add('translate-x-full');
					overlay.classList.add('hidden');
					menuIcon.classList.remove('hidden');
					closeIcon.classList.add('hidden');
				}

				menuButton.addEventListener('click', () => {
					if (sidebar.classList.contains('translate-x-full')) {
						openSidebar();
					} else {
						closeSidebar();
					}
				});

					overlay.addEventListener('click', closeSidebar);

			/*		// TABS
					function showTab(tabNumber) {
						var tabContents = document.querySelectorAll('.tab-content');
						tabContents.forEach(function(content) {
							content.classList.add('hidden');
							content.classList.remove('block');
						});

						var tabs = document.querySelectorAll('.tab-item');
						tabs.forEach(function(tab) {
							tab.classList.remove('tabs-active');
						});

						document.getElementById('tab-content-' + tabNumber).classList.add('block');
						document.getElementById('tab-content-' + tabNumber).classList.remove('hidden');

						tabs[tabNumber - 1].classList.add('tabs-active');
					}
					showTab(1);*/

					// ToggleInfo
					function toggleContent(element) {
						var content = element.querySelector('.content');
						var iconPlus = element.querySelector('.icon-plus');
						var iconMinus = element.querySelector('.icon-minus');

						content.classList.toggle('hidden');
						element.classList.toggle('active');

						iconPlus.classList.toggle('hidden');
						iconMinus.classList.toggle('hidden');
					}

					// SWIPER SHOPS
					const swiper = new Swiper('.swiper', {
						spaceBetween: 36,
						direction: 'horizontal',
						navigation: {
							nextEl: '.custom-next',
							prevEl: '.custom-prev',
						},
						breakpoints: {
							1024: {
								slidesPerView: 7,
							},
							768: {
								spaceBetween: 18,
								slidesPerView: 5,
							},
							300: {
								spaceBetween: 18,
								slidesPerView: 3.5,
							},
						},
						on: {
							init: function () {
								updateButtonStates(this);
							},
							slideChange: function () {
								updateButtonStates(this);
							}
						}
					});

					function updateButtonStates(swiper) {
						const prevButton = document.querySelector('.custom-prev');
						const nextButton = document.querySelector('.custom-next');

						if (prevButton) {
							if (swiper.isBeginning) {
								prevButton.classList.add('swiper-button-disabled');
								toggleSvgVisibility(prevButton, true);
							} else {
								prevButton.classList.remove('swiper-button-disabled');
								toggleSvgVisibility(prevButton, false);
							}
						}

						if (nextButton) {
							if (swiper.isEnd) {
								nextButton.classList.add('swiper-button-disabled');
								toggleSvgVisibility(nextButton, true);
							} else {
								nextButton.classList.remove('swiper-button-disabled');
								toggleSvgVisibility(nextButton, false);
							}
						}
					}

					function toggleSvgVisibility(button, isDisabled) {
						const activeSvg = button.querySelector('.active-svg');
						const disabledSvg = button.querySelector('.disabled-svg');

						if (isDisabled) {
							activeSvg.classList.add('hidden');
							disabledSvg.classList.remove('hidden');
						} else {
							activeSvg.classList.remove('hidden');
							disabledSvg.classList.add('hidden');
						}
					}

					// Footer dropdown
					document.addEventListener('DOMContentLoaded', function() {
						const dropdownToggle = document.getElementById('dropdownToggle');
						const dropdownMenu = document.getElementById('dropdownMenu');
						const dropdownArrow = document.getElementById('dropdownArrow');

						dropdownToggle.addEventListener('click', function() {
							dropdownMenu.classList.toggle('hidden');
							dropdownArrow.classList.toggle('rotate-180');
						});

						// Close dropdown if clicking outside of it
						window.addEventListener('click', function(e) {
							if (!document.getElementById('dropdown').contains(e.target)) {
								dropdownMenu.classList.add('hidden');
								dropdownArrow.classList.remove('rotate-180');
							}
						});
					});

					document.addEventListener('DOMContentLoaded', function() {
						const footerSections = document.querySelectorAll('.mobile-footer');

						footerSections.forEach(footerSection => {
							footerSection.addEventListener('click', function() {
								const list = this.nextElementSibling;
								const svgs = this.querySelectorAll('.footer-arrow');

								if (window.innerWidth < 768) {
									list.classList.toggle('hidden');
									svgs.forEach(svg => {
										svg.classList.toggle('rotate-180');
									});
								}
							});
						});
					});


					// Search modal
					document.getElementById('search-input-shops').addEventListener('input', function() {
						var searchModal = document.getElementById('search-modal');
						var overlaySearch = document.getElementById('overlay-search-modal');
						if (this.value.length > 0) {
							searchModal.classList.remove('hidden');
							overlaySearch.classList.remove('hidden');
						} else {
							searchModal.classList.add('hidden');
							overlaySearch.classList.add('hidden');
						}
					});
					document.getElementById('overlay-search-modal').addEventListener('click', function() {
						var searchModal = document.getElementById('search-modal');
						var overlaySearch = document.getElementById('overlay-search-modal');
						var searchInput = document.getElementById('search-input-shops');

						searchModal.classList.add('hidden');
						overlaySearch.classList.add('hidden');
						searchInput.value = '';
					});
			</script>

			<script n:syntax=off>
				// Zvyraznenie hľadaného textu v zozname miest + regex
				function cleanString(str) {
					return str
						.normalize("NFD")
						.replace(/[\u0300-\u036f]/g, "")
						.replace(/[^a-z0-9]/gi, '');
				}
				function createDiacriticInsensitiveRegex(str) {
					const diacriticMap = {
						'a': '[aáä]', 'c': '[cč]', 'd': '[dď]', 'e': '[eéě]', 'i': '[ií]',
						'l': '[lĺľ]', 'n': '[nň]', 'o': '[oóô]', 'r': '[rŕř]', 's': '[sš]',
						't': '[tť]', 'u': '[uúů]', 'y': '[yý]', 'z': '[zž]'
					};
					return str.split('').map(char => diacriticMap[char.toLowerCase()] || char).join('');
				}
				document.getElementById("select-city-input").addEventListener('input', function() {
					const searchTerm = this.value.toLowerCase();
					const cleanSearchTerm = cleanString(searchTerm);
					const results = document.querySelectorAll('.result-item .city-name');

					results.forEach(result => {
						const cityName = result.textContent;
						const cleanCityName = cleanString(cityName.toLowerCase());

						if (cleanCityName.includes(cleanSearchTerm)) {
							const regex = new RegExp(createDiacriticInsensitiveRegex(searchTerm), 'gi');
							const highlightedName = cityName.replace(regex, match => `<span style="color: #BC2026; font-weight: bold;">${match}</span>`);
							result.innerHTML = highlightedName;
							result.closest('.result-item').style.display = 'flex';
						} else {
							result.innerHTML = cityName;
							result.closest('.result-item').style.display = 'none';
						}
					});
				});
			</script>

		{/if}

		<script src="{$basePath}/js/lazysizes/lazysizes.min.js" async></script>		
		<script src="{$basePath}/js/main.js?v={$version}" async></script>

		{if isset($userLoggedIn) && $pageExtension && $pageExtension->getKeywords()}
			<script src="{$basePath}/js/page-extension.js?v={$version}" async></script>
		{/if}

		<script src="{$basePath}/js/main.oferto.js?v={$version}" async></script>

		{if $localization->hasGeolocation()}
		<script src="/js/nice-select2.js"></script>
		<script>
			const cityPicker = document.getElementById('frm-cityPickerControl-form-cityId');
			const form = document.getElementById('frm-cityPickerControl-form');
			const niceSelect = NiceSelect.bind(cityPicker, { searchable: true });

			cityPicker.addEventListener('change', function(event) {
				const selectedCityId = event.target.value;

				if (selectedCityId === 'currentLocation') {
					getLocationFromBrowser();
				} else {
					form.submit();
				}
			});

			function checkCookie(name) {
				var cookies = document.cookie.split(';');
				for (var i = 0; i < cookies.length; i++) {
					var cookie = cookies[i].trim();
					if (cookie.indexOf(name + '=') === 0) {
						return true;
					}
				}
				return false;
			}

			function getCookie(name) {
				let cookieArr = document.cookie.split(";");

				for (let i = 0; i < cookieArr.length; i++) {
					let cookie = cookieArr[i].trim();

					if (cookie.indexOf(name + "=") === 0) {
						return cookie.substring(name.length + 1);
					}
				}

				return null;
			}

			function resolveCityIdFromUserIp() {
				const xhr = new XMLHttpRequest();
				xhr.open('POST', {link :Api:Offerista:resolveUserLocationFromIp, websiteId: $presenter->website->getId()}, true);
				xhr.setRequestHeader('Content-Type', 'application/json');

				xhr.onload = function() {
					if (xhr.status === 200) {
						const response = JSON.parse(xhr.responseText);
						setUserCityId(response.cityId);

						return response;
					} else {
						console.error('Error:', xhr.status, xhr.statusText);
					}
				};

				xhr.onerror = function() {
					console.error('Request failed.');
				};

				xhr.send();
			}

			function getLocationFromBrowser()
			{
				navigator.geolocation.getCurrentPosition(function(position) {
					let latitude = position.coords.latitude;
					let longitude = position.coords.longitude;

					const xhr = new XMLHttpRequest();
					xhr.open('POST', {link :Api:Offerista:resolveUserLocationFromPosition, websiteId: $presenter->website->getId()}, true);
					xhr.setRequestHeader('Content-Type', 'application/json');

					xhr.onload = function() {
						if (xhr.status === 200) {
							const response = JSON.parse(xhr.responseText);
							setUserCityId(response.cityId);

							return response;
						} else {
							console.error('Error:', xhr.status, xhr.statusText);
						}
					};

					xhr.onerror = function() {
						console.error('Request failed.');
					};

					xhr.send(JSON.stringify({ latitude: latitude, longitude: longitude }));
				}, function(error) {
					alert({_kaufino.cityPicker.error});

					userLocation = JSON.parse(decodeURIComponent(getCookie('userLocation')));
					setUserCityId(userLocation.cityId);
				});
			}

			function setUserCityId(cityId)
			{
				cityPicker.value = cityId
				niceSelect.update();
			}

			let userLocation;
			if (checkCookie('userLocation') === false) {
				userLocation = resolveCityIdFromUserIp();
			} else {
				userLocation = JSON.parse(decodeURIComponent(getCookie('userLocation')));
				setUserCityId(userLocation.cityId);
			}
		</script>
	{/if}
	{/block}
</body>
</html>
<?php

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms\ContentBlocksControl;

use <PERSON><PERSON><PERSON>\Model\ChatGPT\ChatGPTClient;
use <PERSON><PERSON>ino\Model\Shops\ContentBlockFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlock;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlockType;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Kaufino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Users\Entities\User;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Http\Request;
use Nette\InvalidArgumentException;
use Nette\Utils\ArrayHash;

class ContentBlocksControl extends Control
{
	public array $onSuccess = [];
	private ?Shop $shop;
	private ?Tag $tag;
	private ContentBlockFacade $contentBlockFacade;
	private string $websiteType;
	private User $user;
	private ChatGPTClient $chatGPTClient;
	private Request $httpRequest;
	private string $entity;

	public function __construct(
		?Shop $shop,
		?Tag $tag,
		User $user,
		string $websiteType,
		string $entity,
		ContentBlockFacade $contentBlockFacade,
		ChatGPTClient $chatGPTClient,
		Request $httpRequest
	) {
		$this->shop = $shop;
		$this->tag = $tag;
		$this->contentBlockFacade = $contentBlockFacade;
		$this->websiteType = $websiteType;
		$this->user = $user;
		$this->chatGPTClient = $chatGPTClient;
		$this->httpRequest = $httpRequest;
		$this->entity = $entity;
	}

	public function render()
	{
		$this->template->conttentBlockTypes = $this->contentBlockFacade->findContentBlockTypesByWebsiteType($this->websiteType, $this->entity);

		if ($this->shop) {
			$this->template->archivedContentBlocks = $this->contentBlockFacade->findArchivedContentBlocksByShop($this->shop, $this->websiteType);
		} else {
			$this->template->archivedContentBlocks = $this->contentBlockFacade->findArchivedContentBlocksByTag($this->tag, $this->websiteType);
		}

		$this->template->websiteType = $this->websiteType;
		$this->template->setFile(__DIR__ . '/contentBlocksControl.latte');
		$this->template->render();
	}

	public function handleAskChatGpt()
	{
		$prompt = $this->httpRequest->getPost('prompt');

		if ($prompt === null) {
			$this->getPresenter()->sendJson(['data' => '']);
		} else {
			$prompt = str_replace(
				['%language%', '%shop%', '%keywords%'],
				[$this->shop->getLocalization()->getFullLocale(), $this->shop->getName(), ''], // @todo
				$prompt
			);

			$this->getPresenter()->sendJson(['data' => $this->chatGPTClient->getCompletion($prompt)]);
		}
	}

	public function createComponentContentBlockForm(): Form
	{
		$form = new Form();

		$contentBlocksContainer = $form->addContainer('contentBlocks');

		if ($this->tag) {
			$shopContentBlocks = $this->contentBlockFacade->findContentBlocksByTag($this->tag, $this->websiteType);
		} else {
			$shopContentBlocks = $this->contentBlockFacade->findContentBlocksByShop($this->shop, $this->websiteType);
		}

		/** @var ContentBlockType $contentBlockType */
		foreach ($this->contentBlockFacade->findContentBlockTypesByWebsiteType($this->websiteType, $this->entity) as $contentBlockType) {
			$contentBlock = null;

			if (isset($shopContentBlocks[$contentBlockType->getType()])) {
				$contentBlock = $shopContentBlocks[$contentBlockType->getType()];
			}

			$contentBlockContainer = $contentBlocksContainer->addContainer($contentBlockType->getType());

			$contentBlockContainer->addText('heading', 'Heading');

			$contentBlockContainer->addTextArea('content', 'Content')
				->setRequired($contentBlockType->isRequired());

			$prompt = $contentBlockType->getDefaultPrompt();

			$contentBlockContainer->addTextArea('defaultPrompt', 'Default content')
				->setDefaultValue($prompt);

			$contentBlockContainer->addCheckbox('generatedByAi')
				->setDefaultValue($this->websiteType === 'oferto_com');

			$contentBlockContainer->addHidden('id', 0);

			/** @var ContentBlock|null $contentBlock */
			if ($contentBlock) {
				$contentBlockContainer->setDefaults([
					'heading' => $contentBlock->getHeading(),
					'content' => $contentBlock->getContent(),
					'id' => $contentBlock->getId(),
					'generatedByAi' => $contentBlock->isGeneratedByAi(),
				]);
			}
		}

		$form->addSubmit('submit', 'Save all');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values)
	{
		try {
			foreach ($values->contentBlocks as $type => $contentValues) {
				/** @var ContentBlockType $contentBlockType */
				$contentBlockType = $this->contentBlockFacade->findContentBlockType($this->websiteType, $type, $this->entity);

				if ((int) $contentValues->id === 0) {
					$this->contentBlockFacade->createContentBlock(
						$contentBlockType,
						$this->shop,
						$this->tag,
						$this->user,
						$contentValues->heading,
						$contentValues->content,
						$contentValues->generatedByAi
					);
				} else {
					$contentBlock = $this->contentBlockFacade->find($contentValues->id);

					if (
						$contentValues->content !== $contentBlock->getContent() ||
						$contentValues->heading !== $contentBlock->getHeading() ||
						$contentValues->generatedByAi !== $contentBlock->isGeneratedByAi()
					) {
						$contentBlock->setContent($contentValues->content);
						$contentBlock->setHeading($contentValues->heading);
						$contentBlock->update($this->user);

						$contentBlock->setGeneratedByAi($contentValues->generatedByAi);

						$this->contentBlockFacade->saveContentBlock($contentBlock);
					}
				}

				if ($contentValues->defaultPrompt !== $contentBlockType->getDefaultPrompt()) {
					$contentBlockType->setDefaultPrompt($contentValues->defaultPrompt);

					$this->contentBlockFacade->saveContentBlockType($contentBlockType);
				}
			}

			$this->onSuccess();
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function handleArchive($contentBlockTypeId)
	{
		$contentBlockType = $this->contentBlockFacade->findContentBlockTypeById($contentBlockTypeId);

		if ($this->shop) {
			$contentBlock = $this->contentBlockFacade->findContentBlockByTypeForShop($this->shop, $contentBlockType);
		} else {
			$contentBlock = $this->contentBlockFacade->findContentBlockByTypeForTag($this->tag, $contentBlockType);
		}

		if ($contentBlock->getContent() === null) {
			$this->redirect('this');
		}

		$contentBlock->archive($this->user);

		$this->contentBlockFacade->saveContentBlock($contentBlock);

		$this->contentBlockFacade->createContentBlock(
			$contentBlockType,
			$this->shop,
			$this->tag,
			$this->user,
			null,
			null
		);

		$this->redirect('this');
	}
}

interface IContentBlockControlFactory
{
	public function create(?Shop $shop, ?Tag $tag, User $user, string $websiteType, string $entity): ContentBlocksControl;
}

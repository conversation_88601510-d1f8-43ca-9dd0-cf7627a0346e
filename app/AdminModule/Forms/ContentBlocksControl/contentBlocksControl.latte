{form contentBlockForm}
    {formContainer contentBlocks}
        {foreach $conttentBlockTypes as $contentBlockType}
            {var $type = $contentBlockType->getType()}

            {formContainer $type}
                <div class="card">
                    <div class="card-header">
                        <span class="badge bg-danger" n:if="$contentBlockType->isRequired()">Required</span>
                        <strong>{$contentBlockType->getName()}</strong>
                        <a n:href="archive! $contentBlockType->getId()" onclick="return confirm('Are you sure?')" class="pull-right btn btn-sm btn-outline-danger">
                            Archive
                        </a>
                    </div>

                    <div class="card-body">
                        <div class="alert alert-info" role="alert" n:if="$contentBlockType->getInstructions()">
                            {$contentBlockType->getInstructions()}
                        </div>

                        {if isset($archivedContentBlocks[$type])}
                            <div class="alert alert-warning p-1" role="alert" n:foreach="$archivedContentBlocks[$type] as $block">
                                <div class="badge bg-warning mb-2">Archived content</div>
                                <div>
                                    <textarea class="redactor-disabled" contenteditable="false" style="margin-top: 10px;">{$block->getContent()}</textarea>
                                </div>
                            </div>
                        {/if}

                        <div class="mb-3" n:if="$contentBlockType->getContentType() === 'html'">
                            {label heading, class: "form-label"}
                            {input heading, class: "form-control"}
                            {input id}
                        </div>

                        <div class="mb-3" style="width: 100%;">
                            <div style="width: 100%;">
                                {input content, class: $contentBlockType->getContentType() === 'html' ? 'redactor' : 'form-control', id => 'content-' . $contentBlockType->getId()}
                            </div>
                            <div style="display: none; margin-top: 8px;">
                                <label>Default prompt:</label>
                                {input defaultPrompt, class: "form-control", style: "width: 100%; min-height: 50px", data-prompt-id => $contentBlockType->getId()}

                                <div style="display: flex; gap: 5px; margin-top: 10px">
                                    <div>
                                        <a href="javascript:askChatGpt({$contentBlockType->getId()});" data-chatgpt-button-id="{$contentBlockType->getId()}" class="btn btn-primary">
                                            Generate via ChatGPT
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label style="margin-top: 8px">
                                    {input generatedByAi}
                                    <span>Generated by ChatGPT</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            {/formContainer}
        {/foreach}
    {/formContainer}

    <div style="padding-bottom: 20px">
        {input submit, class: "btn btn-primary"}
    </div>
{/form}
<script>
    var processing = [];
    function askChatGpt(id) {
        if (processing[id] === true) {
            return; // don't run if already processing
        }
        processing[id] = true;

        // find button and change text + add spinner
        var buttonObj = $("[data-chatgpt-button-id="+id+"]").html('Generating.. <div class="loading spinner-grow spinner-grow-sm"></div>').addClass("btn-secondary");

        $("[data-prompt-id="+id+"]").attr("disabled", true); // disable prompt for editing

        $.ajax({
            url: {link askChatGpt!},
            data: { prompt: $("[data-prompt-id="+id+"]").val() }, // get prompt from textarea
            method: "POST",
            success: function(response) {
                $R('#content-' + id, 'source.setCode', response.data); // set generated AI content to textarea (redactor)
                buttonObj.text("Generate via ChatGPT").removeClass("btn-secondary"); // reset button
                $("[data-prompt-id="+id+"]").removeAttr("disabled"); // enable prompt for editing
                processing[id] = false;
            }
        });
    }
</script>
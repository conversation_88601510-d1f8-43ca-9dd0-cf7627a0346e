<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms;

use <PERSON><PERSON><PERSON>\Model\Images\ImageStorage;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON>ino\Model\Tags\TagFacade;
use Ka<PERSON>ino\Model\Tools;
use Kaufino\Model\Users\UserIdentity;
use Nette;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;
use Nette\Utils\Strings;
use Ka<PERSON>ino\Model\Offers\Entities\Offer;
use <PERSON><PERSON>ino\Model\Offers\OfferFacade;
use Tracy\Debugger;

class OfferControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var ?Offer */
	private $offer;

	/** @var OfferFacade */
	private $offerFacade;

	/** @var ImageStorage */
	private $imageStorage;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var TagFacade */
	private $tagFacade;

	/**
	 * @var UserIdentity
	 */
	private $userIdentity;

	public function __construct(Offer $offer = null, OfferFacade $offerFacade, ImageStorage $imageStorage, LocalizationFacade $localizationFacade, ShopFacade $shopFacade, TagFacade $tagFacade, UserIdentity $userIdentity)
	{
		$this->offer = $offer;
		$this->offerFacade = $offerFacade;
		$this->imageStorage = $imageStorage;
		$this->localizationFacade = $localizationFacade;
		$this->shopFacade = $shopFacade;
		$this->tagFacade = $tagFacade;
		$this->userIdentity = $userIdentity;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addText('name', 'Name:')
			->setRequired('Enter a name.');

		if ($this->offer) {
			$form->addText('slug', 'Slug:')
				->setDisabled();
		}

		$form->addSelect('shop', 'Shop:', $this->shopFacade->findPairs())
			->setPrompt('Select a shop.')
			->setRequired('Select a shop.');

		$form->addSelect('type', 'Type:', Offer::getTypes())
			->setPrompt('Select a type.')
			->setRequired('Select a type.');

		$form->addSelect('discountType', 'Discount type:', Offer::getDiscountTypes())
			->setPrompt('Select a type.')
			->setRequired('Select a type.');

		if ($this->offer) {
			$form->addMultiSelect('tags', 'Tags:', $this->tagFacade->findPairs($this->offer->getLocalization(), Tag::TYPE_OFFERS))
				->setRequired('Select a tag.');
		}

		$form->addUpload('image', 'Image (Preferably PNG):')
			->addCondition(Form::FILLED)
			->addRule(Form::IMAGE, 'Image must be in JPEG, PNG or GIF');

		$form->addInteger('discountAmount', 'Discount amount:')
			->setRequired('Enter a discount amount.');

		$form->addInteger('currentPrice', 'Price:')
			->addConditionOn($form['type'], $form::EQUAL, Offer::TYPE_LEAFLET)
				->setRequired('Enter a price.');

		$form->addInteger('commonPrice', 'Common price:');

		$form->addText('code', 'Code:')
			->setRequired('Enter a code.');

		$form->addText('exitUrl', 'Exit url:');

		$form->addText('validSince', 'Valid since:')
			->setRequired('Enter a valid since.');

		$form->addText('validTill', 'Valid till:')
			->setRequired('Enter a valid till.');

		$form->addTextArea('description', 'Description:', null, 10)
			->setHtmlAttribute('class', 'redactor')
			->setRequired(false);

		$form->addSelect('priorityLevel', 'Priority level:', Offer::getPriorityLevels())
			->setPrompt('Select a priority level.')
			->setRequired('Select a priority level.');

		$form->addInteger('priority', 'Priority:')
			->setDefaultValue(0)
			->setRequired(false);

		$form->addSubmit('submit', 'Save');
		$form->addSubmit('submitAndContinue', 'Save and continue editing');

		if ($this->offer) {
			$form->setDefaults(
				[
					'localization' => $this->offer->getLocalization()->getId(),
					'name' => $this->offer->getName(),
					'slug' => $this->offer->getSlug(),
					'type' => $this->offer->getType(),
					'shop' => $this->offer->getShop()->getId(),
					'discountType' => $this->offer->getDiscountType(),
					'discountAmount' => $this->offer->getDiscountAmount(),
					'currentPrice' => $this->offer->getCurrentPrice(),
					'commonPrice' => $this->offer->getCommonPrice(),
					'code' => $this->offer->getCode(),
					'exitUrl' => $this->offer->getExitUrl(),
					'validSince' => $this->offer->getValidSince()->format('Y-m-d H:i:s'),
					'validTill' => $this->offer->getValidTill()->format('Y-m-d H:i:s'),
					'priorityLevel' => $this->offer->getPriorityLevel(),
					'description' => $this->offer->getDescription(),
					'priority' => $this->offer->getPriority(),
					//'tag' => $this->offer->getTag() ? $this->offer->getTag()->getId() : null
				]
			);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		$validSince = \DateTime::createFromFormat('Y-m-d H:i:s', $values->validSince);
		$validTill = \DateTime::createFromFormat('Y-m-d H:i:s', $values->validTill);
		$shop = $this->shopFacade->findShop($values->shop);
		$localization = $shop->getLocalization();

		try {
			if ($this->offer) {
				$offer = $this->offer;
				$offer->setName($values->name);

				$offer->clearTags();
				foreach ($values->tags as $tagId) {
					$tag = $this->tagFacade->findTag($tagId);
					$offer->addTag($tag);
				}
			} else {
				$slug = Strings::webalize($values->name);

				if (isset($values->slug) && $this->offerFacade->findOfferBySlug($localization, $slug)) {
					throw new InvalidArgumentException('This slug is already used at another offer.');
				}

				$offer = $this->offerFacade->createOffer($localization, $values->name, $slug, $shop, $values->type, $values->discountType, $validSince, $validTill);
			}

			$offer->setType($values->type);
			$offer->setDescription($values->description);
			$offer->setDiscountAmount($values->discountAmount);
			$offer->setCommonPrice($values->commonPrice);
			$offer->setCurrentPrice($values->currentPrice);
			$offer->setCode($values->code);
			$offer->setExitUrl($values->exitUrl);
			$offer->setPriorityLevel($values->priorityLevel);
			$offer->setAuthor($this->userIdentity->getIdentity());

			$offer->setPriorityLevel($values->priorityLevel);
			$offer->setPriority($values->priority);

			$this->offerFacade->saveOffer($offer);

			/** @var Nette\Http\FileUpload $image */
			$image = $values->image;

			if ($image->isOk() && $image->isImage()) {
				$this->offerFacade->saveOfferImage($offer, $image->toImage(), Tools::getImageTypeFromFileUpload($image));
			}

//			if (isset($oldLogo)) {
//				$this->imageStorage->deleteImage($oldLogo);
//			}

			$this->onSuccess($offer, $form['submitAndContinue']->isSubmittedBy());
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}


interface IOfferControlFactory
{
	public function create(Offer $offer = null): OfferControl;
}

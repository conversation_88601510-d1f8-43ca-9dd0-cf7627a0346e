<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use <PERSON><PERSON><PERSON>\Model\Images\ImageStorage;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Tags\TagFacade;
use Nette;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;
use Nette\Utils\Strings;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ShopFacade;

class ShopControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var ?Shop */
	private $shop;

	/** @var ShopFacade */
	private $shopFacade;

	/** @var ImageStorage */
	private $imageStorage;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var TagFacade */
	private $tagFacade;

	public function __construct(Shop $shop = null, ShopFacade $shopFacade, ImageStorage $imageStorage, LocalizationFacade $localizationFacade, TagFacade $tagFacade)
	{
		$this->shop = $shop;
		$this->shopFacade = $shopFacade;
		$this->imageStorage = $imageStorage;
		$this->localizationFacade = $localizationFacade;
		$this->tagFacade = $tagFacade;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addText('name', 'Name:')
			->setRequired('Enter a name.');

		$fieldSlug = $form->addText('slug', 'Slug:')
			->setRequired('Enter a slug.');

		$fieldLocalization = $form->addSelect('localization', 'Localization:', $this->localizationFacade->findPairs())
			->setPrompt('-- select a localization --')
			->setRequired('Select a localization.');

		$form->addSelect('type', 'Type:', Shop::getTypes())
			->setPrompt('Select a type.')
			->setRequired('Select a type.');

		if ($this->shop) {
			$tags = $this->tagFacade->findPairs($this->shop->getLocalization(), Tag::TYPE_SHOPS);
			$form->addSelect('tag', 'Tags:', $tags)
				->setPrompt('Select a tag.')
				->setRequired('Select a tag.');

			$currentTagId = $this->shop->getTag() ? $this->shop->getTag()->getId() : null;

			$form->addMultiSelect('tags', 'Other tags:', array_diff_key($tags, [$currentTagId => null]))
				->setHtmlAttribute('class', 'multiselect')
				->setRequired(false);

			$form->addMultiSelect('labels', 'Labels:', $this->tagFacade->findPairs($this->shop->getLocalization(), Tag::TYPE_LABELS))
				->setHtmlAttribute('class', 'multiselect')
			;
		}

		if ($this->shop) {
			$fieldSlug->setDisabled();
			$fieldLocalization->setDisabled();
		}

		$form->addUpload('logo', 'Logo (Preferably PNG):')
			->addCondition(Form::FILLED)
			->addRule(Form::IMAGE, 'Logo must be in JPEG, PNG or GIF');

		$form->addText('domain', 'Domain:')
			->setRequired(false)
			->addRule($form::MAX_LENGTH, 'Domain can be at most %d characters.', 255);

		$form->addTextArea('alternativeNames', 'Alternative names:', null, 5)
			->setRequired(false);

		$form->addCheckbox('useOcr', 'Use OCR:')
			->setDefaultValue(0)
			->setRequired(false);

		$form->addInteger('priorityLeaflets', 'Priority - leaflets (search volume):')
			->setDefaultValue(0)
			->setRequired(false);

		$form->addInteger('priorityCoupons', 'Priority - coupons (search volume):')
			->setDefaultValue(0)
			->setRequired(false);

		$form->addInteger('boost', 'Boost:')
			->setDefaultValue(0)
			->setRequired(false);

		$form->addInteger('boostCoupons', 'Boost coupons - mroferto.com:')
			->setDefaultValue(0)
			->setRequired(false);

		$form->addText('shopId', 'Steve Shop ID')
			->setDefaultValue(null)
			->addCondition($form::FILLED)
			->addRule($form::INTEGER, 'Steve Shop ID Musí být číslo')
			->endCondition()
			->setRequired(false);

		/*
		$form->addCheckbox('activeLeaflets', 'Active - leaflets (do not change!):')
			->setHtmlAttribute('class', 'shop-checkbox')
			->setRequired(false);

		$form->addCheckbox('activeCoupons', 'Active - coupons (do not change!):')
			->setHtmlAttribute('class', 'shop-checkbox')
			->setRequired(false);
		*/

		$form->addCheckbox('activeKaufino', 'Active on Kaufino websites (do not change!):')
			->setHtmlAttribute('class', 'shop-checkbox')
			->setRequired(false);

		$form->addCheckbox('activeOferto', 'Active on MrOferto websites (do not change!):')
			->setHtmlAttribute('class', 'shop-checkbox')
			->setRequired(false);

		$form->addCheckbox('activeLetado', 'Active on Letado websites (do not change!):')
			->setHtmlAttribute('class', 'shop-checkbox')
			->setRequired(false);

		$form->addCheckbox('activeOfertoCom', 'Active on OfertoCom websites (do not change!):')
			->setHtmlAttribute('class', 'shop-checkbox')
			->setRequired(false);

		$form->addCheckbox('useLeafletTemplateForNewsletters', 'Use leaflet template for newsletters:')
			->setHtmlAttribute('class', 'shop-checkbox')
			->setRequired(false);

		$form->addCheckbox('activeCities', 'Active cities')
			->setHtmlAttribute('class', 'shop-checkbox')
			->setRequired(false);

		$form->addSubmit('submit', 'Save');
		$form->addSubmit('submitAndContinue', 'Save and continue editing');

		if ($this->shop) {
			$addedLabels = [];
			if ($labels = $this->shop->getLabels()) {
				foreach ($labels as $label) {
					$addedLabels[] = $label->getId();
				}
			}

			$addedTags = [];
			if ($otherTags = $this->shop->getTags()) {
				foreach ($otherTags as $tagItem) {
					$addedTags[] = $tagItem->getId();
				}
			}

			$form->setDefaults(
				[
					'localization' => $this->shop->getLocalization()->getId(),
					'name' => $this->shop->getName(),
					'slug' => $this->shop->getSlug(),
					'type' => $this->shop->getType(),
					'domain' => $this->shop->getDomain(),
					'alternativeNames' => $this->shop->getAlternativeNames(),
					'useOcr' => $this->shop->isUseOcr(),
					'priorityLeaflets' => $this->shop->getPriorityLeaflets() ?: 0,
					'priorityCoupons' => $this->shop->getPriorityCoupons() ?: 0,
					'boost' => $this->shop->getBoost() ?: 0,
					'boostCoupons' => $this->shop->getBoostCoupons() ?: 0,
					'activeKaufino' => $this->shop->isActiveKaufino(),
					'activeLetado' => $this->shop->isActiveLetado(),
				//					'activeCoupons' => $this->shop->isActiveCoupons(),
					'activeOferto' => $this->shop->isActiveOferto(),
					'activeOfertoCom' => $this->shop->isActiveOfertoCom(),
					'tag' => $this->shop->getTag() && array_key_exists($this->shop->getTag()->getId(), $tags) ? $this->shop->getTag()->getId() : null,
					'labels' => $addedLabels,
					'tags' => $addedTags,
					'shopId' => $this->shop->getShopId(),
					'useLeafletTemplateForNewsletters' => $this->shop->useLeafletTemplateForNewsletters(),
					'activeCities' => $this->shop->hasActiveCities(),
				]
			);
		}

		if (is_int($form['shopId']->getValue())) {
			$form['shopId']->setHtmlAttribute('readonly', 'true');
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if ($this->shop) {
				$shop = $this->shop;
				$shop->setName($values->name);

				$tag = $this->tagFacade->findTag($values->tag);
				$shop->setTag($tag);

				$addedLabels = [];

				foreach ($values->labels as $labelId) {
					$label = $this->tagFacade->findTag($labelId);
					$shop->addLabel($label);

					$addedLabels[] = $label;
				}

				foreach ($shop->getLabels() as $label) {
					if (!in_array($label, $addedLabels)) {
						$shop->removeLabel($label);
					}
				}

				$addedTags = [];
				foreach ($values->tags as $tagId) {
					$tag = $this->tagFacade->findTag($tagId);

					if ($tag === $shop->getTag()) {
						continue;
					}

					$shop->addTag($tag);

					$addedTags[] = $tag;
				}

				foreach ($shop->getTags() as $tag) {
					if (!in_array($tag, $addedTags)) {
						$shop->removeTag($tag);
					}
				}
			} else {
				$localization = isset($values->localization) ? $this->localizationFacade->findLocalization($values->localization) : null;
				$slug = Strings::webalize($values->slug);

				if (!$localization) {
					throw new InvalidArgumentException('Select a localization, please.');
				}

				if (isset($values->slug) && $this->shopFacade->findShopBySlug($localization, $slug)) {
					throw new InvalidArgumentException('This slug is already used at another shop.');
				}

				$shop = $this->shopFacade->createShop($localization, $values->name, $slug);
			}

			$shop->setDomain($values->domain);
			$shop->setType($values->type);
			$shop->setAlternativeNames($values->alternativeNames);
			$shop->setUseOcr($values->useOcr);
			$shop->setPriorityCoupons($values->priorityCoupons);
			$shop->setPriorityLeaflets($values->priorityLeaflets);
			$shop->setBoost($values->boost);
			$shop->setBoostCoupons($values->boostCoupons);
			$shop->setActiveKaufino($values->activeKaufino);
			$shop->setActiveOfertoCom($values->activeOfertoCom);
			$shop->setActiveLetado($values->activeLetado);
			$shop->setActiveCities($values->activeCities);
			// $shop->setActiveCoupons($values->activeCoupons);

			$shop->setActiveOferto($values->activeOferto);

			if ($shop->getShopId() === null && empty($values->shopId) === false) {
				$shop->setShopId($values->shopId);
			}

			if ($values->useLeafletTemplateForNewsletters) {
				$shop->setUseLeafletTemplateForNewsletters();
			} else {
				$shop->unsetUseLeafletTemplateForNewsletters();
			}

			$this->shopFacade->saveShop($shop);

			/** @var Nette\Http\FileUpload $logo */
			$logo = $values->logo;

			if ($logo->isOk()) {
				$oldLogo = $shop->getLogoUrl();
				$newLogo = $this->imageStorage->saveImage($values->logo, ImageStorage::NAMESPACE_SHOP_LOGO, rand(500, 1000), $shop->getSlug());
				$shop->setLogoUrl($newLogo);

				if ($oldLogo != $newLogo) {
					$this->imageStorage->removeImage($oldLogo);
				}
			}

			$this->shopFacade->saveShop($shop);

//			if (isset($oldLogo)) {
//				$this->imageStorage->deleteImage($oldLogo);
//			}

			$this->onSuccess($shop, $form['submitAndContinue']->isSubmittedBy());
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		} catch (UniqueConstraintViolationException $e) {
			$form->addError('Zadané Shop ID je již přiřazené pro jiný obchod');
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}


interface IShopControlFactory
{
	public function create(Shop $shop = null): ShopControl;
}

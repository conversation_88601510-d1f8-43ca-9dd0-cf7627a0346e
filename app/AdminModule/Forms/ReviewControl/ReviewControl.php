<?php

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms\ReviewControl;

use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Shops\ReviewFacade;
use <PERSON><PERSON>ino\Model\Users\Entities\User;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;

class ReviewControl extends Control
{
	public array $onSuccess = [];

	private Shop $shop;
	private User $user;
	private ReviewFacade $reviewFacade;

	public function __construct(Shop $shop, ?User $user, ReviewFacade $reviewFacade)
	{
		$this->shop = $shop;
		$this->user = $user;
		$this->reviewFacade = $reviewFacade;
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		$form->addSelect('rate', 'Hodnocení', [
			5 => 5, 4 => 4, 3 => 3, 2 => 2, 1 => 1,
		]);

		$form->addSubmit('submit');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values)
	{
		$this->reviewFacade->createReview($this->shop, $values->rate, $this->user);

		$this->onSuccess();
	}
}

interface IReviewControlFactory
{
	public function create(Shop $shop, ?User $user): ReviewControl;
}

{form form}
<div class="container">
    <div class="row justify-content-center">
        <div>
            <div class="card border-light">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <h5 class="card-title">
                            Edit tag: {$tag ? $tag->getName() : 'Create new tag'}
                        </h5>

                        <div n:if="$tag">
                            {foreach Kaufino\Model\Websites\Entities\Website::getModules() as $websiteType}
                                <a href="{plink :Admin:Tag:contentBlocks, id: $tag->getId(), entity: 'tag', websiteType: $websiteType}" class="btn btn-primary btn-sm" role="button">Content {$websiteType |firstUpper}</a>
                            {/foreach}
                        </div>
                    </div>

                    <div class="mb-3">
                        {label name, class: "form-label"}
                        {input name, class: "form-control"}
                    </div>
                    <div class="mb-3">
                        {label slug, class: "form-label"}
                        {input slug, class: "form-control"}
                    </div>
                    <div class="mb-3">
                        {label localization, class: "form-label"}
                        {input localization, class: "form-control"}
                    </div>
                    <div class="mb-3" n:ifset="$form['parentTag']">
                        {label parentTag, class: "form-label"}
                        {input parentTag, class: "form-control"}
                    </div>
                    <div class="mb-3">
                        {label type, class: "form-label"}
                        {input type, class: "form-control"}
                    </div>
                    <div class="mb-3">
                        {label description, class: "form-label"}
                        {input description}
                    </div>
                    <div class="form-check">
                        {input active:input, class: "form-check-input"}
                        <label class="form-check-label" for="frm-tagControl-form-active">
                            Active?
                        </label>
                    </div>
                    <div class="form-check">
                        {input activeKaufino:input, class: "form-check-input"}
                        <label class="form-check-label" for="frm-tagControl-form-activeKaufino">
                            Active Kaufino
                        </label>
                    </div>
                    <div class="form-check">
                        {input activeLetado:input, class: "form-check-input"}
                        <label class="form-check-label" for="frm-tagControl-form-activeLetado">
                            Active Letado
                        </label>
                    </div>
                    <div class="form-check">
                        {input activeOferto:input, class: "form-check-input"}
                        <label class="form-check-label" for="frm-tagControl-form-activeOferto">
                            Active Oferto
                        </label>
                    </div>
                    <div class="" id="activeTillContainer">
                        <div class="form-check" n:ifset="$form['showInMenu']">
                            {input showInMenu:input, class: "form-check-input"}
                            <label class="form-check-label" for="frm-tagControl-form-showInMenu">
                                Show in menu?
                            </label>
                        </div>

                        <div>
                            {label activeTill, class: "form-label"}
                            {input activeTill, class: "form-control", type: 'date'}
                        </div>
                    </div>
                    <div class="mt-3 mb-3">
                        {label englishName, class: "form-label"}
                        {input englishName, class: "form-control"}
                    </div>
                    <div class="mb-3">
                        {label matchRule, class: "form-label"}
                        {input matchRule, class: "form-control"}
                    </div>
                    <div class="mb-3">
                        {label priority, class: "form-label"}
                        {input priority, class: "form-control"}
                    </div>

                    <div class="d-grid gap-2 d-md-flex">
                        {input submit, class: "btn btn-primary"}
                        {input submitAndContinue, class: "btn btn-primary"}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/form}
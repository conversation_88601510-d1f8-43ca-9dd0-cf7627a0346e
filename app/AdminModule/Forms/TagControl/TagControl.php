<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms\TagControl;

use <PERSON><PERSON><PERSON>\Model\Images\ImageStorage;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON><PERSON><PERSON>\Model\Tools;
use Nette;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;
use Nette\Utils\Strings;
use Kaufino\Model\Tags\Entities\Tag;

class TagControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var ?Tag */
	private $tag;

	/** @var TagFacade */
	private $tagFacade;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var ImageStorage  */
	private $imageStorage;

	public function __construct(?Tag $tag, TagFacade $tagFacade, LocalizationFacade $localizationFacade, ImageStorage $imageStorage)
	{
		$this->tag = $tag;
		$this->tagFacade = $tagFacade;
		$this->localizationFacade = $localizationFacade;
		$this->imageStorage = $imageStorage;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addText('name', 'Name:')
			->setRequired('Enter a name.');

		$fieldSlug = $form->addText('slug', 'Slug:')
			->setRequired('Enter a slug.');

		$fieldLocalization = $form->addSelect('localization', 'Localization:', $this->localizationFacade->findPairs())
			->setPrompt('-- select a localization --')
			->setRequired('Select a localization.');

		if ($this->tag) {
			$form->addSelect('parentTag', 'Parent tag:', $this->tagFacade->findPairs($this->tag->getLocalization(), $this->tag->getType()))
				->setPrompt('Select a tag.');
		}

		$fieldType = $form->addSelect('type', 'Type:', Tag::getTypes())
			->setPrompt('Select a type.')
			->setRequired('Select a type.');

		if ($this->tag) {
			$fieldSlug->setDisabled();
			$fieldLocalization->setDisabled();
			$fieldType->setDisabled();
		}

		if ($this->tag && $this->tag->isOffersType()) {
			$form->addUpload('productImage', 'Product LP image')
				->addCondition(Form::FILLED)
				->addRule(Form::IMAGE, 'Image must be in JPEG, PNG or GIF');
		}

		$form->addTextArea('description', 'Description:', null, 10)
			->setHtmlAttribute('class', 'redactor')
			->setRequired(false);

		$form->addText('englishName', 'English name:')
			->setRequired(false);

		$form->addText('matchRule', 'Match rule:')
			->setRequired(false);

		$form->addInteger('priority', 'Priority:')
			->setDefaultValue(0)
			->setRequired(false);

		$form->addCheckbox('active', 'Active:')
			->setRequired(false)
			->addCondition($form::EQUAL, true)
			->toggle('activeTillContainer')
		;

		$form->addCheckbox('activeKaufino', 'Active Kaufino:')
			->setRequired(false)
			->addCondition($form::EQUAL, true)
		;

		$form->addCheckbox('activeLetado', 'Active Letado:')
			->setRequired(false)
			->addCondition($form::EQUAL, true)
		;

		$form->addCheckbox('activeOferto', 'Active Oferto:')
			->setRequired(false)
			->addCondition($form::EQUAL, true)
		;

		if ($this->tag === null || $this->tag->isShopsType()) {
			$form->addCheckbox('showInMenu', 'Show in menu:')
				->setRequired(false)
				->addCondition($form::EQUAL, true)
				->toggle('activeTillContainer')
			;
		}

		$form->addText('activeTill', 'Active till (empty = no valid by date)')
			->setHtmlType('datetime')
		;

		$form->addSubmit('submit', 'Save');
		$form->addSubmit('submitAndContinue', 'Save and continue editing');

		//Debugger::dump($this->tag);

		if ($this->tag) {
			$form->setDefaults(
				[
					'localization' => $this->tag->getLocalization()->getId(),
					'name' => $this->tag->getName(),
					'slug' => $this->tag->getSlug(),
					'type' => $this->tag->getType(),
					'description' => $this->tag->getDescription(),
					'englishName' => $this->tag->getEnglishName(),
					'matchRule' => $this->tag->getMatchRule(),
					'priority' => $this->tag->getPriority(),
					'active' => $this->tag->isActive(),
					'activeKaufino' => $this->tag->isActiveKaufino(),
					'activeLetado' => $this->tag->isActiveLetado(),
					'activeOferto' => $this->tag->isActiveOferto(),
					'parentTag' => $this->tag->getParentTag() ? $this->tag->getParentTag()->getId() : null,
					'activeTill' => $this->tag->getActiveTill() ? $this->tag->getActiveTill()->format('Y-m-d') : null,
					'showInMenu' => $this->tag->isShowInMenu(),
				]
			);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if ($this->tag) {
				$tag = $this->tag;
				$tag->setName($values->name);
			} else {
				$localization = isset($values->localization) ? $this->localizationFacade->findLocalization($values->localization) : null;
				$slug = Strings::webalize($values->slug);

				if (!$localization) {
					throw new InvalidArgumentException('Select a localization, please.');
				}

				if (isset($values->slug) && $values->type !== Tag::TYPE_LABELS && $this->tagFacade->findTagBySlug($localization, $slug, Tag::TYPE_OFFERS, Tag::TYPE_SHOPS, Tag::TYPE_MANUFACTURER, Tag::TYPE_REGION, Tag::TYPE_PARENT_PRODUCT)) {
					throw new InvalidArgumentException('This slug is already used at another tag.');
				}

				if (isset($values->slug) && $values->type === Tag::TYPE_LABELS && $this->tagFacade->findTagBySlug($localization, $slug, Tag::TYPE_LABELS)) {
					throw new InvalidArgumentException('This slug is already used at another tag.');
				}

				$tag = $this->tagFacade->createTag($localization, $values->name, $slug);
				$tag->setType($values->type);
			}

			if (isset($values->parentTag)) {
				$parentTag = $this->tagFacade->findTag($values->parentTag);

				if ($parentTag->getType() != $tag->getType()) {
					throw new InvalidArgumentException('Type of tag must be equal to type of parent tag.');
				}

				if ($parentTag->getId() == $tag->getId()) {
					throw new InvalidArgumentException('Tag and parent tag are identical.');
				}

				$tag->setParentTag($parentTag);
			} else {
				$tag->setParentTag(null);
			}

			if (isset($values->productImage) && !empty($values->productImage)) {
				/** @var Nette\Http\FileUpload $image */
				$image = $values->productImage;

				if ($image->isOk() && $image->isImage()) {
					$oldImage = $tag->getProductImageUrl();
					$newImage = $this->imageStorage->saveImagev2($image->toImage(), Tools::getImageTypeFromFileUpload($image), ImageStorage::NAMESPACE_TAG_IMAGE, rand(500, 1000), $tag->getSlug());
					$tag->setProductImageUrl($newImage);

					if ($oldImage != $newImage) {
						$this->imageStorage->removeImage($oldImage);
					}
				}
			}

			$tag->setDescription($values->description);
			$tag->setEnglishName($values->englishName);
			$tag->setMatchRule($values->matchRule);
			$tag->setPriority($values->priority);
			$tag->setActive($values->active);
			$tag->setActiveKaufino($values->activeKaufino);
			$tag->setActiveLetado($values->activeLetado);
			$tag->setActiveOferto($values->activeOferto);
			$tag->setShowInMenu($values->showInMenu ?? false);

			if ($values->activeTill) {
				$tag->setActiveTill(new \DateTime($values->activeTill));
			} else {
				$tag->setActiveTill(null);
			}

			$this->tagFacade->saveTag($tag);

			$this->onSuccess($tag, $form['submitAndContinue']->isSubmittedBy());
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->tag = $this->tag;
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}

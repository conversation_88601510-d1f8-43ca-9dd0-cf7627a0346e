<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms;

use <PERSON><PERSON><PERSON>\Model\Articles\ArticleFacade;
use <PERSON><PERSON>ino\Model\Articles\Entities\Article;
use <PERSON><PERSON>ino\Model\Images\ImageStorage;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use Ka<PERSON>ino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Tags\TagFacade;
use Ka<PERSON>ino\Model\Users\Entities\User;
use Ka<PERSON>ino\Model\Users\UserFacade;
use Ka<PERSON>ino\Model\Websites\WebsiteFacade;
use Ka<PERSON>ino\Model\Tools;
use Nette;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;
use Nette\Utils\Image;
use Nette\Utils\Strings;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Tracy\Debugger;

class ArticleControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var ?Article */
	private $article;

	/** @var ArticleFacade */
	private $articleFacade;

	/** @var ImageStorage */
	private $imageStorage;

	/** @var WebsiteFacade */
	private $websiteFacade;

	/** @var ShopFacade */
	private $shopFacade;
	private UserFacade $userFacade;

	public function __construct(?Article $article, ArticleFacade $articleFacade, ImageStorage $imageStorage, WebsiteFacade $websiteFacade, ShopFacade $shopFacade, UserFacade $userFacade)
	{
		$this->article = $article;
		$this->articleFacade = $articleFacade;
		$this->imageStorage = $imageStorage;
		$this->websiteFacade = $websiteFacade;
		$this->shopFacade = $shopFacade;
		$this->userFacade = $userFacade;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addText('name', 'Name:')
			->setRequired('Enter a name.');

		$fieldSlug = $form->addText('slug', 'Slug:')
			->setRequired('Enter a slug.');

		$fieldWebsite = $form->addSelect('website', 'Website:', $this->websiteFacade->findPairs())
			->setPrompt('-- select a website --')
			->setRequired('Select a website.')
			->setHtmlAttribute('class', 'multiselect')
		;

		$authors = [];

		foreach ($this->userFacade->findAuthors() as $author) {
			$authors[$author->getId()] = $author->getFullName() ?: $author->getEmail();
		}

		$form->addSelect('author', 'Author', $authors)
			->setPrompt('-- select an author --')
		;

		if ($this->article) {
			$fieldSlug->setDisabled();
			$fieldWebsite->setDisabled();
		}

		$form->addUpload('image', 'Image (Preferably JPEG):')
			->addCondition(Form::FILLED)
			->addRule(Form::IMAGE, 'Image must be in JPEG, PNG or GIF');

		if ($this->article) {
			$shops = $this->shopFacade->findPairs($this->article->getWebsite()->getLocalization(), true);

			$addedShops = [];

			foreach ($this->article->getShops() as $shop) {
				$addedShops[] = $shop->getId();
			}

			$form->addMultiSelect('shops', 'Shops:', $shops)
				->setHtmlAttribute('class', 'multiselect')
				->setDefaultValue($addedShops)
			;
		}

		$form->addTextArea('shortDescription', 'Short description:', null, 10)
			->setRequired(false);

		$form->addTextArea('content', 'Content:', null, 10)
			->setHtmlAttribute('class', 'redactor')
			->setRequired(false);

		$form->addCheckbox('active', 'Active:')
			->setRequired(false);

		$form->addSubmit('submit', 'Save');
		$form->addSubmit('submitAndContinue', 'Save and continue editing');

		if ($this->article) {
			$form->setDefaults(
				[
					'website' => $this->article->getWebsite()->getId(),
					'name' => $this->article->getName(),
					'slug' => $this->article->getSlug(),
					'shortDescription' => $this->article->getShortDescription(),
					'content' => $this->article->getContent(),
					'active' => $this->article->isActive(),
					'author' => $this->article->getAuthor() ? $this->article->getAuthor()->getId() : null,
				]
			);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if ($this->article) {
				$article = $this->article;
				$article->setName($values->name);
			} else {
				$website = isset($values->website) ? $this->websiteFacade->findWebsite($values->website) : null;
				$slug = Strings::webalize($values->slug);

				if (!$website) {
					throw new InvalidArgumentException('Select a website, please.');
				}

				if (isset($values->slug) && $this->articleFacade->findArticleBySlug($website, $slug)) {
					throw new InvalidArgumentException('This slug is already used at another article.');
				}

				$article = $this->articleFacade->createArticle($website, $values->name, $slug);
			}

			$article->setShortDescription($values->shortDescription);
			$article->setContent($values->content);
			$article->setActive($values->active);
			$article->setAuthor($values->author ? $this->userFacade->find($values->author) : null);

			$this->articleFacade->saveArticle($article);

			/** @var Nette\Http\FileUpload $image */
			$image = $values->image;

			if ($image->isOk()) {
				$oldImage = $article->getImageUrl();

				$imageObject = $image->toImage();
				$originalWidth = $imageObject->getWidth();
				$originalHeight = $imageObject->getHeight();

				if ($originalWidth > 800 || $originalHeight > 800) {
					if ($originalWidth > $originalHeight) {
						$imageObject->resize(800, null);
					} else {
						$imageObject->resize(null, 800);
					}
				}

				$imageType = Tools::getImageTypeFromFileUpload($image);
				$newImage = $this->imageStorage->saveImagev2($imageObject, $imageType, ImageStorage::NAMESPACE_ARTICLE_IMAGE, rand(500, 1000), $article->getSlug());
				$article->setImageUrl($newImage);

				if ($oldImage != $newImage) {
					$this->imageStorage->removeImage($oldImage);
				}
			}

			if (isset($values->shops)) {
				$article->setShops($this->shopFacade->findByIds($values->shops));
			}

			$this->articleFacade->saveArticle($article);

			$this->onSuccess($article, $form['submitAndContinue']->isSubmittedBy());
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}


interface IArticleControlFactory
{
	public function create(Article $article = null): ArticleControl;
}

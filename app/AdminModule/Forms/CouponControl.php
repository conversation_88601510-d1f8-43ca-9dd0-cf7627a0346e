<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms;

use <PERSON><PERSON><PERSON>\Model\Competitors\CompetitorFacade;
use <PERSON><PERSON><PERSON>\Model\Competitors\Entities\Coupon;
use <PERSON><PERSON><PERSON>\Model\Images\ImageStorage;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use Ka<PERSON>ino\Model\Offers\OfferFacade;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Tags\Entities\Tag;
use <PERSON><PERSON>ino\Model\Tags\TagFacade;
use Ka<PERSON>ino\Model\Tools;
use Ka<PERSON>ino\Model\Users\UserIdentity;
use Nette;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;
use Nette\Utils\Strings;

class CouponControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var ?Coupon */
	private $coupon;

	/** @var CompetitorFacade */
	private $competitorFacade;

	/** @var ImageStorage */
	private $imageStorage;

	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var ShopFacade */
	private $shopFacade;

	/**
	 * @var UserIdentity
	 */
	private $userIdentity;

	/**
	 * @var OfferFacade
	 */
	private $offerFacade;

	public function __construct(Coupon $coupon = null, CompetitorFacade $competitorFacade, LocalizationFacade $localizationFacade, ShopFacade $shopFacade, OfferFacade $offerFacade, UserIdentity $userIdentity)
	{
		$this->coupon = $coupon;
		$this->competitorFacade = $competitorFacade;
		$this->localizationFacade = $localizationFacade;
		$this->shopFacade = $shopFacade;
		$this->offerFacade = $offerFacade;
		$this->userIdentity = $userIdentity;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$form->addText('name', 'Name:')
			->setRequired('Enter a name.');

		$form->addSelect('shop', 'Shop:', $this->shopFacade->findPairs())
			->setPrompt('Select a shop.')
			->setRequired('Select a shop.');

		$form->addSelect('discountType', 'Discount type:', Offer::getDiscountTypes())
			->setPrompt('Select a type.')
			->setRequired('Select a type.');

		$form->addInteger('discountAmount', 'Discount amount:')
			->setRequired('Enter a discount amount.');

		$form->addText('code', 'Code:')
			->setRequired('Enter a code.');

		$form->addText('exitUrl', 'Exit url:');

		$form->addText('validSince', 'Valid since:')
			->setRequired('Enter a valid since.');

		$form->addText('validTill', 'Valid till:')
			->setRequired('Enter a valid till.');

		$form->addTextArea('description', 'Description:', null, 10)
			->setHtmlAttribute('class', 'redactor')
			->setRequired(false);

		$form->addSelect('priorityLevel', 'Priority level:', Offer::getPriorityLevels())
			->setRequired('Select a priority level.');

		$form->addSubmit('submit', 'Save');
		$form->addSubmit('submitAndContinue', 'Save and continue editing');

		if ($this->coupon) {
			$form->setDefaults(
				[
					'localization' => $this->coupon->getLocalization()->getId(),
					'name' => $this->coupon->getName(),
					'shop' => $this->coupon->getShop()->getId(),
					'discountType' => $this->coupon->getDiscountType(),
					'discountAmount' => $this->coupon->getDiscountAmount(),
					'code' => $this->coupon->getCode(),
					'exitUrl' => $this->coupon->getExitUrl(),
					'validSince' => $this->coupon->getValidSince()->format('Y-m-d H:i:s'),
					'validTill' => $this->coupon->getValidTill()->format('Y-m-d H:i:s'),
					'description' => $this->coupon->getDescription(),
				]
			);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		$validSince = \DateTime::createFromFormat('Y-m-d H:i:s', $values->validSince);
		$validTill = \DateTime::createFromFormat('Y-m-d H:i:s', $values->validTill);
		$shop = $this->shopFacade->findShop($values->shop);
		$localization = $shop->getLocalization();

		try {
			$slug = Strings::webalize($values->name) . '-' . time();

			if (isset($values->slug) && $this->offerFacade->findOfferBySlug($localization, $slug)) {
				throw new InvalidArgumentException('This slug is already used at another coupon.');
			}

			$offer = $this->offerFacade->createOffer($localization, $values->name, $slug, $shop, Offer::TYPE_COUPON, $values->discountType, $validSince, $validTill);

			$offer->setDescription($values->description);
			$offer->setDiscountAmount($values->discountAmount);
			$offer->setCode($values->code);
			$offer->setExitUrl($values->exitUrl);
			$offer->setPriorityLevel($values->priorityLevel);
			$offer->setAuthor($this->userIdentity->getIdentity());

			$offer->setPriorityLevel($values->priorityLevel);
			$offer->confirm();
			$this->offerFacade->saveOffer($offer);

			$this->coupon->setOffer($offer);
			$this->coupon->close();
			$this->competitorFacade->saveCoupon($this->coupon);

			$this->onSuccess($this->coupon, $form['submitAndContinue']->isSubmittedBy());
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}


interface ICouponControlFactory
{
	public function create(Coupon $coupon = null): CouponControl;
}

{form form}
<div class="container">
    <div class="row justify-content-center">
        <div>
            <div class="card border-light">
                <div class="card-body">
                    <h5 class="card-title">
                        Edit localization: {$localization ? $localization->getName() : 'Unknown'}
                    </h5>

                    <div class="mb-3">
                        {label name, class: "form-label"}
                        {input name, class: "form-control"}
                        <div class="form-text">This field cannot be changed</div>
                    </div>
                    
                    <div class="mb-3">
                        {label shortDateFormat, class: "form-label"}
                        {input shortDateFormat, class: "form-control"}
                        <div class="form-text">Format for short date display (e.g., d.m.Y)</div>
                    </div>
                    
                    <div class="mb-3">
                        {label longDateFormat, class: "form-label"}
                        {input longDateFormat, class: "form-control"}
                        <div class="form-text">Format for long date display (e.g., j. <PERSON>)</div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex">
                        {input submit, class: "btn btn-primary"}
                        {input submitAndContinue, class: "btn btn-primary"}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/form}

<?php

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms\UserControl;

use <PERSON><PERSON><PERSON>\Model\Images\ImageStorage;
use <PERSON><PERSON>ino\Model\Localization\LocalizationFacade;
use <PERSON><PERSON>ino\Model\Users\Entities\User;
use <PERSON><PERSON>ino\Model\Users\UserFacade;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Http\FileUpload;
use Nette\Utils\Strings;

class UserControl extends Control
{
	public array $onSuccess = [];
	private ?User $user;
	private UserFacade $userFacade;
	private LocalizationFacade $localizationFacade;
	private ImageStorage $imageStorage;

	public function __construct(?User $user, UserFacade $userFacade, LocalizationFacade $localizationFacade, ImageStorage $imageStorage)
	{
		$this->user = $user;
		$this->userFacade = $userFacade;
		$this->localizationFacade = $localizationFacade;
		$this->imageStorage = $imageStorage;
	}

	public function render()
	{
		$this->template->user = $this->user;
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}

	public function createComponentForm()
	{
		$form = new Form();

		$fieldLocalization = $form->addSelect('localization', 'Localization:', $this->localizationFacade->findPairs())
			->setPrompt('-- select a localization --')
		;

		$fieldEmail = $form->addEmail('email', 'Email:');

		$form->addText('name', 'Name:')
			->setRequired('Enter a name.');

		$form->addText('surname', 'Surname:')
			->setRequired('Enter a name.');

		$form->addCheckbox('active', 'Active:')
			->setRequired(false)
			->addCondition($form::EQUAL, true)
			->toggle('activeTillContainer')
		;

		$form->addUpload('image', 'Profile image (Preferably JPEG):')
			->addCondition(Form::FILLED)
			->addRule(Form::IMAGE, 'Image must be in JPEG, PNG or GIF');

		$passwordField = $form->addPassword('password', $this->user ? 'Password (empty to keep current):' : 'Password:');

		if ($this->user === null) {
			$fieldLocalization->setRequired('Select a localization.');

			$fieldEmail->setRequired('Enter an email.')
				->addRule($form::EMAIL, 'Enter a valid email.')
				->addRule($form::MAX_LENGTH, 'Email is too long.', 255);

			$passwordField->setRequired('Enter a password.')
				->addRule($form::MIN_LENGTH, 'Password must be at least 6 characters long.', 6);
		}

		$form->addSubmit('submit', 'Save');
		$form->addSubmit('submitAndContinue', 'Save and continue editing');

		if ($this->user) {
			$fieldEmail->setDisabled();

			$form->setDefaults([
				'localization' => $this->user->getLocalization()->getId(),
				'email' => $this->user->getEmail(),
				'name' => $this->user->getName(),
				'surname' => $this->user->getSurname(),
				'active' => $this->user->isActive(),
			]);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if ($this->user === null) {
				if ($this->userFacade->findByEmail($values->email)) {
					throw new \InvalidArgumentException('User with this email already exists.');
				}

				$user = $this->userFacade->createUser(
					$this->localizationFacade->findLocalization($values->localization),
					$values->email,
					$values->password,
					$values->name,
					$values->surname
				);
			} else {
				$user = $this->user;

				$user->setName($values->name);
				$user->setSurname($values->surname);
				$user->setLocalization($this->localizationFacade->findLocalization($values->localization));

				if ($values->password && Strings::length($values->password) >= 6) {
					$user->setPassword($values->password);
				}
			}

			if ($values->active) {
				$user->setActive();
			} else {
				$user->setInactive();
			}

			/** @var FileUpload $image */
			$image = $values->image;

			if ($image->isOk()) {
				$oldImage = $user->getImageUrl();
				$newImage = $this->imageStorage->saveImage($values->image, ImageStorage::NAMESPACE_USER_IMAGE, rand(500, 1000), $user->getSlug());
				$user->setImageUrl($newImage);

				if ($oldImage != $newImage) {
					$this->imageStorage->removeImage($oldImage);
				}
			}

			$this->userFacade->saveUser($user);

			$this->onSuccess($user, $form['submitAndContinue']->isSubmittedBy());
		} catch (\InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}
}

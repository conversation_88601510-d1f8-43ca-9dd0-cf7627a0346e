<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Forms;

use <PERSON><PERSON><PERSON>\Model\Seo\Entities\PageExtension;
use <PERSON><PERSON><PERSON>\Model\Seo\Events\PageExtensionNewVersionCreatedEvent;
use <PERSON><PERSON><PERSON>\Model\Seo\SeoFacade;
use <PERSON><PERSON><PERSON>\Model\Users\Entities\User;
use <PERSON><PERSON>ino\Model\Websites\WebsiteFacade;
use Nette;
use Nette\Application\UI\Form;
use Nette\InvalidArgumentException;
use Nette\Utils\Strings;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

class PageExtensionControl extends Nette\Application\UI\Control
{
	/** @var array */
	public $onSuccess = [];

	/** @var User */
	private $user;

	/** @var ?PageExtension */
	private $pageExtension;

	/** @var SeoFacade */
	private $seoFacade;

	/** @var WebsiteFacade */
	private $websiteFacade;

	/** @var string */
	private $websiteId;

	/** @var string */
	private $slug;

	/** @var EventDispatcherInterface */
	private $eventDispatcher;

	public function __construct(
		User $user,
		PageExtension $pageExtension = null,
		string $websiteId = null,
		string $slug = null,
		SeoFacade $seoFacade,
		WebsiteFacade $websiteFacade,
		EventDispatcherInterface $eventDispatcher
	) {
		$this->user = $user;
		$this->pageExtension = $pageExtension;
		$this->seoFacade = $seoFacade;
		$this->websiteFacade = $websiteFacade;
		$this->websiteId = $websiteId;
		$this->slug = $slug;
		$this->eventDispatcher = $eventDispatcher;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();

		$fieldWebsite = $form->addSelect('website', 'Website:', $this->websiteFacade->findPairs())
			->setPrompt('-- select a website --')
			->setRequired('Select a website.');

		$fieldSlug = $form->addText('slug', 'Slug:')
			->setRequired('Enter a slug.');

		if ($this->pageExtension || $this->websiteId) {
			$fieldWebsite->setDisabled();
		}

		if ($this->slug) {
			$fieldSlug->setDisabled();
		}

		$form->addTextArea('heading', 'Heading (h1):')
			->setRequired(false);

		$form->addTextArea('shortDescription', 'short description:', null, 4)
			->setRequired(false);

		$form->addTextArea('longDescription', 'long description:', null, 10)
			->setHtmlAttribute('class', 'redactor')
			->setRequired(false);

		$form->addTextArea('title', 'meta title:', null, 10)
			->setRequired(false);

		$form->addTextArea('description', 'meta description:', null, 10)
			->setRequired(false);

		$form->addTextArea('keywords', 'Keywords:', null, 10)
			->setRequired(false);

		$form->addSubmit('submit', 'Save');
		$form->addSubmit('submitAndContinue', 'Save and continue editing');

		//Debugger::dump($this->tag);

		if ($this->pageExtension) {
			$form->setDefaults(
				[
					'website' => $this->pageExtension->getWebsite()->getId(),
					'heading' => $this->pageExtension->getHeading(),
					'slug' => $this->pageExtension->getSlug(),
					'title' => $this->pageExtension->getTitle(),
					'description' => $this->pageExtension->getDescription(),
					'shortDescription' => $this->pageExtension->getShortDescription(),
					'longDescription' => $this->pageExtension->getLongDescription(),
					'keywords' => $this->pageExtension->getKeywords(),
				]
			);
		} else {
			$form->setDefaults(
				[
					'website' => $this->websiteId,
					'slug' => $this->slug,
				]
			);
		}

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			if ($this->pageExtension) {
				$website = $this->pageExtension->getWebsite();
			} else {
				$website = isset($values->website) ? $this->websiteFacade->findWebsite($values->website) : null;

				if (!$website) {
					$website = $this->websiteFacade->findWebsite($this->websiteId);
				}

				if (!$website) {
					throw new InvalidArgumentException('Select a website, please.');
				}
			}

			$inputSlug = isset($values->slug) ? $values->slug : $this->slug;

			$slug = $inputSlug;
			if ($slug && $this->seoFacade->findPageExtensionBySlug($website, $slug) && !($this->pageExtension && $this->pageExtension->getSlug() == $slug)) {
				throw new InvalidArgumentException('This slug is already used at another page extension.');
			}

			if ($this->pageExtension) {
				$pageExtension = $this->pageExtension;
			} else {
				$pageExtension = $this->seoFacade->createPageExtension($website, $slug);
			}

			$pageExtension->setSlug($slug);
			$pageExtension->setHeading($values->heading);
			$pageExtension->setShortDescription($values->shortDescription);
			$pageExtension->setLongDescription($values->longDescription);

			$pageExtension->setTitle($values->title);
			$pageExtension->setDescription($values->description);
			$pageExtension->setKeywords($values->keywords);

			$this->seoFacade->savePageExtension($pageExtension);

			$this->eventDispatcher->dispatch(
				new PageExtensionNewVersionCreatedEvent($pageExtension, $this->user)
			);

			$this->onSuccess($pageExtension, $form['submitAndContinue']->isSubmittedBy());
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	public function render()
	{
		$this->template->setFile(__DIR__ . '/control.latte');
		$this->template->render();
	}
}


interface IPageExtensionControlFactory
{
	public function create(
		User $user,
		PageExtension $pageExtension = null,
		string $websiteId = null,
		string $slug = null
	): PageExtensionControl;
}

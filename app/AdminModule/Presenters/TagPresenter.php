<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ContentBlocksControl\ContentBlocksControl;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ContentBlocksControl\IContentBlockControlFactory;
use Ka<PERSON>ino\AdminModule\Forms\TagControl\TagControl;
use Ka<PERSON>ino\AdminModule\Forms\TagControl\TagControlFactory;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlockType;
use <PERSON><PERSON><PERSON>\Model\Tags\Entities\Tag;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use Ka<PERSON><PERSON>\Model\Users\UserFacade;
use Ka<PERSON><PERSON>\Model\Websites\Entities\Website;

class TagPresenter extends BasePresenter
{
	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var UserFacade @inject */
	public $userFacade;

	/** @var TagControlFactory @inject */
	public $tagControlFactory;

	/** @var IContentBlockControlFactory @inject */
	public $contentBlockControlFactory;

	public function actionTag($id = null)
	{
		if ($id) {
			/** @var Tag $tag */
			$tag = $this->tagFacade->findTag($id);

			if ($tag === null) {
				$this->error('Tag not found.');
			}

			$this->template->tag = $tag;
		}
	}

	public function createComponentTagsGrid($name)
	{
		$tags = $this->tagFacade->getTags(null, null, false);

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $tags);

		$grid->setRememberState(true);
		$grid->setColumnsHideable();
		$grid->setStrictSessionFilterValues(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Tag/grid/default.latte');

		$grid->addToolbarButton(':Admin:Tag:tag')
			->setTitle('Add a new record')
			->setIcon('plus')
			->setClass('btn btn-xs btn-primary');

		$grid->addColumnText('localization', 'Localization')
			->setRenderer(static function (Tag $tag) {
				return $tag->getLocalization()->getName();
			})->setFilterSelect(['' => 'All'] + $this->localizationFacade->findPairs());

		$grid->addColumnText('name', 'Name');
		$grid->addColumnText('englishName', 'English name');

		$grid->addColumnText('slug', 'Slug');

		$grid->addColumnText('type', 'Type')
			->setFilterSelect(['' => 'All'] + Tag::getTypes());
		$grid->addColumnText('matchRule', 'Match rule');

		$grid->addColumnText('active', 'Active')
			->setRenderer(static function (Tag $tag) {
				return $tag->isActive() ? 'Yes' : 'No';
			})
			->setFilterSelect(['' => 'All'] + [true => 'Yes', false => 'No'])
		;

		$grid->addFilterText('name', 'Name')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('t.name LIKE :name')->setParameter('name', '%' . $value . '%');
			});

		$grid->addFilterText('englishName', 'English name')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('t.englishName LIKE :englishName')->setParameter('englishName', '%' . $value . '%');
			});

		$grid->addFilterText('matchRule', 'Match rule')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('t.matchRule LIKE :matchRule')->setParameter('matchRule', '%' . $value . '%');
			});

		$grid->addAction('tag', '', 'tag')->setClass('btn btn-xs btn-primary')->setIcon('pencil');
		$grid->addAction('openTag', '', 'openTag!')->setClass('btn btn-xs btn-primary')->setIcon('search');

		$grid->setItemsPerPageList([10, 50, 100, 200], false);
		$grid->setDefaultSort(['name' => 'ASC']);
	}

	public function handleOpenTag($id)
	{
		/** @var Tag $tag */
		$tag = $this->tagFacade->findTag($id);

		/** @var Localization $localization */
		$localization = $tag->getLocalization()->getRegion();

		$this->redirectUrl('https://www.kaufino.com/' . $localization . '/' . $tag->getSlug());
	}

	protected function createComponentTagControl(): TagControl
	{
		$tag = $this->getParameter('id') ? $this->tagFacade->findTag($this->getParameter('id')) : null;
		$control = $this->tagControlFactory->create($tag);

		$control->onSuccess[] = function ($entity, $continue) use ($tag) {
			$this->flashMessage('Successfully saved.');
			if ($tag && !$continue) {
				$this->redirect(':Admin:Tag:default');
			} else {
				$this->redirect(':Admin:Tag:tag', $entity->getId());
			}
		};

		return $control;
	}

	public function createComponentContentBlocksControl(): ContentBlocksControl
	{
		$control = $this->contentBlockControlFactory->create(
			null,
			$this->tagFacade->findTag((int) $this->getParameter('id')),
			$this->userFacade->find($this->getUser()->getId()),
			$this->getParameter('websiteType'),
			ContentBlockType::ENTITY_TAG
		);

		$control->onSuccess[] = function () {
			$this->flashMessage('Successfully saved.');
			$this->redirect('this');
		};

		return $control;
	}

	public function renderContentBlocks(int $id, string $websiteType)
	{
		$websiteType = $this->getParameter('websiteType');

		if ($websiteType === null || in_array($websiteType, Website::getModules()) === false) {
			$this->redirect('tag', $id);
		}

		$tag = $this->tagFacade->findTag((int) $this->getParameter('id'));

		$this->template->website = $this->websiteFacade->findActiveWebsiteByLocalization($tag->getLocalization(), $websiteType);

		$this->template->tag = $tag;
		$this->template->websiteType = $websiteType;
	}
}

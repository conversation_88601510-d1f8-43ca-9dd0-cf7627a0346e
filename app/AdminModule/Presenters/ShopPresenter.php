<?php

declare(strict_types=1);

namespace Ka<PERSON>ino\AdminModule\Presenters;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ContentBlocksControl\ContentBlocksControl;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ContentBlocksControl\IContentBlockControlFactory;
use Ka<PERSON>ino\AdminModule\Forms\IShopControlFactory;
use Kaufino\AdminModule\Forms\ReviewControl\IReviewControlFactory;
use Ka<PERSON>ino\AdminModule\Forms\ShopControl;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlockType;
use Ka<PERSON><PERSON>\Model\Shops\Entities\Review;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ReviewFacade;
use Ka<PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use <PERSON><PERSON><PERSON>\Model\Users\UserFacade;
use Ka<PERSON><PERSON>\Model\Websites\Entities\Website;
use Nette\Application\Attributes\Persistent;

class ShopPresenter extends BasePresenter
{
	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var IShopControlFactory @inject */
	public $shopControlFactory;

	/** @var IContentBlockControlFactory @inject */
	public $contentBlockControlFactory;

	/** @var UserFacade @inject */
	public $userFacade;

	/** @var ReviewFacade @inject */
	public $reviewFacade;

	/** @var IReviewControlFactory @inject */
	public $reviewControlFactory;

	public function actionShop($id = null)
	{
		if ($id) {
			/** @var Shop $shop */
			$shop = $this->shopFacade->findShop($id);

			if ($shop === null) {
				$this->error('Shop not found.');
			}

			$this->template->shop = $shop;

			$localization = $shop->getLocalization();

			$this->template->getWebsiteByType = function (string $websiteType) use ($localization, $shop) {
				if ($websiteType !== Website::MODULE_KAUFINO && $shop->isStore() === false) {
					return null;
				}

				if ($websiteType === Website::MODULE_OFERTO && $shop->isActiveOferto() === false) {
					return null;
				}

				return $this->websiteFacade->findActiveWebsiteByLocalization($localization, $websiteType);
			};
		}
	}

	public function renderContentBlocks(int $id, string $websiteType)
	{
		$websiteType = $this->getParameter('websiteType');

		if ($websiteType === null || in_array($websiteType, Website::getModules()) === false) {
			$this->redirect('shop', $id);
		}

		$shop = $this->shopFacade->findShop((int) $this->getParameter('id'));

		$this->template->website = $this->websiteFacade->findActiveWebsiteByLocalization($shop->getLocalization(), $websiteType);

		$this->template->shop = $shop;
		$this->template->websiteType = $websiteType;
	}

	public function createComponentContentBlocksControl(): ContentBlocksControl
	{
		$control = $this->contentBlockControlFactory->create(
			$this->shopFacade->findShop((int) $this->getParameter('id')),
			null,
			$this->userFacade->find($this->getUser()->getId()),
			$this->getParameter('websiteType'),
			ContentBlockType::ENTITY_SHOP
		);

		$control->onSuccess[] = function () {
			$this->flashMessage('Successfully saved.');
			$this->redirect('this');
		};

		return $control;
	}

	public function renderReviews(int $id)
	{
		$this->template->shop = $this->shopFacade->findShop($id);
	}

	public function createComponentReviewControl()
	{
		$control = $this->reviewControlFactory->create(
			$this->shopFacade->findShop((int) $this->getParameter('id')),
			$this->userFacade->find($this->getUser()->getId())
		);

		$control->onSuccess[] = function () {
			$this->flashMessage('Successfully saved.');
			$this->redirect('this');
		};

		return $control;
	}

	public function createComponentReviewsGrid($name)
	{
		$reviews = $this->reviewFacade->getReviewsByShop(
			$this->shopFacade->findShop($this->getParameter('id'))
		);

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $reviews);

		$grid->addColumnText('rate', 'Počet hvězd');

		$grid->addColumnText('user', 'Přidal', 'createdByUser.email');

		$grid->addAction('removeReview!', '')->setClass('btn btn-xs btn-danger')->setIcon('times');

		return $grid;
	}

	public function handleRemoveReview(int $id)
	{
		/** @var Review $review */
		$review = $this->reviewFacade->find($id);
		$shop = $review->getShop();

		$this->reviewFacade->removeReview($review);

		$this->redirect('reviews', ['id' => $shop->getId()]);
	}

	public function createComponentShopsGrid($name)
	{
		$shops = $this->shopFacade->getShops();

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $shops);

		$grid->setRememberState(true);
		$grid->setColumnsHideable();
		$grid->setStrictSessionFilterValues(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Shop/grid/default.latte');

#		$grid->addToolbarButton(':Admin:Shop:shop')
#			->setTitle('Add a new record')
#			->setIcon('plus')
#			->setClass('btn btn-xs btn-primary');

		$grid->addColumnText('localization', 'Localization')
			->setRenderer(static function (Shop $shop) {
				return $shop->getLocalization()->getName();
			})->setFilterSelect(['' => 'All'] + $this->localizationFacade->findPairs());

		$grid->addColumnText('logo', 'Logo')->setFilterSelect(['' => 'All'] + ['withLogo' => 'With logo', 'withoutLogo' => 'Without logo'])
			->setCondition(static function (QueryBuilder $qb, $value) {
				if ($value == 'withLogo') {
					$qb->andWhere('s.logoUrl IS NOT NULL');
				} elseif ($value == 'withoutLogo') {
					$qb->andWhere('s.logoUrl IS NULL');
				}
			});

		$grid->addColumnText('name', 'Name');
		$grid->addColumnText('slug', 'Slug');

		$grid->addColumnStatus('tag.name', 'Tag');

		$grid->addColumnText('activeKaufino', 'Kaufino')
			->setFilterSelect(['' => 'All'] + [1 => 'Active', 0 => 'Inactive']);

		$grid->addColumnText('activeLetado', 'Letado')
			->setFilterSelect(['' => 'All'] + [1 => 'Active', 0 => 'Inactive']);

		$grid->addColumnText('activeOferto', 'MrOferto')
			->setFilterSelect(['' => 'All'] + [1 => 'Active', 0 => 'Inactive']);

		$grid->addColumnText('activeOfertoCom', 'MrOfertoCom')
			->setFilterSelect(['' => 'All'] + [1 => 'Active', 0 => 'Inactive']);

		$grid->addColumnText('priorityLeaflets', 'Priority - leaflets')
			->setSortable();

		$grid->addColumnText('priorityCoupons', 'Priority - coupons')
			->setSortable();

		$grid->addColumnText('type', 'Type')
			->setFilterText();

		$grid->addColumnNumber('shopId', 'Steve ID')
			->setFormat(0, '', '')
			->setFilterText();

		$grid->addFilterText('name', 'Name')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('s.name LIKE :name')->setParameter('name', '%' . $value . '%');
			});

		$grid->addColumnNumber('cities', 'Cities')
			->setRenderer(static function (Shop $shop) {
				return count($shop->getCities());
			})
		;

		$grid->addFilterSelect('cities', 'Cities', ['' => 'All'] + [0 => '0 cities'])
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('SIZE(s.cities) = :cities')->setParameter('cities', $value);
			});

		$grid->addColumnNumber('activeCities', 'Active cities')
			->setRenderer(static function (Shop $shop) {
				return $shop->hasActiveCities() ? 1 : 0;
			})
			->setFilterSelect(['' => 'All', 1 => 'Active', 0 => 'Inactive'])
		;

		$grid->addColumnText('useOcr', 'Use OCR')
			->setRenderer(static function (Shop $shop) {
				return $shop->isUseOcr() ? 'Yes' : 'No';
			})
			->setFilterSelect(['' => 'All', 1 => 'Yes', 0 => 'No']);

		$grid->addAction('shop', '', 'shop')->setClass('btn btn-xs btn-primary')->setIcon('pencil');
		$grid->addAction('openShop', '', 'openShop!')->setClass('btn btn-xs btn-primary')->setIcon('search');

		$grid->setItemsPerPageList([10, 50, 100, 200], false);
		$grid->setDefaultSort(['name' => 'ASC']);
	}

	public function handleOpenShop($id)
	{
		/** @var Shop $shop */
		$shop = $this->shopFacade->findShop($id);

		/** @var Localization $localization */
		$localization = $shop->getLocalization()->getRegion();

		$this->redirectUrl('https://www.kaufino.com/' . $localization . '/' . $shop->getSlug());
	}

	protected function createComponentShopControl(): ShopControl
	{
		$shop = $this->getParameter('id') ? $this->shopFacade->findShop($this->getParameter('id')) : null;
		$control = $this->shopControlFactory->create($shop);

		$control->onSuccess[] = function ($entity, $continue) use ($shop) {
			$this->flashMessage('Successfully saved.');

			if ($shop && !$continue) {
				$this->redirect(':Admin:Shop:default');
			} else {
				$this->redirect(':Admin:Shop:shop', $entity->getId());
			}
		};

		return $control;
	}
}

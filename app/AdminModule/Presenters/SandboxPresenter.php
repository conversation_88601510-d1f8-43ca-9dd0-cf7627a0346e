<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Shops\ContentBlockFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlock;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;

class SandboxPresenter extends BasePresenter
{
	/** @var ContentBlockFacade @inject */
	public $contentBlockFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	public function actionMergeContentBlocks()
	{
		$contentBlocksToMerge = [
			'flyers' => [1, 2, 3],
			'about' => [29, 35, 32, 32, 36],
			'contact' => [34, 31],
		];

		foreach ($contentBlocksToMerge as $type => $contentBlockTypeIds) {
			$contentBlocks = $this->contentBlockFacade->getContentBlocks()
				->andWhere('cb.contentBlockType IN (:ids)')
				->setParameter('ids', $contentBlockTypeIds)
				->getQuery()
				->getResult();

			$blocksByShop = [];
			foreach ($contentBlocks as $contentBlock) {
				$blocksByShop[$contentBlock->getShop()->getId()][$contentBlock->getType()] = $contentBlock;
			}

			foreach ($blocksByShop as $blocks) {
				$content = '';
				/** @var ContentBlock $block */
				foreach ($blocks as $block) {
					if ($block->getHeading()) {
						$content .= '<h2>' . $block->getHeading() . '</h2>';
					}

					$content .= $block->getContent();
				}

				$blockToUpdate = $blocks[$type];
				$blockToUpdate->setNewContent($content);
				$this->contentBlockFacade->saveContentBlock($blockToUpdate);
			}
		}

		$this->terminate();
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\AdminModule\Components\IDataGridFactory;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\LocalizationControl\LocalizationControl;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\LocalizationControl\LocalizationControlFactory;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Localization\LocalizationFacade;
use Ublaboo\DataGrid\DataGrid;

final class LocalizationPresenter extends BasePresenter
{
	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var IDataGridFactory @inject */
	public $dataGridFactory;

	/** @var LocalizationControlFactory @inject */
	public $localizationControlFactory;

	public function actionLocalization($id)
	{
		if ($id && !$this->localizationFacade->findLocalization($id)) {
			$this->error();
		}
	}

	public function createComponentLocalizationsGrid($name)
	{
		$localizations = $this->localizationFacade->getLocalizations();

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $localizations);

		$grid->setRememberState(true);
		$grid->setColumnsHideable();
		$grid->setStrictSessionFilterValues(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Localization/grid/default.latte');

		$grid->addColumnText('id', 'ID')
			->setFilterText();

		$grid->addColumnText('name', 'Name')
			->setFilterText();

		$grid->addColumnText('locale', 'Locale')
			->setFilterText();

		$grid->addColumnText('region', 'Region')
			->setFilterText();

		$grid->addColumnText('fullLocale', 'Full Locale')
			->setFilterText();

		$grid->addColumnText('shortDateFormat', 'Short Date Format')
			->setFilterText();

		$grid->addColumnText('longDateFormat', 'Long Date Format')
			->setFilterText();

		$grid->addColumnText('active', 'Active')
			->setRenderer(static function (Localization $localization) {
				return $localization->isActive() ? 'Yes' : 'No';
			})
			->setFilterSelect(['' => 'All'] + [1 => 'Yes', 0 => 'No']);

		$grid->addAction('localization', '', 'localization')->setClass('btn btn-xs btn-primary')->setIcon('pencil');

		$grid->setItemsPerPageList([10, 50, 100, 200], false);
		$grid->setDefaultSort(['name' => 'ASC']);

		return $grid;
	}

	protected function createComponentLocalizationControl(): LocalizationControl
	{
		$localization = $this->getParameter('id') ? $this->localizationFacade->findLocalization($this->getParameter('id')) : null;
		$control = $this->localizationControlFactory->create($localization);

		$control->onSuccess[] = function ($entity, $continue) use ($localization) {
			$this->flashMessage('Successfully saved.');
			if ($localization && !$continue) {
				$this->redirect(':Admin:Localization:default');
			} else {
				$this->redirect(':Admin:Localization:localization', $entity->getId());
			}
		};

		return $control;
	}
}

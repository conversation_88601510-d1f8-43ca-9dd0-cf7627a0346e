<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use <PERSON><PERSON><PERSON>\Forms;
use Nette\Application\UI\Form;

final class SignPresenter extends BasePresenter
{
	/** @persistent */
	public $backlink = '';

	/** @var Forms\SignInFormFactory */
	private $signInFactory;

	public function __construct(Forms\SignInFormFactory $signInFactory)
	{
		$this->signInFactory = $signInFactory;
	}

	/**
	 * Sign-in form factory.
	 */
	protected function createComponentSignInForm(): Form
	{
		return $this->signInFactory->create(function (): void {
			$this->restoreRequest($this->backlink);
			$this->redirect('Homepage:');
		});
	}

	public function actionOut(): void
	{
		$this->getUser()->logout();
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\IOfferControlFactory;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\OfferControl;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\IOfferLeafletPageControlFactory;
use Ka<PERSON><PERSON>\AdminModule\Forms\OfferLeafletPageControl;
use Ka<PERSON>ino\Model\Localization\LocalizationFacade;
use <PERSON><PERSON>ino\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use Nette\Utils\Json;

class OfferPresenter extends BasePresenter
{
	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var IOfferControlFactory @inject */
	public $offerControlFactory;

	/** @var IOfferLeafletPageControlFactory @inject */
	public $offerLeafletPageControlFactory;

	/** @persistent */
	public ?int $localizationId = null;

	public function actionOffer($id = null)
	{
		if ($id) {
			/** @var Offer $offer */
			$offer = $this->offerFacade->findOffer($id);

			if ($offer === null) {
				$this->error('Offer not found.');
			}

			$this->template->offer = $offer;
		}
	}

	public function createComponentOffersGrid($name)
	{
		$offers = $this->offerFacade->getOffers(null, false);

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $offers);

		$grid->setRememberState(true);
		$grid->setColumnsHideable();
		$grid->setStrictSessionFilterValues(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Offer/grid/default.latte');

		$grid->addToolbarButton(':Admin:Offer:offer')
			->setTitle('Add a new record')
			->setIcon('plus')
			->setClass('btn btn-xs btn-primary');

		$grid->addColumnText('localization', 'Localization')
			->setRenderer(static function (Offer $offer) {
				return $offer->getLocalization()->getName();
			})->setFilterSelect(['' => 'All'] + $this->localizationFacade->findPairs());

		$grid->addColumnText('id', 'Id');

		$grid->addColumnText('shop', 'Shop')
			->setRenderer(static function (Offer $offer) {
				return $offer->getShop()->getName();
			});

		$grid->addColumnText('name', 'Name')
			->setEditableCallback(function ($id, $value): void {
				$offer = $this->offerFacade->findOffer($id);
				$offer->setName($value);
				$this->offerFacade->saveOffer($offer);
			});

		$grid->addColumnText('image', 'Image')->setFilterSelect(['' => 'All'] + ['withImage' => 'With image', 'withoutImage' => 'Without image'])
			->setCondition(static function (QueryBuilder $qb, $value) {
				if ($value == 'withImage') {
					$qb->andWhere('o.imageUrl IS NOT NULL');
				} elseif ($value == 'withoutImage') {
					$qb->andWhere('o.imageUrl IS NULL');
				}
			});

		$grid->addColumnText('priorityLevel', 'Priority level')
			->setEditableCallback(function ($id, $value): void {
				$offer = $this->offerFacade->findOffer($id);
				$offer->setPriorityLevel((int) $value);
				$this->offerFacade->saveOffer($offer);
			})
			->setEditableInputTypeSelect(Offer::getPriorityLevels())
			->setFilterSelect(['' => 'All'] + Offer::getPriorityLevels());



		//$grid->addColumnStatus('tag.name', 'Tag');


		$grid->addFilterText('name', 'Name')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('o.name LIKE :name')->setParameter('name', '%' . $value . '%');
			});

		$grid->addAction('offer', '', 'offer')->setClass('btn btn-xs btn-primary')->setIcon('pencil');

		$grid->setItemsPerPageList([10, 50, 100, 200], false);
		$grid->setDefaultSort(['o.name' => 'ASC']);
	}

	protected function createComponentOfferControl(): OfferControl
	{
		$offer = $this->getParameter('id') ? $this->offerFacade->findOffer($this->getParameter('id')) : null;
		$control = $this->offerControlFactory->create($offer);

		$control->onSuccess[] = function ($entity, $continue) use ($offer) {
			$this->flashMessage('Successfully saved.');
			if ($offer && !$continue) {
				$this->redirect(':Admin:Offer:default');
			} else {
				$this->redirect(':Admin:Offer:offer', $entity->getId());
			}
		};

		return $control;
	}

	public function actionAnnotations($id = null)
	{
		$localization = null;
		if ($this->localizationId) {
			$localization = $this->localizationFacade->findLocalization($this->localizationId);
		}

		if ($id) {
			$offer = $this->offerFacade->findOffer($id);

			if ($offer->waitingToConfirm() === false) {
				$this->redirect('this', ['id' => null]);
			}
		} else {
			$offers = $this->offerFacade->findOffersToConfirm(1, $localization);

			if (count($offers) > 0) {
				$this->redirect('annotations', ['id' => $offers[0]->getId()]);
			}

			$offer = null;
		}

		$this->template->localizationId = $this->localizationId;
		$this->template->countOfOffersRemaining = $this->offerFacade->getCountOfOffersToConfirm($localization);
		$this->template->countOfAllOffersRemaining = $this->offerFacade->getCountOfOffersToConfirm();
		$this->template->countOfOffersToAnnotateByLocalization = $this->offerFacade->findCountOfOffersToAnnotateByLocalization();
		$this->template->offer = $offer;
	}

	protected function createComponentOfferLeafletPageControl(): OfferLeafletPageControl
	{
		$offer = $this->getParameter('id') ? $this->offerFacade->findOffer($this->getParameter('id')) : null;
		$control = $this->offerLeafletPageControlFactory->create($offer);

		$control->onSuccess[] = function ($entity) {
			$this->flashMessage('Successfully saved.');

			$localization = $this->localizationId ? $this->localizationFacade->findLocalization($this->localizationId) : null;

			$offers = $this->offerFacade->findOffersToConfirm(1, $localization);

			if (count($offers) > 0) {
				$this->redirect('annotations', ['localizationId' => $this->localizationId, 'id' => $offers[0]->getId()]);
			}

			$this->redirect(':Admin:Offer:annotations', ['localizationId' => $this->localizationId]);
		};

		return $control;
	}

	public function actionArchive(int $offerId, string $backLink)
	{
		$offer = $this->offerFacade->findOffer($offerId);

		$offer->archive();

		$this->offerFacade->saveOffer($offer);

		$this->redirectUrl($backLink);
	}
}

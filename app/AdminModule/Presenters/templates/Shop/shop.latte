{block title}Shop{/block}

{block content}

{var $title = 'Shop'}

{ifset $shop}
    <div class="container-fluid no-padding">
        <div class="card card-accent-primary">
            <div class="card-header">
                <h1>{$title}</h1>
            </div>

            <div class="card-body">
                {if $shop->getLogoUrl()}
                    <img src="{$shop->getLogoUrl()|image:300}" style="max-width: 150px; max-height: 50px;">
                {else}
                    <div class="alert alert-warning" style="margin-bottom: 15px; padding: 3px 12px">
                        No logo added for this shop.
                    </div>
                {/if}

                <div class="d-flex justify-content-end mr-3 gap-1">
                   <a n:href="reviews, $shop->getId()" class="btn btn-success btn-sm">Reviews</a>

                   {foreach Kaufino\Model\Websites\Entities\Website::getModules() as $websiteType}
                        <a n:href=":Admin:Shop:contentBlocks, id: $shop->getId(), entity: 'shop', websiteType: $websiteType" class="btn btn-primary btn-sm" role="button">Content {$websiteType |firstUpper}</a>
                   {/foreach}

                    <span class="ml-2">
                        {foreach Kaufino\Model\Websites\Entities\Website::getModules() as $websiteType}
                            {var $websiteByTypeAndShop = $getWebsiteByType($websiteType)}

                            <a href="{$websiteByTypeAndShop->getDomain() . ($websiteByTypeAndShop->getModule() === 'oferto' ? '/') . $shop->getSlug()}" target="_blank" n:if="$websiteByTypeAndShop">
                                Visit on {$websiteType |firstUpper} »
                            </a>
                        {/foreach}
                    </span>
                </div>

                {control shopControl}
            </div>
        </div>
    </div>
{else}
    <h1>{$title}</h1>
    {control shopControl}
{/ifset}
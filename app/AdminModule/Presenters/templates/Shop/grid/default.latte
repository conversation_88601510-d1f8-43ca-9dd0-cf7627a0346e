{extends $originalTemplate}

{define col-logo}
    {if $item->getLogoUrl()}
        <img src="{$item->getLogoUrl()}" alt="{$item->getName()}" style="max-width: 150px; max-height: 50px;">
    {else}
        <i class="fa fa-check text-danger text-center d-block d-xl-none">
    {/if}
{/define}

{define col-name}
    {$item->getName()}
{/define}


{define col-activeKaufino}
    {if $item->isActiveKaufino()}
       <span class="badge badge-pill badge-success">Active</span>
    {else}
        <span class="badge badge-pill badge-warning">Inactive</span>
    {/if}
{/define}

{define col-activeLetado}
    {if $item->isActiveLetado()}
       <span class="badge badge-pill badge-success">Active</span>
    {else}
        <span class="badge badge-pill badge-warning">Inactive</span>
    {/if}
{/define}

{define col-activeOferto}
    {if $item->isActiveOferto()}
       <span class="badge badge-pill badge-success">Active</span>
    {else}
        <span class="badge badge-pill badge-warning">Inactive</span>
    {/if}
{/define}

{define col-activeOfertoCom}
    {if $item->isActiveOfertoCom()}
       <span class="badge badge-pill badge-success">Active</span>
    {else}
        <span class="badge badge-pill badge-warning">Inactive</span>
    {/if}
{/define}
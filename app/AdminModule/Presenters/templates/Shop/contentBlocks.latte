{block content}

<div class="container">
    <div class="d-flex mb-2">
        <a n:href="shop, $shop->getId()" class="p-1">« Back</a>

        <h1>
            Content blocks

            {if $website}
                <a href="{$website->getDomain() . ($website->getModule() === 'oferto' ? '/') . $shop->getSlug()}" target="_blank"><strong>{$shop->getName()}({$shop->getLocalization()->getLocale() |upper})</strong></a>
            {else}
                {dump $shop}
                <strong>{$shop->getName()}({$shop->getLocalization()->getLocale() |upper})</strong>
            {/if}

            for <strong>{$websiteType |firstUpper}</strong>
        </h1>
    </div>

    {control contentBlocksControl}
</div>
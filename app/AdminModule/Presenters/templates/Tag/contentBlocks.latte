{block content}

<div class="container">
    <div class="d-flex mb-2">
        <a n:href="tag, $tag->getId()" class="p-1">« Back</a>

        <h1>
            Content blocks

            {if $website}
                <a href="{$website->getDomain() . ($website->getModule() === 'oferto' ? '/') . $tag->getSlug()}" target="_blank"><strong>{$tag->getName()}({$tag->getLocalization()->getLocale() |upper})</strong></a>
            {else}
                <strong>{$tag->getName()}({$tag->getLocalization()->getLocale() |upper})</strong>
            {/if}

            for <strong>{$websiteType |firstUpper}</strong>
        </h1>
    </div>

    {control contentBlocksControl}
</div>
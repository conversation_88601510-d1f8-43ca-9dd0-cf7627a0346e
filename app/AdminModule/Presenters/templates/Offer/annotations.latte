{block content}

<div class="card mx-2">
    <div class="card-body">
        <div class="btn-group" role="group" aria-label="Basic outlined example">
            <a n:href="this, id: null, localizationId: null"  class="btn {$localizationId === null ? "btn-primary" : "btn-outline-primary"}">All ({$countOfAllOffersRemaining})</a>
            {foreach $countOfOffersToAnnotateByLocalization as $localizationItem}
                <a n:href="this, id: null, localizationId: $localizationItem['id']"  class="btn {$localizationId === $localizationItem['id'] ? "btn-primary" : "btn-outline-primary"}">{$localizationItem['name']} ({$localizationItem['count']})</a>
            {/foreach}
        </div>
    </div>
</div>

{if $offer}
    <h1>Annotate (remaining {$countOfOffersRemaining})</h1>

    <div class="container mt-4" style="text-align: center">
        <div class="row">
            <div class="col">
                <img id="leaflet" src="{$offer->getImageUrl()}" alt="" class="img-fluid" style="max-width: 500px;min-width:300px">
            </div>
            <div class="col">
                {control offerLeafletPageControl}
            </div>
        </div>
    </div>

{else}
    <h1>All done</h1>
{/if}

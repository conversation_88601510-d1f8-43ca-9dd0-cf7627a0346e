{block title}Product annotations{/block}

{block head}
    <script crossorigin src="https://unpkg.com/react@17/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>


    <script src="{$basePath}/js/product.js"></script>


{*    <script n:syntax=off type="text/babel">

        'use strict';

        const e = React.createElement;

        class LikeButton extends React.Component {
            constructor(props) {
                super(props);
                this.state = { liked: false };
            }

            componentDidMount() {
                fetch("http://localhost:8000/admin/product/data")
                    .then(res => res.json())
                    .then(
                        (result) => {
                            this.setState({
                                isLoaded: true,
                                item: result
                            });
                        },
                        // Note: it's important to handle errors here
                        // instead of a catch() block so that we don't swallow
                        // exceptions from actual bugs in components.
                        (error) => {
                            this.setState({
                                isLoaded: true,
                                error
                            });
                        }
                    )
            }

            render() {
                console.log("render");

                if (!this.state.isLoaded) {
                    return 'Loading...';
                }

                console.log(this.state);

                if (this.state.liked) {
                    return 'You liked this.';
                }



/*                return e(
                    'button',
                    { onClick: () => this.setState({ liked: true }) },
                    'Like ' + this.state.item.name
                );*/

                return (
                    <form>
                        <label>
                            Name:
                            <input type="text" />
                        </label>
                        <input type="submit" value="Submit" />
                    </form>
                );
            }
        }

        document.addEventListener("DOMContentLoaded", function(event) {
            console.log("Loaded");

            ReactDOM.render(
                <h1>Hello, world!</h1>,
                document.getElementById('like_button_container')
            );
        })

    </script>
    <script>

        document.addEventListener("DOMContentLoaded", function(event) {
            console.log("Loaded");

            const domContainer = document.querySelector('#like_button_container');
            ReactDOM.render(e(LikeButton), domContainer);

        })

    </script>*}

    <style>
        div.focus {
        color: red;
        }
    </style>
{/block}

{block content}

{var $title = 'Product annotations'}



<div class="container-fluid no-padding">
    <div class="card card-accent-primary">
        <div class="card-header">
            <h1>{$title}</h1>
        </div>

        <div id="product-annotation"></div>

    </div>
</div>

{block title}Product{/block}

{block content}

{var $title = 'Product'}

{ifset $product}
    <div class="container-fluid no-padding">
        <div class="card card-accent-primary">
            <div class="card-header">
                <h1>{$title}</h1>
            </div>

            <div class="card-body">
                {if $product->getImageUrl()}
                    <img src="{$product->getImageUrl()|image:300}" style="max-width: 150px; max-height: 50px;">
                {else}
                    <div class="alert alert-warning" style="margin-bottom: 15px; padding: 3px 12px">
                        No image added for this product.
                    </div>
                {/if}

            {control productControl}
            </div>
        </div>
    </div>
{else}
    <h1>{$title}</h1>
    {control productControl}
{/ifset}
<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Presenters;

use Doctrine\ORM\QueryBuilder;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ICouponControlFactory;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\CouponControl;
use <PERSON><PERSON><PERSON>\AdminModule\Forms\ICouponLeafletPageControlFactory;
use Ka<PERSON>ino\AdminModule\Forms\CouponLeafletPageControl;
use Ka<PERSON>ino\Model\Competitors\CompetitorFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Competitors\Entities\Coupon;
use Nette\Utils\Json;

class CouponPresenter extends BasePresenter
{
	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var CompetitorFacade @inject */
	public $competitorFacade;

	/** @var ICouponControlFactory @inject */
	public $couponControlFactory;

	public function actionCoupon($id = null)
	{
		if ($id) {
			/** @var Coupon $coupon */
			$coupon = $this->competitorFacade->findCoupon($id);

			if ($coupon === null) {
				$this->error('Coupon not found.');
			}

			$this->template->coupon = $coupon;
		}
	}

	public function createComponentCouponsGrid($name)
	{
		$coupons = $this->competitorFacade->getCoupons(null, false);

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $coupons);

		$grid->setRememberState(true);
		$grid->setColumnsHideable();
		$grid->setStrictSessionFilterValues(false);

		$grid->setTemplateFile(__DIR__ . '/templates/Coupon/grid/default.latte');

		$grid->addColumnText('localization', 'Localization')
			->setRenderer(static function (Coupon $coupon) {
				return $coupon->getLocalization()->getName();
			})->setFilterSelect(['' => 'All'] + $this->localizationFacade->findPairs());

		//$grid->addColumnText('id', 'Id');

		$grid->addColumnText('name', 'Name')
			->setEditableCallback(function ($id, $value): void {
				$coupon = $this->competitorFacade->findCoupon($id);
				$coupon->setName($value);
				$this->competitorFacade->saveCoupon($coupon);
			});

		$grid->addColumnText('shop', 'Shop')
			->setRenderer(static function (Coupon $coupon) {
				return $coupon->getShop() ? $coupon->getShop()->getName() : null;
			});
		$grid->addColumnText('shopName', 'Shop name')
			->setRenderer(static function (Coupon $coupon) {
				return sprintf('%s (%s)', $coupon->getShopName(), $coupon->getShopDomain());
			});

		$grid->addColumnText('discountType', 'DisType');
		$grid->addColumnText('discountAmount', 'DisAmount');

		$grid->addColumnText('code', 'Code');

		$grid->addColumnText('validTill', 'Valid till')
			->setRenderer(static function (Coupon $coupon) {
				return $coupon->getValidTill()->format('Y-m-d');
			});

		$grid->addColumnText('closedAt', 'ClosedAt')
			->setRenderer(static function (Coupon $coupon) {
				return $coupon->getClosedAt() ? $coupon->getClosedAt()->format('Y-m-d') : null;
			});
		//$grid->addColumnText('shop_name', 'Shop name');


		$grid->addFilterSelect('closedAt', 'ClosedAt', [
			'waiting' => 'Waiting',
			'closed' => 'Closed',
		])
			->setCondition(static function (QueryBuilder $qb, $value) {
				if ($value == 'waiting') {
					$qb->andWhere('c.closedAt IS NULL');
				}

				if ($value == 'closed') {
					$qb->andWhere('c.closedAt IS NOT NULL');
				}
			});

		$grid->addFilterText('name', 'Name')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('s.name LIKE :name')->setParameter('name', '%' . $value . '%');
			});

		$grid->addAction('coupon', '', 'coupon')
			->setClass('btn btn-xs btn-primary')
			->setIcon('search')
			->setRenderCondition(static function (Coupon $coupon) {
				return !$coupon->getOffer();
			});

		$grid->setItemsPerPageList([10, 50, 100, 200], false);
		$grid->setDefaultSort(['s.name' => 'ASC']);
	}

	protected function createComponentCouponControl(): CouponControl
	{
		$coupon = $this->getParameter('id') ? $this->competitorFacade->findCoupon($this->getParameter('id')) : null;
		$control = $this->couponControlFactory->create($coupon);

		$control->onSuccess[] = function ($entity, $continue) use ($coupon) {
			$this->flashMessage('Successfully saved.');
			if ($coupon && !$continue) {
				$this->redirect(':Admin:Coupon:default');
			} else {
				$this->redirect(':Admin:Coupon:coupon', $entity->getId());
			}
		};

		return $control;
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\TranslationsModule\Presenters;

use <PERSON><PERSON><PERSON>\AdminModule\Presenters\BasePresenter;
use <PERSON><PERSON><PERSON>\AdminModule\TranslationsModule\Forms\DictionaryControl;
use <PERSON><PERSON><PERSON>\AdminModule\TranslationsModule\Forms\IDictionaryControlFactory;
use <PERSON><PERSON><PERSON>\Model\Github\GithubClient;
use Ka<PERSON>ino\Model\Localization\Entities\Localization;
use Ka<PERSON>ino\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Translations\TranslationFacade;

class DictionaryPresenter extends BasePresenter
{
	/** @var IDictionaryControlFactory @inject */
	public $dictionaryControlFactory;

	/** @var TranslationFacade @inject */
	public $translationFacade;

	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	public function startup()
	{
		parent::startup();

		ini_set('max_input_vars', '5000');
	}

	public function renderDictionary($dictionary, $localizationId, $branch, $compareLocale = Localization::LOCALE_AND_REGION_CZECHIA)
	{
		if ($dictionary === 'router') {
			$this->redirect(':Admin:Translations:Translation:default');
		}

		/** @var Localization $localization */
		$localization = $this->localizationFacade->findLocalization($localizationId);
		$branch = $branch ? : GithubClient::DEFAULT_BRANCH;

		$this->template->localizations = $this->localizationFacade->findLocalizations();
		$this->template->branch = $branch;

		$dictionaryData = $this->translationFacade->getDictionaryData($localization, $dictionary, $branch);

		if (empty($dictionaryData)) {
			$this->redirect('Translation:');
		}

		$this->template->localization = $localization;
		$this->template->dictionary = $dictionary;
		$this->template->dictionaryData = $this->translationFacade->convertDictionaryDataToSingleArray($dictionaryData);

		$compareLocalization = $this->localizationFacade->findLocalizationByFullLocale($compareLocale);

		$compareDictionaryData = $this->translationFacade->getDictionaryData($compareLocalization, $dictionary, $branch);

		$this->template->compareDictionaryData = $this->translationFacade->convertDictionaryDataToSingleArray($compareDictionaryData);
		$this->template->compareLocalization = $compareLocalization;
		$this->template->translatingKey = $dictionary . '.' . $localization->getLocale() . '.' . $branch;
		$this->template->showAll = $this->getParameter('showAll');
	}

	public function createComponentDictionaryControl(): DictionaryControl
	{
		$dictionary = $this->getParameter('dictionary');
		/** @var Localization $localization */
		$localization = $this->localizationFacade->findLocalization($this->getParameter('localizationId'));
		$branch = $this->getParameter('branch') ? : GithubClient::DEFAULT_BRANCH;

		$control = $this->dictionaryControlFactory->create($dictionary, $localization, $branch);

		$control->onSuccess[] = function () use ($dictionary, $localization, $branch) {
			$this->getHttpResponse()->deleteCookie($dictionary . '.' . $localization->getLocale() . '.' . $branch);

			$this->flashMessage('Changes have been submitted for approval.');

			$this->redirect('Translation:');
		};

		return $control;
	}
}

{block title}{$localization->getName() . ' dictionary: ' . $dictionary}{/block}

{block content}

<style>
    .spinner {
        position:absolute;
        width:100%;
        height:100%;
        background: #b9b9b9;
        z-index:1000;
        opacity:0.5;
        text-align: center;
        padding:250px 0;
        font-size:50px;
        color:#000;
    }
</style>

<div class="modal fade" id="draft-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Load changes</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Found unsaved concept from: <span id="draft-date"></span>.
                Do you want to load unsaved changes?
            </div>
            <div class="modal-footer">
                <div class="text-center">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" data-dismiss="modal" onclick="loadDraft()">Load changes</button>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="row">
    <div class="col-md-12" n:if="$branch !== master">
        <div class="alert alert-warning" role="alert">
            You are editing the development version (<strong>{$branch}</strong>).
        </div>
    </div>
    <div class="col-md-12">
        {form dictionaryControl-form}
            {foreach $form->errors as $error}
                <div class="alert alert-danger" role="alert">
                    {$error}
                </div>
            {/foreach}

        <div class="card card-accent-primary">
            <div class="spinner">
                <i class="fa fa-spinner fa-spin"></i>
            </div>
            <div class="card-header">
                Dictionary
                &nbsp;&nbsp;<a n:href="Translation:">< back to the list</a>
                <label class="pull-right">
                    <input type="checkbox" name="todo-only-checkbox" {if $branch !== master && (!isset($showAll) || !$showAll)}checked="checked"{/if} /> Show only untranslated phrases
                </label>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2"><strong>Key</strong></div>
                    <div class="col-md-5" n:if="$localization !== $compareLocalization">
                        <strong>original</strong>
                        {if $compareLocalization->isCzech()}(switch to <a n:href="this, compareLocale => en_US">english</a>){/if}
                        {if $compareLocalization->isEnglish()}(switch to <a n:href="this, compareLocale => cs_CZ">czech</a>){/if}
                    </div>
                    <div class="col-md-5"><strong>translation: {$localization->getLocale() |upper} {$dictionary}</strong></div>
                    <div class="col-md-12"><hr /></div>
                </div>
                {dump $dictionaryData}
                {foreach $dictionaryData as $key => $value}
                    <div class="row form-group keyword">
                        <div class="form-label col-md-2">
                            <small>
                                <label style="word-break: break-all">{$key}:</label>
                            </small>
                        </div>
                        <div class="col-md-5" n:if="$localization !== $compareLocalization">
                            {if isset($compareDictionaryData[$key])}
                                {if mb_strlen($compareDictionaryData[$key]) < 90}
                                    <input type="text" class="form-control readonly" value="{$compareDictionaryData[$key]}" readonly="readonly" />
                                {else}
                                    <textarea class="form-control readonly" readonly="readonly">{$compareDictionaryData[$key]}</textarea>
                                {/if}
                            {else}
                                <input type="text" class="form-control readonly" value="- překlad není dostupný -" readonly="readonly" />
                            {/if}
                        </div>
                        <div class="col-md-{$localization === $compareLocalization ? 10 : 5}">
                            {if mb_strlen($value) < 90}
                                <input type="text" class="form-control keyword-input" data-key="{$key}" name="translation[{str_replace(".", "][", $key)}]" value="{$value}" tabindex="{$iterator->counter} "/>
                            {else}
                                <textarea class="form-control keyword-input" data-key="{$key}" name="translation[{str_replace(".", "][", $key)}]" tabindex="{$iterator->counter}">{$value}</textarea>
                            {/if}
                        </div>
                    </div>
                {/foreach}
            </div>
        </div>

        <div class="alert alert-warning" role="alert" n:if="$branch !== master">
            You are editing the development version (<strong>{$branch}</strong>).
        </div>

        <div class="card card-accent-primary">
            <div class="card-body text-center">
                {input submit, class => "btn btn-primary", value => "Save changes"}
            </div>
        </div>
        {/form}
    </div>

</div>

<script>
    var isFormEdited = false;

    $(document).ready(function() {
        var defaultFormValues = $("form").serialize();

        $(".spinner").hide();

        bindTodoOnlyCheckbox();
        showLoadDraftLayer();

        $('textarea').bind("change keyup paste cut", function() {
            $(this).height(0).height(this.scrollHeight);
        }).trigger("keyup");

        $(".keyword-input").bind("keyup", function () {
            isFormEdited = $("form").serialize() !== defaultFormValues;

            if (isFormEdited) {
                $("input[type=submit]").removeAttr("disabled");
            } else {
                $("input[type=submit]").attr("disabled", "disabled");
            }

            if (!isFormEdited) {
                $('form').bind('submit.kaufino', function (e) {
                    e.preventDefault();
                });
            } else {
                $('input[type=submit]', this).removeAttr('disabled', 'disabled');
                $('form').unbind('submit.kaufino');
            }

            if ($(this).prop("defaultValue") !== $(this).val()) {
                $(this).addClass("changed");
            } else {
                $(this).removeClass("changed");
            }

        });

        setTimeout(function() {
            saveDraft();
        }, 15000);
    });

    function bindTodoOnlyCheckbox()
    {
        $("input[name=todo-only-checkbox]").bind("change", function() {
            $(".row.keyword").each(function() {
                var phrase = $(this).find(".keyword-input").val();

                $(this).show();

                if ($("input[name=todo-only-checkbox]").is(":checked") && (!phrase.includes("@todo") || phrase === "")) {
                    $(this).hide();
                }
            });

        }).trigger("change");
    }

    function saveDraft()
    {
        setTimeout(function() {
            saveDraft();
        }, 15000);

        if (!isFormEdited) {
            return;
        }

        var draft = {};

        $(".keyword-input").each(function() {
            if ($(this).prop("defaultValue") !== $(this).val()) {
                var key = $(this).attr("data-key");
                draft[key] = $(this).val();
            }
        });

        localStorage.removeItem({$translatingKey});
        localStorage.setItem({$translatingKey}, JSON.stringify(draft));

        var today = new Date();
        var hours = today.getHours();
        var minutes = today.getMinutes();
        var seconds = today.getSeconds();
        var date = today.getDate() + "." + (today.getMonth()+1) + "." + today.getFullYear() +" " + ("0" + hours).slice(-2) + ":" + ("0" + minutes).slice(-2) + ":" + ("0" + seconds).slice(-2);
        createCookie({$translatingKey}, date, 14);
    }

    function loadDraft()
    {
        var draft = localStorage.getItem({$translatingKey});

        if (draft) {
            draft = JSON.parse(draft);
            var changedInputs = [], input;

            for (const [key, value] of Object.entries(draft)) {
                input = $("input[data-key='" + key + "']");

                input.val(value);
                changedInputs.push(input);
            }

            for (index = 0; index < changedInputs.length; ++index) {
                input = changedInputs[index];
                input.trigger("keyup");
            }
        }
    }

    function showLoadDraftLayer()
    {
        if (readCookie({$translatingKey}) !== null) {
            $("#draft-modal").modal("show");
            $("#draft-date").text(readCookie({$translatingKey}));
        }
    }
</script>

<style>
    .keyword:hover .col-md-6 {
        background:#eee;
    }
    .keyword label {
        padding-top:5px;
    }
    .keyword-input {
        background: #f9f9f9 !important;
    }
    .keyword-input.changed {
        background: #fff !important;
    }

    .form-control.readonly {
        background: #f2f2f2;
    }

    .like-input {
        -moz-appearance: textfield;
        -webkit-appearance: textfield;
        overflow: auto;
    }
</style>

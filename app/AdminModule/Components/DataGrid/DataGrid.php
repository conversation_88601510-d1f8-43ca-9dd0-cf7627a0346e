<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\AdminModule\Components;

use <PERSON><PERSON><PERSON>\Model\Images\ImageFilter;
use Nette\Application\IPresenter;
use Nette\Application\UI\Presenter;
use Nette\Bridges\ApplicationLatte\Template;
use Ublaboo\DataGrid\Localization\SimpleTranslator;

interface IDataGridFactory
{
	public function create(): DataGrid;
}

final class DataGrid
{
	/** @var ImageFilter */
	private $imageFilter;

	public function __construct(ImageFilter $imageFilter)
	{
		$this->imageFilter = $imageFilter;
	}

	public function getGrid(Presenter $presenter, string $name, $source, int $itemsPerPage = 50): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$presenter->addComponent($grid, $name);

		/** @var Template $template */
		$template = $grid->getTemplate();
		$template->addFilter('image', $this->imageFilter);

		$grid->setRememberState(false);
		$grid->setRefreshUrl(false);
		$grid->setItemsPerPageList([10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 200, 300, 500]);
		$grid->setDefaultPerPage($itemsPerPage);

		$translator = new SimpleTranslator([
			'ublaboo_datagrid.no_item_found_reset' => 'Žádné položky nenalezeny. Filtr můžete vynulovat',
			'ublaboo_datagrid.no_item_found' => 'Žádné položky nenalezeny.',
			'ublaboo_datagrid.here' => 'zde',
			'ublaboo_datagrid.items' => 'Položky',
			'ublaboo_datagrid.all' => 'všechny',
			'ublaboo_datagrid.from' => 'z',
			'ublaboo_datagrid.reset_filter' => 'Resetovat filtr',
			'ublaboo_datagrid.group_actions' => 'Hromadné akce',
			'ublaboo_datagrid.show_all_columns' => 'Zobrazit všechny sloupce',
			'ublaboo_datagrid.hide_column' => 'Skrýt sloupec',
			'ublaboo_datagrid.action' => 'Akce',
			'ublaboo_datagrid.previous' => 'Předchozí',
			'ublaboo_datagrid.next' => 'Další',
			'ublaboo_datagrid.choose' => 'Vyberte',
			'ublaboo_datagrid.execute' => 'Provést',
			'ublaboo_datagrid.save' => 'Uložit',
			'ublaboo_datagrid.cancel' => 'Zrušit',
		]);

		$grid->setTranslator($translator);
		$grid->setDataSource($source);

		return $grid;
	}
}

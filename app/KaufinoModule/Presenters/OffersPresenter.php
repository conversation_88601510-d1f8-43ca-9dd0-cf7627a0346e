<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON>ino\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON>ino\Model\Shops\ContentBlockFacade;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Kaufino\Model\Shops\ShopFacade;
use Kaufino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Websites\Entities\Website;
use Nette\Http\IResponse;

final class OffersPresenter extends BasePresenter
{
	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ContentBlockFacade @inject */
	public $contentBlockFacade;

	public function actionOffers(): void
	{
		$offerTags = $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 1000);

		$this->template->offersTags = $offerTags;

		$offers = $this->offerFacade->findTopOffersByTags($this->localization, $offerTags);

		$offerTagsAlphabetically = [];

		foreach ($offerTags as $tag) {
			$offerTagsAlphabetically[mb_substr($tag->getName(), 0, 1)][] = $tag;
		}

		ksort($offerTagsAlphabetically);

		$this->template->offerTagsAlphabetically = $offerTagsAlphabetically;
		$this->template->offers = $offers;
	}

	public function renderTag(Tag $tag)
	{
		if ($tag->isActiveKaufino() === false) {
			$this->error('', IResponse::S410_Gone);
		}

		$offers = $tag->getMatchRule() ? $this->offerFacade->findCurrentOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), Website::MODULE_KAUFINO) : [];
		$futureOffers = $tag->getMatchRule() ? $this->offerFacade->findFutureOffersByFulltext($tag->getMatchRule(), $tag->getLocalization(), Website::MODULE_KAUFINO) : [];

		$leafletPagesFromOffers = array_map(static function (Offer $offer) {
			return $offer->getLeafletPage();
		}, $offers);

		$leafletPages = $tag->getMatchRule() ? $this->leafletFacade->findLeafletPagesByFulltext($tag->getMatchRule(), $tag->getLocalization(), $this->website->getModule(), 25, $tag, false) : [];

		$this->template->leafletPages = array_filter($leafletPages, static function ($leafletPage) use ($leafletPagesFromOffers) {
			return !in_array($leafletPage, $leafletPagesFromOffers, true);
		});

		$this->template->tag = $tag;
		$this->template->currentOffers = $offers;
		$this->template->futureOffers = $futureOffers;

		if ($this->localization->isCzech()) {
			$this->template->avgPriceFromOffers = $this->offerFacade->findAvgPriceFromOffers($tag->getMatchRule(), $tag->getLocalization(), Website::MODULE_KAUFINO);
			$this->template->avgDiscountFromOffers = $this->offerFacade->findAvgPriceByFulltext($tag->getMatchRule(), $tag->getLocalization(), Website::MODULE_KAUFINO);
			$this->template->countOfOffersByShop = $this->offerFacade->findCountOfOffersByShop($tag->getMatchRule(), $tag->getLocalization(), Website::MODULE_KAUFINO);
		} else {
			$this->template->priceHistory = [];
			$this->template->avgDiscountFromOffers = [];
			$this->template->countOfOffersByShop = [];
		}

		$this->template->childTags = $this->tagFacade->findTagsbyTag($tag, 100);

		$this->template->bestOffer = $this->findBestOffer($offers);

		$this->template->frequentOfferShops = $this->getMostFrequentShops($offers);

		$this->template->name = $tag->getName();

		$contentBlocks = $this->contentBlockFacade->findContentBlocksByTag($tag, Website::MODULE_KAUFINO);
		$this->template->contentBlocks = $contentBlocks;

		$this->template->faqContentBlocks = $this->contentBlockFacade->findFaqContentBlocksForTag($tag, Website::MODULE_KAUFINO);

		$countOfOffers = count($offers);
		$this->template->offersByShops = [];

		$offersByShops = [];

		$shops = array_map(static function (Offer $offer) {
			return $offer->getShop();
		}, $offers);

		$shops = array_values(array_unique($shops, SORT_REGULAR));

		usort($shops, static function ($a, $b) {
			return $b->getPriorityLeaflets() <=> $a->getPriorityLeaflets();
		});

		foreach ($shops as $shop) {
			$offersByShops[$shop->getId()] = array_filter($offers, static function (Offer $offer) use ($shop) {
				return $offer->getShop()->getId() === $shop->getId();
			});
		}

		$oneOfferPerShop = array_map(static function ($offers) {
			return reset($offers);
		}, $offersByShops);

		$this->template->oneOfferPerShop = $oneOfferPerShop;

		$this->template->shops = $shops;
		$this->template->offersByShops = $offersByShops;

		$this->template->countOfOffers = $countOfOffers;

		$this->template->contentParams = [];

		$this->responseCacheTags[] = 'tag/' . $tag->getId();
	}

	private function findBestOffer($offers): ?Offer
	{
		$bestOffer = null;
		foreach ($offers as $offer) {
			if ($offer->getCurrentPrice() === 0 || $offer->getCurrentPrice() === null) {
				continue;
			}

			if ($bestOffer === null || $offer->getCurrentPrice() < $bestOffer->getCurrentPrice()) {
				$bestOffer = $offer;
			}
		}

		return $bestOffer;
	}

	private function getMostFrequentShops(array $entities): array
	{
		foreach ($entities as $entity) {
			$shops[$entity->getShop()->getId()] = $entity->getShop();
		}

		return $shops ?? [];
	}
}

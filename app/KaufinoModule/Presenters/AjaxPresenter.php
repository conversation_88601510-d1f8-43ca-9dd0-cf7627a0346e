<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;
use Nette;

class AjaxPresenter extends BasePresenter
{
	public function actionSearch($search = null)
	{
		$shops = $this->shopFacade->findLeafletShopsByFulltext($this->localization, $search, empty($search), 10, Website::MODULE_KAUFINO);

		$results = [];
		foreach ($shops as $shop) {
			if (!$shop->getLogoUrl()) {
				continue;
			}

			$results[] = [
				'id' => $shop->getId(),
				'name' => $shop->getName(),
				'logoUrl' => $shop->getLogoUrl(),
				'destinationUrl' => $this->link('//Shop:shop', ['shop' => $shop]),
			];
		}

		$this->sendResponse(
			new Nette\Application\Responses\JsonResponse($results)
		);
	}
}

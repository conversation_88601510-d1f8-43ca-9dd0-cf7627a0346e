<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoModule\Presenters;

use Ka<PERSON>ino\Model\Articles\ArticleFacade;
use Ka<PERSON>ino\Model\Articles\Entities\Article;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use Kaufino\Model\Offers\OfferFacade;
use Kaufino\Model\Tags\Entities\Tag;
use Kaufino\Model\Users\Entities\User;
use Kaufino\Model\Users\UserFacade;

final class ArticlesPresenter extends BasePresenter
{
	/** @var ArticleFacade @inject */
	public $articleFacade;

	/** @var UserFacade @inject */
	public $userFacade;

	public function actionArticles(): void
	{
		if ($this->localization->hasArticles() === false) {
			$this->redirect(':Kaufino:Homepage:default');
		}

		$this->responseCacheTags[] = 'articles';

		$this->template->articles = $this->articleFacade->findArticles($this->website, 20);
	}

	public function renderAuthor(User $author)
	{
		$this->template->author = $author;
		$this->template->articles = $this->articleFacade->findArticlesByAuthor($author, $this->website, 20);
	}
}

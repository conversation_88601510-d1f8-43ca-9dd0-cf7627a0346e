<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON>ino\Model\Shops\Entities\Shop;

final class ExitPresenter extends BasePresenter
{
	public function actionShop(Shop $shop): void
	{
		$exitUrl = $shop->getDomain();

		$this->getHttpResponse()
			->addHeader('X-Robots-Tag', 'noindex, nofollow');

		$this->redirectUrl($exitUrl);
	}

	public function actionOffer(Offer $offer): void
	{
		$exitUrl = $offer->getExitUrl() ? $offer->getExitUrl() : $offer->getShop()->getDomain();

		$this->getHttpResponse()
			->addHeader('X-Robots-Tag', 'noindex, nofollow');

		$this->redirectUrl($exitUrl);
	}

	public function actionUrl($url): void
	{
	}
}

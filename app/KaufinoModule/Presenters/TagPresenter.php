<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\KaufinoModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Shops\ContentBlockFacade;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Kaufino\Model\Offers\OfferFacade;
use Kaufino\Model\Tags\Entities\Tag;
use Kaufino\Model\Websites\Entities\Website;

final class TagPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var GeoFacade @inject */
	public $geoFacade;

	/** @var ContentBlockFacade @inject */
	public $contentBlockFacade;

	public function actionTag(Tag $tag): void
	{
		if ($tag->isActive() === false && ($tag->getActiveTill() === null || $tag->getActiveTill() < new \DateTime())) {
			$this->redirectPermanent("Homepage:default");
		}

		if ($tag->isShopsType()) {
			$this->prepareTagShops($tag);
		}

		if ($tag->isOffersType()) {
			$this->redirectPermanent('Offers:tag', ['tag' => $tag]);
		}

		$this->responseCacheTags[] = 'tag/' . $tag->getId();

		$this->template->tag = $tag;
	}

	private function prepareTagShops(Tag $tag): void
	{
		$this->setView('tagShops');

		$shops = $this->shopFacade->findLeafletShopsByTag($tag, true, 100, Website::MODULE_KAUFINO);

		$leaflets = $this->leafletFacade->findLeafletsByTag($tag, false, 60, Website::MODULE_KAUFINO);

		if (count($leaflets) < 60) {
			$leaflets = array_merge($leaflets, $this->leafletFacade->findLeafletsByShops($shops, 60, true, $leaflets, Website::MODULE_KAUFINO));
		}

		$this->template->leaflets = array_slice($leaflets, 0, 60);
		$this->template->shops = $shops;

		$this->template->cities = $shops ? $this->geoFacade->findCitiesByShops($shops, 100) : [];
	}
}

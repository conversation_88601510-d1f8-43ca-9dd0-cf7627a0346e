{capture $shopsInText}{var $items = ($shops|slice: 0, 8)}{foreach $items as $_shop}{last}{if count($items) > 1} {_kaufino.homepage.and} {/if}{/last}<a n:href="Shop:shop $_shop">{$_shop->getName()}</a>{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}
{capture $shopsInTextMetaDesc}{var $items = ($shops|slice: 0, 8)}{foreach $items as $_shop}{last}{if count($items) > 1} {_kaufino.homepage.and} {/if}{/last}{$_shop->getName()}{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}


{block scripts}
    {include parent}

    <script defer src="{$basePath}/js/swiper/swiper-bundle.min.js"></script>
    <script defer src="{$basePath}/js/swiper.js"></script>
{/block}

{block hreflang}
    {var $otherWebsites = $footerWebsites()}

    {foreach $otherWebsites as $otherWebsite}
        {var $localizedUrl = $getLocalizedUrl($otherWebsite)}
        <link rel="alternate" n:attr="hreflang: $otherWebsite->getLocalization()->getFullLocale('-')" href={$localizedUrl} />
    {/foreach}
{/block}

{block title}{_kaufino.homepage.metaTitle}{/block}
{block description}{_kaufino.homepage.metaDescription, [shopsInText => $shopsInTextMetaDesc]}{/block}

{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Homepage',
            'country' : {{$localization->getRegion()}}
        });
    </script>        
{/block}

{block content}
<div class="container">
    <div class="swiper k-hp-swiper">
        <div class="swiper-wrapper">
            {foreach $shops as $shop}
                <a n:href="Shop:shop $shop" title="{_kaufino.shop.storeLeaflet, [brand => $shop->getName()]}" class="swiper-slide">                    
                    <picture class="img-wrapper">                        
                        <source 
                            srcset="
                                {$shop->getLogoUrl() |image:80,70,'fit','webp'} 1x,
                                {$shop->getLogoUrl() |image:160,140,'fit','webp'} 2x
                            " 
                            type="image/webp"
                        >                                    
                        <img 
                            src="{$shop->getLogoUrl() |image:80,70,'fit','png'}" 
                            srcset="
                                {$shop->getLogoUrl() |image:80,70,'fit','png'} 1x,
                                {$shop->getLogoUrl() |image:160,140,'fit','png'} 2x
                            "
                            data-sizes="auto"
                            width="80" 
                            height="70" 
                            alt="{$shop->getName()}" 
                            class=""
                            loading="lazy"
                        >
                    </picture>   
                    
                </a>
            {/foreach}
        </div>
        <div class="swiper-pagination"></div>
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>
    </div>

    {*
    <div class="k-shop">
        {foreach $shops as $shop}
            {include '../components/shop-logo.latte', shop => $shop}
        {/foreach}
    </div>    
    *}

    <h1 class="k__title mt-4 mt-sm-5 mb-4">        
        {_kaufino.homepage.leaflets}        
    </h1>

    <div class="k-leaflets__wrapper  k-leaflets__wrapper--xs-mx">
        {foreach $leaflets as $leaflet}
            {include '../components/leaflet.latte', leaflet => $leaflet, validBadgeShow => false}
        {/foreach}
    </div>

    <div class="d-flex mt-3">
        <a href="{link Leaflets:leaflets}" class="link ml-auto k-show-more-button">
            {_kaufino.homepage.allLeaflets} »        
        </a>
    </div>    

    <div n:if="count($offersTags) > 0">        
        <h2 class="k__title ">            
            {_kaufino.homepage.offersCategory}             
        </h2>             

        <div class="k-tag mb-3">
            {foreach $offersTags as $tag}
                <div class="k-tag__inner">
                    <a n:href="Offers:tag $tag" class="k-tag__item">{$tag->getName()}</a>
                </div>
            {/foreach}            
        </div>

        <div class="d-flex mt-3 mb-5">            
            <a n:href="Offers:offers" class="link ml-auto k-show-more-button">
               {_'kaufino.showMore.tags'} »
            </a>
        </div>
    </div>

    <div n:if="count($topOffers) > 0">
        <h2 class="k__title mt-4 mt-sm-5 mb-4">{_kaufino.offer.whatOnSale}</h2>

        <div class="k-offers">
            {foreach $topOffers as $offer}
                {breakIf $iterator->getCounter() > 15}
                {include '../components/offer-item.latte', offer => $offer}
            {/foreach}
        </div>

        <div class="d-flex mt-3 mb-5">
            <a n:href="Offers:offers#offers" class="link ml-auto k-show-more-button">
                {_'kaufino.showMore.offers'} »
            </a>
        </div>
    </div>

    <div n:if="count($cities) > 0">
        <h2 class="k__title">
            {_kaufino.homepage.city}                        
        </h2>             

        <div class="k-tag mb-3">
            {foreach $cities as $city}
                <div class="k-tag__inner">
                    <a n:href="City:city $city" class="k-tag__item">{$city->getName()}</a>
                </div>
            {/foreach}            
        </div>

        <div class="d-flex mt-3 mb-5">            
            <a n:href="Cities:cities" class="link ml-auto k-show-more-button">
                {_'kaufino.showMore.cities'} »
            </a>
        </div>
    </div>
    
    <div class="k-content">            
        <h2>{_kaufino.homepage.bottomText.mainTitle}</h2>
        
        <h3>{_kaufino.homepage.bottomText.section1.title}</h3>
        <p>{_kaufino.homepage.bottomText.section1.text|noescape}</p>

        <h3>{_kaufino.homepage.bottomText.section2.title}</h3>
        <p>{_kaufino.homepage.bottomText.section2.text|noescape}</p>

        <h3>{_kaufino.homepage.bottomText.section3.title}</h3>
        <p>{_kaufino.homepage.bottomText.section3.text|noescape}</p>
        
        <h3>{_kaufino.homepage.bottomText.section4.title}</h3>
        <p>{_kaufino.homepage.bottomText.section4.text|noescape}</p>
        <p>{_kaufino.homepage.bottomText.section4.text2|noescape}</p>                
    </div>    
</div>

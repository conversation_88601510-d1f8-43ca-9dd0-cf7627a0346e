{block scripts}
    {include parent}
{/block}

{block robots}noindex, follow{/block}

{block title}{_kaufino.leaflets.expiredMetaTitle, [brand: $shop->getName()]}{/block}
{block description}{_kaufino.leaflets.expiredText}{/block}

{block content}
<div class="container">
    <div class="mb-6">
        <h1 class="k__title ta-center mt-5 mb-4">{_kaufino.leaflets.expiredTitle}</h1>
        <p class="k__text ta-center mw-700 mb-0">{_kaufino.leaflets.expiredText}</p>
    </div>

    <div class="k-leaflets__wrapper k-leaflets__wrapper--xs-mx">
        {foreach $leaflets as $leaflet}
            {include '../components/leaflet.latte', leaflet => $leaflet, validBadgeShow => true}
        {/foreach}
    </div>
</div>

{block head}
    {include parent}
    <script n:syntax="double">
        window.dataLayer.push({
            'content_group' : 'TagOffers',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {elseif $bestOffer}
        {_kaufino.tag.offers.titleWithBestOffer, [tag => $tag->getName(), 'price' => ($bestOffer->getCurrentPrice()|price:$bestOffer->getLocalization())]}
    {else}
        {_kaufino.tag.offers.title, [tag => $tag->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {elseif $bestOffer}
        {_kaufino.tag.offers.metaDescriptionWithBestOffer, [tag => $tag->getName()]|noescape}
    {else}
        {_kaufino.tag.offers.metaDescription, [tag => $tag->getName()]|noescape}
    {/if}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container">
        <p class="k-breadcrumb">
            <a n:href="Offers:offers" class="link">{_kaufino.navbar.offers}</a> |
            {if $tag->getParentTag() && $tag->getParentTag()->isActive()}<a n:href="Offers:tag $tag->getParentTag()" class="link">{$tag->getParentTag()->getName()}</a> |{/if}
            <span class="color-grey">{$tag->getName()}</span>
        </p>
    </div>
{/block}

{block scripts}
    {include parent}

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
            {
                    "@type": "ListItem",
                    "position": 1,
                    "name": {_kaufino.navbar.home},
                "item": {link //Homepage:default}
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": {_kaufino.navbar.offers},
                "item": {link //Offers:offers}
            },
{if $tag->getParentTag() && $tag->getParentTag()->isActive()}
            {
                "@type": "ListItem",
                "position": 3,
                "name": {$tag->getParentTag()->getName()},
                "item": {link //Offers:tag $tag->getParentTag()}
            },
{/if}
            {
                "@type": "ListItem",
                "position": {$tag->getParentTag() ? 4 : 3},
                "name": {$tag->getName()},
                "item": {link //Offers:tag $tag}
        }
    ]
  }
    </script>

    <script n:if="$faqContentBlocks" type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
        {foreach $faqContentBlocks as $faq} {
                    "@type": "Question",
                    "name": {$faq->getHeading()},
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": {strip_tags($faq->getContent())}
                    }
                }{sep},{/sep}
        {/foreach}
        ]
    }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script>

    {dump $avgPriceFromOffers}
    {dump $avgDiscountFromOffers}
    {dump $countOfOffersByShop}

    <script n:syntax="double">
        const priceHistoryData = {{$avgPriceFromOffers}};
        const avgDiscountFromOffers = {{$avgDiscountFromOffers}};
        const countOfOffersByShop = {{$countOfOffersByShop}};
        
        const labelMonth = {{_kaufino.offer.graphLabel.month}};
        const labelPrice = {{_kaufino.offer.graphLabel.price}};        
        const labelCurrency = {{_kaufino.offer.graphLabel.currency}};        
        const labelDiscount = {{_kaufino.offer.graphLabel.discount}};
        const labelOffers = {{_kaufino.offer.graphLabel.offers}};
    </script>    

    <script n:if="$avgPriceFromOffers" n:syntax="off">
        document.addEventListener('DOMContentLoaded', function () {
        const ctx = document.getElementById('priceChart').getContext('2d');

        const labels = priceHistoryData.map(p => p.month);
        const prices = priceHistoryData.map(p => p.avgPrice);

        const offerTotals = labels.map(month =>
            countOfOffersByShop
            .filter(o => o.month === month)
            .reduce((sum, o) => sum + o.count, 0)
        );

        const avgDiscountMap = {};
        avgDiscountFromOffers.forEach(d => {
            avgDiscountMap[d.month] = d.avgDiscount;
        });

        const shopBreakdown = {};
        labels.forEach(month => {
            const offers = countOfOffersByShop.filter(o => o.month === month);
            shopBreakdown[month] = offers.map(o => `${o.shopName}: ${o.count}`);
        });

        new Chart(ctx, {
            type: 'bar',
            data: {
            labels: labels,
            datasets: [
                {
                type: 'line',
                label: labelPrice + ' (' + labelCurrency + ')',
                data: prices,
                yAxisID: 'y',
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.3,
                pointRadius: 4,
                fill: false
                },
                {
                type: 'bar',
                label: labelOffers,
                data: offerTotals,
                yAxisID: 'y1',
                backgroundColor: 'rgba(54, 162, 235, 0.5)'
                }
            ]
            },
            options: {
            responsive: true,
            layout: {
                padding: 20
            },
            plugins: {
                legend: {
                labels: {
                    font: { size: 14 }
                }
                },
                tooltip: {
                callbacks: {
                    afterBody: (tooltipItems) => {
                    const month = tooltipItems[0].label;
                    const discount = avgDiscountMap[month];                    
                    return [
                        discount ? labelDiscount + `: ${discount.toFixed(0)} %` : ''                        
                    ].filter(Boolean);
                    }
                }
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            },
            scales: {
                y: {
                type: 'linear',
                position: 'left',
                title: {
                    display: true,
                    text: labelPrice + ' (' + labelCurrency + ')'
                },
                beginAtZero: false
                },
                y1: {
                type: 'linear',
                position: 'right',
                title: {
                    display: true,
                    text: labelOffers
                },
                beginAtZero: true,
                grid: {
                    drawOnChartArea: false
                }
                },
                x: {
                title: {
                    display: true,
                    text: labelMonth
                }
                }
            }
            }
        });
        });
    </script>
{/block}

{block content}
{var $currentOffersContent = null}
{if $oneOfferPerShop}
    {capture $currentOffersContent |strip}
        <ul>
            {foreach $oneOfferPerShop as $offer}
                {breakIf $iterator->getCounter() > 3}
                {var $offerLeaflet = $offer->getLeafletPage()->getLeaflet()}
                {var $pageNumber = $offer->getLeafletPage()->getPageNumber()}
                <li>
                    <a n:href="Leaflet:leaflet#p-$pageNumber shop => $offerLeaflet->getShop(), leaflet => $offerLeaflet">
                        {_kaufino.tag.offers.currentLeaflet, ['brand' => $offer->getShop()->getName()]}
                    </a>
                </li>
            {/foreach}
        </ul>
    {/capture}
{/if}

{var $contentParams['currentShops'][] = $currentOffersContent}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container">
        <div class="leaflet__content">
            <div class="w100">
                <div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
                    <div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            <span n:tag="$countOfOffers <= 5 ? 'a'" {if $countOfOffers <= 5}href="#offers"{/if}>
                                {if $pageExtension && $pageExtension->getHeading()}
                                    {$pageExtension->getHeading()}
                                {else}
                                    {_kaufino.tag.offers.title, [tag => $tag->getName()]}
                                {/if}
                            </span>
                        </h1>
                        <p class="page-header__text ml-0">
                            {if $pageExtension && $pageExtension->getDescription()}
                                {$pageExtension->getDescription()}
                            {elseif $localization->isCzech() && $bestOffer}
                                {capture $shopsInText}{var $items = ($frequentOfferShops|slice: 0, 3)}{foreach $items as $_shop}{last}{if count($items) > 1} {_kaufino.homepage.and} {/if}{/last}<a n:href="Shop:shop $_shop">{$_shop->getName()}</a>{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}
                                Hledáte nejlevnější {$tag->getName()} v akci? Podívejte se na aktuální nabídky z letáků obchodů, jako jsou {$shopsInText}. Tento týden můžete pořídit {$tag->getName()} v akci od {$bestOffer->getCurrentPrice()|price:$bestOffer->getLocalization()}.
                            {else}
                                {_kaufino.tag.offers.text, [tag => $tag->getName(), leafletLink => $presenter->link('Leaflets:leaflets')]|noescape}
                            {/if}
                        </p>
                    </div>
                </div>

                <div n:if="$childTags" class="k-tag k-tag--in-content k-tag--4 mb-5">
                    {foreach $childTags as $childTag}
                        <div class="k-tag__inner">
                            <a n:href="Offers:tag $childTag" class="k-tag__item">{$childTag->getName()}</a>
                        </div>
                    {/foreach}
                </div>

                {if false && $user->isLoggedIn()}
                    <div class="k-shop">
                        {foreach $shops as $shop}
                            <a  n:tag="$countOfOffers <= 5 ? 'span'" {if $countOfOffers > 5}href="#{_kaufino.tag.offers.sectionAnchor, [slug => $shop->getSlug()]}"{/if} class="k-shop__item">
                                    <span class="k-shop__image-wrapper">
                                        <picture>
                                            <source
                                                    srcset="
                                                    {$shop->getLogoUrl() |image:80,70,'fit','webp'} 1x,
                                                    {$shop->getLogoUrl() |image:160,140,'fit','webp'} 2x
                                                "
                                                    type="image/webp"
                                            >
                                            <img
                                                    src="{$shop->getLogoUrl() |image:80,70,'fit','png'}"
                                                    srcset="
                                                    {$shop->getLogoUrl() |image:80,70,'fit','png'} 1x,
                                                    {$shop->getLogoUrl() |image:160,140,'fit','png'} 2x
                                                "
                                                    data-sizes="auto"
                                                    width="80"
                                                    height="70"
                                                    alt="{$shop->getName()}"
                                                    class=""
                                                    loading="lazy"
                                            >
                                        </picture>
                                    </span>
                                {_kaufino.tag.offers.shopCountOfOffers, [count => count($offersByShops[$shop->getId()]), shop => $shop->getName()]}
                            </a>
                        {/foreach}
                    </div>

                    {if $countOfOffers > 5}
                        {foreach $shops as $shop}
                            <div id="{_kaufino.tag.offers.sectionAnchor, [slug => $shop->getSlug()]}">
                                {_kaufino.tag.offers.shopWithOffers, [tag => $tag->getName(), shop => $shop->getName()]}

                                <div class="k-offers">
                                    {foreach $offersByShops[$shop->getId()] as $offer}
                                        {include '../components/offer-item.latte', offer => $offer, hideTags => true}
                                    {/foreach}
                                </div>
                            </div>
                        {/foreach}
                    {else}
                        <div id="offers" n:if="$countOfOffers > 0">
                            <div class="k-offers">
                                {foreach $currentOffers as $offer}
                                    {if false && $iterator->counter == 1}
                                        <div class="k-leaflets__item k-leaflets__item--first mb-3">
                                            <!-- Vypis tagu - Responsive - 3 -->
                                            <ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="1919083451" data-ad-format="auto" data-full-width-responsive="true"></ins>

                                            <script>
                                                (adsbygoogle = window.adsbygoogle || []).push({});
                                            </script>
                                        </div>
                                    {/if}

                                    {include '../components/offer-item.latte', offer => $offer, hideTags => true}
                                {/foreach}
                            </div>
                        </div>
                    {/if}
                {else}
                    <div n:if="count($currentOffers) > 0">
                        <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.offer.currentOffers, ['name' => $tag->getName()]}</h2>
                        <div class="k-offers">
                            {foreach $currentOffers as $offer}
                                {if false && $iterator->counter == 1}
                                    <div class="k-leaflets__item k-leaflets__item--first mb-3">
                                        <!-- Vypis tagu - Responsive - 3 -->
                                        <ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="1919083451" data-ad-format="auto" data-full-width-responsive="true"></ins>

                                        <script>
                                            (adsbygoogle = window.adsbygoogle || []).push({});
                                        </script>
                                    </div>
                                {/if}

                                {include '../components/offer-item.latte', offer => $offer, hideTags => true}
                            {/foreach}
                        </div>
                    </div>
                {/if}

                <div n:if="count($futureOffers) > 0">
                        <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.offer.futureOffers, ['name' => $tag->getName()]}</h2>
                        <div class="k-offers">
                            {foreach $futureOffers as $offer}
                                {include '../components/offer-item.latte', offer => $offer, hideTags => true}
                            {/foreach}
                        </div>
                </div>

                {*
                <!-- Vypis offers - Responsive - 1 -->
                <ins
                    class="adsbygoogle"
                    style="display:block"
                    data-ad-client="ca-pub-4233432057183172"
                    data-ad-slot="1780563915"
                    data-ad-format="auto"
                    data-full-width-responsive="true">
                </ins>

                <script>
                    (adsbygoogle = window.adsbygoogle || []).push({});
                </script>
                *}

                {if count($leafletPages) > 0}
                    <h2 n:if="count($currentOffers) > 0" class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offersLeaflets, [category => $tag->getName()]}</h2>
                    <div class="k-leaflets__wrapper k-leaflets__wrapper--scroll-x">
                        {foreach $leafletPages as $leafletPage}
                            {include '../components/leafletPage.latte', leafletPage => $leafletPage, tagId: $tag->getId()}
                        {/foreach}
                    </div>
                {else}
                    {*<div class="alert alert-info mx-3">{_kaufino.tag.noLeaflets}</div>*}
                {/if}

                {if $avgPriceFromOffers}
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.offer.priceHistory, [name => $tag->getName()]}</h2>
                    <canvas id="priceChart" style="height:40vh; max-width: 100%; margin: 2rem auto; display: block;"></canvas>
                {/if}


                <div class="k-content">
                        {if $contentBlocks}
                            {foreach $contentBlocks as $contentBlock}
                                {continueIf $contentBlock->isFaqType()}
                                {continueIf $contentBlock->getType() === 'legacy_description'}
                                {continueIf $contentBlock->isArchived()}

                                <div class="k-content k__text mw-900 mb-0 pb-0 ml-0" n:if="$contentBlock->getContent()">
                                    <h2 n:if="$contentBlock->getheading()">{$contentBlock->getHeading()}</h2>

                                    {$contentBlock->getContent() |content: $contentParams|noescape}
                                </div>
                            {/foreach}
                        {else}
                            {$tag->getDescription() |content: $contentParams|noescape}
                        {/if}
                </div>

                {if $faqContentBlocks}
                    <div class="faq-section mb-5 mt-3">
                        {* <div class=faq-section-title>Frequetly asked questions about Checkers</div> *}

                        {foreach $faqContentBlocks as $contentBlock}
                            {continueIf !$contentBlock->getHeading() || !$contentBlock->getContent()}

                            <div class="faq-question">
                                <h2 class="faq-title">
                                    <div>{$contentBlock->getHeading()}</div>
                                    <svg class="arrow" width="22" height="12" viewBox="0 0 22 12" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.9823 11.5239C10.8079 11.5239 10.6374 11.4905 10.4708 11.4239C10.3041 11.3572 10.1502 11.2534 10.0092 11.1124L0.916901 2.02001C0.778434 1.88155 0.711135 1.6963 0.715001 1.46426C0.718835 1.2322 0.789985 1.04693 0.928452 0.908462C1.10795 0.728995 1.2932 0.645029 1.4842 0.656562C1.67523 0.668096 1.85665 0.755913 2.02845 0.920013L10.9823 9.87386L19.9362 0.920013C20.0746 0.781546 20.2535 0.702062 20.4727 0.681562C20.6919 0.661062 20.8836 0.740546 21.0477 0.920013C21.2272 1.08411 21.3047 1.26745 21.2804 1.47001C21.256 1.67258 21.1746 1.85976 21.0362 2.03156L11.9554 11.1124C11.8144 11.2534 11.6669 11.3572 11.5131 11.4239C11.3592 11.4905 11.1823 11.5239 10.9823 11.5239Z"/>
                                    </svg>
                                </h2>
                                <div class="faq-answer">
                                    {$contentBlock->getContent() |content|noescape}
                                </div>
                            </div>
                        {/foreach}
                    </div>
                {/if}
            </div>

        </div>

        <div class="float-wrapper__stop"></div>
    </div>

    <script type="application/ld+json" n:if="count($currentOffers)">
        {
            "@context": "http://schema.org",
            "itemListElement": [
        {foreach $currentOffers as $offer}
                {
                    "endDate": {$offer->getValidTill()->format('Y-m-d')},
                    "startDate": {$offer->getValidSince()->format('Y-m-d')},
                    "location": {
                        "address": {
                            "name": {$offer->getShop()->getName()},
                            "@type": "PostalAddress"
                        },
                        "url": {link //:Kaufino:Shop:shop $offer->getShop()},
                        "image": {$offer->getShop()->getLogoUrl() |image:160,140,'fit','webp'},
                        "name": {$offer->getShop()->getName()},
                        "@type": "Place"
                    },
                    "performer": {
                        "name": {$offer->getShop()->getName()},
                        "@type": "Organization"
                    },
                    "image": {$offer->getImageUrl()|image:150,150,'fit','webp'},
                    "name": {$offer->getName()},
                    "url": {link //:Kaufino:Offers:tag, $tag},
                    "description": "",
                    "eventAttendanceMode": ["https://schema.org/OfflineEventAttendanceMode"],
                    "eventStatus": "https://schema.org/EventScheduled",
                    "organizer": {
                        "@type": "Organization",
                        "name": {$offer->getShop()->getName()},
                        "url": {link //:Kaufino:Shop:shop $offer->getShop()}
                    },
                    "@type": "SaleEvent"
                }{sep},{/sep}
        {/foreach}
        ],
        "@type": "OfferCatalog"
    }
    </script>
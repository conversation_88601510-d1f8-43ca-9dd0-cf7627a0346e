{* Kupon modal *}
<div class="k-modal show">
    <div class="k-modal__container">
        <button class="k-modal__close"></button>
        <div class="k-coupon">
            <div class="k-coupon__box">
                <strong class="k-coupon__box-value">{$coupon->getDiscountAmount()}{if $coupon->getDiscountType() == relative}%{/if}</strong>
                <small class="k-coupon__box-type">{_kaufino.coupon.type.sale}</small>
            </div>

            <div class="k-coupon__content">
                <small class="d-block mb-2">{$coupon->getShop()->getName()}: {$coupon->getValidTill()|localDate:'long'}</small>
                <h3 class="mt-0 mb-2"><a n:href="Exit:offer $coupon" class="color-black">{$coupon->getName()}</a></h3>
                {$coupon->getDescription()|noescape}
            </div>
        </div>
        <div class="k-modal__content">
            <p class="ta-center mb-4">
                {_kaufino.coupon.copyText, [brand => $coupon->getShop()->getName()]|noescape}
            </p>
            <div class="k-modal__code-copy">
                <div class="p-relative">
                    <input id="copyInput" type="text" class="k-modal__code-input" value="{$coupon->getCode()}" readonly="readonly">
                    <span class="k-modal__copied">{_kaufino.coupon.copied}</span>
                </div>
                <span class="k-modal__code-btn">
                    {_kaufino.coupon.copy}
                </span>
            </div>

            <a n:href="Exit:offer $coupon" class="k-modal__btn">
                {_kaufino.coupon.button, [brand => $coupon->getShop()->getName()]}
            </a>
        </div>
    </div>
</div>

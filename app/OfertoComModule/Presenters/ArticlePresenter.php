<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Articles\ArticleFacade;
use <PERSON><PERSON><PERSON>\Model\Articles\Entities\Article;

final class ArticlePresenter extends BasePresenter
{
	/** @var ArticleFacade @inject */
	public $articleFacade;

	public function actionArticle(Article $article): void
	{
		if (!$article->isActive()) {
			$this->redirectPermanent(':OfertoCom:Homepage:default');
		}

		$this->template->article = $article;
		$this->template->articles = $this->articleFacade->findArticles($this->website, 10);
		$this->template->previousArticle = $this->articleFacade->findPreviousArticle($article);
		$this->template->nextArticle = $this->articleFacade->findNextArticle($article);
	}
}

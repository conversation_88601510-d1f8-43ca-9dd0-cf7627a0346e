<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class HomepagePresenter extends BasePresenter
{
	/** @var OfferFacade @inject */
	public $offerFacade;

	/** @var LeafletFacade @inject */
	public $leafletFacade;

	public function renderDefault(): void
	{
		$this->template->offers = $this->offerFacade->findTopCoupons($this->localization, 8);

		$this->template->shops = $this->shopFacade->findTopCouponShops($this->localization, 18);

		$this->template->leaflets = $this->leafletFacade->findNewsletters($this->localization, false, 5, Website::MODULE_OFERTO_COM);
	}

	public function actionOther($other): void
	{
		$this->redirectPermanent('Homepage:default', ['region' => 'cz']);
	}
}

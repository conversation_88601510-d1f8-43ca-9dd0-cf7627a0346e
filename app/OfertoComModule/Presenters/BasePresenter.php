<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Conditions\DocumentFacade;
use <PERSON><PERSON><PERSON>\Model\Content\ContentGenerator;
use <PERSON><PERSON><PERSON>\Model\EntityManager;
use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON><PERSON>ino\Model\Seo\SeoFacade;
use Ka<PERSON>ino\Model\Seo\SeoGenerator;
use Ka<PERSON>ino\Model\Shops\Entities\Shop;
use Ka<PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Tags\Entities\Tag;
use Ka<PERSON>ino\Model\Tags\TagFacade;
use Nette\Localization\ITranslator;
use Nette\Utils\Strings;

abstract class BasePresenter extends \Kaufino\Presenters\BasePresenter
{
	/** @var EntityManager @inject */
	public $entityManager;

	/** @var DocumentFacade @inject */
	public $documentFacade;

	/** @var SeoGenerator @inject */
	public $seoGenerator;

	/** @var ContentGenerator @inject */
	public $contentGenerator;

	/** @var ITranslator @inject */
	public $translator;

	/** @var string @persistent */
	public $region;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var SeoFacade @inject */
	public $seoFacade;

	/** @var OfferFacade @inject */
	public $offerFacade;

	protected function startup()
	{
		parent::startup();

		if ($this->getParameter('region') === 'no') {
			$this->redirect('this', ['region' => $this->geoResolver->resolveRegionFromRequest()]);
		}

		$region = $this->region ?: $this->getParameter('region');

		if (!$region) {
			$this->redirect('this', ['region' => 'cz']);
		}

		if (!$this->localization) {
			# Debugger::log('Region does not exist.');
			$this->redirect('this', ['region' => 'cz']);
		}

		if (!$this->website->isActive() && !$this->getUser()->isLoggedIn()) {
			# Debugger::log('Website is not active.');
			$this->redirect('this', ['region' => 'cz']);
		}

		if ($utmSource = $this->getParameter('utm_source')) {
			//Debugger::barDump($utmSource);
			if ($utmSource === 'google' || $utmSource === 'seznam') {
				$this->getHttpResponse()->setCookie('isP', 'yes', '1 day');
			}
		}

		if ($this->getParameter('cookie') && $this->getParameter('cookie') === 'wVPkTDuR8QSQXKsU') {
			$this->getHttpResponse()->setCookie('d2s0KZA1rp9pwsRI9n0l', 'Rj1Z53FM17fL6nskc5NG', new \DateTime('+ 1 month'));

			$this->redirect('this');
		}

		if ($this->getUser()->isLoggedIn()) {
			$this->template->userLoggedIn = true; // only for starting session purpose
		}

		$pageExtensionSlug = Strings::substring($this->getHttpRequest()->getUrl()->getPath(), 4);
		$this->template->pageExtension = $this->seoFacade->findPageExtensionBySlug($this->website, $pageExtensionSlug);
		$this->template->pageExtensionSlug = $pageExtensionSlug;

		$this->template->isTrafficPaid = $utmSource === 'google' || $utmSource === 'seznam' || $this->getHttpRequest()->getCookie('isP') === 'yes';
		$this->template->allPageCacheAllowed = false;
		$this->template->canonicalUrl = $this->seoGenerator->generateCanonicalUrl($this->getHttpRequest()->getUrl()->getAbsoluteUrl());
		$this->template->translator = $this->translator;
		$this->template->headerShops = function () {
			return $this->shopFacade->findTopCouponShops($this->localization, 10);
		};
		$this->template->footerShops = function () {
			return $this->shopFacade->findTopCouponShops($this->localization, 10);
		};
		$this->template->footerShopsTags = function () {
			return $this->tagFacade->findTags($this->localization, Tag::TYPE_SHOPS, 11);
		};
		$this->template->footerOffersTags = function () {
			return $this->tagFacade->findTags($this->localization, Tag::TYPE_OFFERS, 11);
		};
		$this->template->footerWebsites = function () {
			return $this->websiteFacade->findActiveWebsites($this->website->getModule());
		};

		$this->template->getCountOfCouponsByShop = function (Shop $shop) {
			return $this->offerFacade->findOffersByShop($shop, null, true, Offer::TYPE_COUPON);
		};

		$this->template->getCountOfProductsByShop = function (Shop $shop) {
			return $this->offerFacade->findOffersByShop($shop, null, true, Offer::TYPE_PRODUCT);
		};

		$this->template->getCountOfExpiredCouponsByShop = function (Shop $shop) {
			return $this->offerFacade->findOffersByShop($shop, null, false, Offer::TYPE_COUPON);
		};

		$this->template->getCountOfExpiredProductsByShop = function (Shop $shop) {
			return $this->offerFacade->findOffersByShop($shop, null, false, Offer::TYPE_COUPON);
		};

		$this->template->conditions = $this->documentFacade->findByLocalization($this->localization);
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\Leaflet;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class LeafletPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	/** @var ShopFacade @inject */
	public $shopFacade;

	public function renderDefault(): void
	{
		$leaflets = $this->leafletFacade->findLeaflets($this->localization, true, null, Website::MODULE_OFERTO_COM);

		$this->template->leaflets = $leaflets;

		$this->responseCacheTags[] = 'leaflets';
	}

	public function actionLeaflet(Shop $shop, Leaflet $leaflet, $page = 1): void
	{
		if ($leaflet->getShop() != $shop) {
			$this->error('The leaflet is not owned by the shop.');
		}

		if ($leaflet->isDeleted() || $shop->isActiveCoupons() === false) {
			$this->redirectPermanent("Shop:shop", ['shop' => $shop]);
		}

		if (!$leaflet->hasPageByNumber($page)) {
			$this->redirectPermanent("Leaflet:leaflet", ['shop' => $shop, 'leaflet' => $leaflet]);
		}

		$this->responseCacheTags[] = 'shop/' . $shop->getId();

		$this->template->shop = $shop;
		$this->template->leaflet = $leaflet;
		$this->template->similarLeaflets = $this->leafletFacade->findLeafletsByShop($shop, 5, true, true, $leaflet);
		$this->template->recommendedLeaflets = $this->leafletFacade->findLeaflets($this->localization, false, 5, Website::MODULE_OFERTO_COM);

		$this->template->similarShops = $this->shopFacade->findLeafletShops($this->localization, true, null, Website::MODULE_OFERTO_COM);
		$this->template->currentPage = $page;

		$this->template->leafletDescription = $this->contentGenerator->generateLeafletDescription($leaflet);

		if ($leaflet->isNewsletter() && $shop->useLeafletTemplateForNewsletters() === false) {
			$this->setView('leafletNewsletter');
		}
	}
}

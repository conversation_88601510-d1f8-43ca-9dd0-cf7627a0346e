<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\OfertoComModule\Presenters;

use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;
use <PERSON><PERSON><PERSON>\Model\Websites\Entities\Website;

final class LeafletsPresenter extends BasePresenter
{
	/** @var LeafletFacade @inject */
	public $leafletFacade;

	public function actionLeaflets(): void
	{
		$this->responseCacheTags[] = 'leaflets';

		$this->template->leaflets = $this->leafletFacade->findLeaflets($this->localization, false, 100, Website::MODULE_OFERTO_COM);
	}
}

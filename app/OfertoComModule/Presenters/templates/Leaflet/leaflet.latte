
{block title}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{_letado.leaflet.metaTitle, [brand => $leaflet->getShop()->getName(), validSince => $validSince]}
{/block}

{block description}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{_letado.leaflet.metaDesc, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
{/block}

{block robots}{if $leaflet->isInNoIndexPeriod()}noindex,nofollow{else}index,follow{/if}{/block}

{block head}

{/block}

{block content}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}

<div class="leaflet k-lf-layout">
    <div class="container">
		<div class="leaflet__content">
			<div class="d-block overflow-hidden">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {_letado.leaflet.leaflet, [brand => $leaflet->getShop()->getName()]}
                            <span class="leaflet__date">{$leaflet->getValidSince()|localDate} – {$leaflet->getValidTill()|localDate}</span>
                        </h1>                        

						<p class="page-header__text mw-400">
                            {_letado.leaflet.metaDesc, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
                        </p>
					</div>

					<div class="leaflet__detail-header-side">
						<a n:href="Shop:shop $leaflet->getShop()">
                            <picture>
                                <source data-srcset="{$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                                <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$leaflet->getShop()->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$leaflet->getShop()->getName()}" class="lazyload leaflet__detail-header-logo">
                            </picture>							
						</a>
					</div>
				</div>                

                {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}

                <div>

                </div>                

                <div class="leaflet-preview mb-5">						                    
                    <picture>
                        <source data-srcset="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:870,null,'fit','webp'}" type="image/webp">
                        <img src="{$basePath}/images/placeholder-870.png" data-src="{$leaflet->getPageByNumber($currentPage)->getImageUrl() |image:870,null}" width="870" height="1190" alt="{$leaflet->getShop()->getName()}" class="lazyload">
                    </picture>
                </div>                

                {if $leaflet->getPartnerLink()}
                    <div class="ta-center mt-3 mb-3">
                        <a href="{$leaflet->getPartnerLink()}" target="_blank" class="k-button">							
                            {_letado.leaflet.goToShop}
						</a>
                    </div>
                {/if}

                <div>        

                </div>                  

                {include 'paginator.latte', shop => $shop, leaflet => $leaflet, currentPage => $currentPage}                  

                <div>                

                </div>

                {capture $leafletBrandLink}                    
                    <a n:href="Shop:shop $leaflet->getShop()" class="td-underline td-hover-none">{$leaflet->getShop()->getName()}</a>
                {/capture}

                {capture $leafletPageCount}           
                    {count($leaflet->getPages())}
                {/capture}

                <div class="px-3 px-lg-0">
                    <p class="color-grey fz-m lh-15 mb-3"><strong>{_letado.leaflet.smallTitle, [brand => $leaflet->getName()]} {$leaflet->getValidSince()|localDate:'long'}</strong></p>
                    <p class="color-grey fz-m lh-15 mb-3">
                        {_'letado.leaflet.desc', [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill, leafletPageCount => $leafletPageCount] |noescape}                        
                    </p>
                    
                    <p class="color-grey fz-m lh-15 mb-3">{_'letado.leaflet.longDesc1', [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill] |noescape}</p>
                    <p class="color-grey fz-m lh-15 mb-3">{_'letado.leaflet.longDesc2'}</p>
                    <p class="color-grey fz-m lh-15 mb-3">{_'letado.leaflet.longDesc3'}</p>
                    <p class="color-grey fz-m lh-15 mb-5">{_'letado.leaflet.longDesc4', [leafletBrandLink => $leafletBrandLink, validSince => $validSince , validTill => $validTill] |noescape}</p>                    
                </div>

                <div class="d-flex mb-5 px-3 px-lg-0">
                    <a href="{link Leaflets:leaflets}" class="color-grey fz-m td-underline td-hover-none mr-3"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_letado.leaflet.backToLeaflets}</a>                    
                    <a n:href="Shop:shop $leaflet->getShop()" class="color-grey fz-m td-underline td-hover-none mr-3" >{_letado.leaflet.allBrandLeaflets, [brand => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                </div>                
            </div>        
			
            <div class="leaflet__aside">
                <div class="lf__box lf__box-lg-border">
                    <h3 class="lf__box-title px-3 px-lg-0">{_letado.leaflet.recommendedLeaflets}</h3>
                    <div class="lf__box-wrapper">
                        {foreach $recommendedLeaflets as $recommendedLeaflet}
                            {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                            <div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
                                <a class="lf__box-image-wrapper {if $recommendedLeaflet->isExpired()} expired{/if} mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">
                                    <picture>
                                        <source data-srcset="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                        <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$recommendedLeaflet->getName()}" class="img-responsive lazyload">
                                    </picture>
                                </a>
                                <p class="fz-xxs fz-sm-xs mb-0">
                                    <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">{$recommendedLeaflet->getName()}</a>
                                    <small>{$recommendedLeaflet->getValidSince()|localDate} - {$recommendedLeaflet->getValidTill()|localDate:'long'}</small>
                                </p>
                            </div>
                        {/foreach}
                    </div>
                </div>
                
                <div class="float-wrapper">
                    <!-- letado.com / halfpage1 -->     
                    {include "../components/halfpage1.latte"}                               
                </div>             
                
            </div>
        </div>

        <div class="leaflet__sidebar" style="height: auto !important;" n:if="count($similarLeaflets) > 0">
            <div class="lf__box">
                <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_letado.leaflet.similarLeaflets, [brand => $leaflet->getShop()->getName()]}</h3>
                <div class="lf__box-wrapper">                    
                    <div n:foreach="$similarLeaflets as $similarLeaflet" class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                        <a class="lf__box-image-wrapper {if $similarLeaflet->isExpired()}expired{/if} lf__box-image--medium mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">
                            <picture>
                                <source data-srcset="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'}" type="image/webp">
                                <img src="{$basePath}/images/placeholder-100x140.png" data-src="{$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'}" width="100" height="140" alt="{$similarLeaflet->getName()}" class="img-responsive lazyload">
                            </picture> 
                        </a>
                        <p class="fz-xxs fz-sm-xs mb-0">
                            <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">{$similarLeaflet->getName()}</a>
                            <small>{$similarLeaflet->getValidSince()|localDate} - {$similarLeaflet->getValidTill()|localDate:'long'}</small>
                        </p>
                    </div>
                </div>
            </div>

            
            <div class="float-wrapper">                
                <!-- letado.com / halfpage2 -->
                {include "../components/halfpage2.latte"}                                         
            </div>            
        </div>	
    </div>	

	<div class="float-wrapper__stop"></div>	
</div>



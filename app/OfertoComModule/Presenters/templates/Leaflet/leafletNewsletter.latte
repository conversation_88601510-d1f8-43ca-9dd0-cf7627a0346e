{block #robots}noindex, nofollow{/block}

{block title}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{_ofertocom.newsletter.metaTitle, [brand => $leaflet->getShop()->getName(), validSince => $validSince]}
{/block}

{block description}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}
{_ofertocom.newsletter.metaDesc, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
{/block}

{block scripts}
    {include parent}        
{/block}

{block content}
{capture $validSince}{$leaflet->getValidSince()|localDate:'long'}{/capture}
{capture $validSinceDay}{$leaflet->getValidSince()|dayGenitive}{/capture}
{capture $validTill}{$leaflet->getValidTill()|localDate:'long'}{/capture}

<div class="leaflet k-lf-layout">
    <div class="container">
		<div class="leaflet__content">
			<div class="d-block overflow-hidden">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {_ofertocom.newsletter.leaflet, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}                            
                        </h1>                        

						<p class="page-header__text mw-700">
                            {_ofertocom.newsletter.metaDesc, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validSinceDay => $validSinceDay]|noescape}
                        </p>
					</div>

					<div class="leaflet__detail-header-side">
						<a n:href="Shop:shop $leaflet->getShop()">
                            <picture>                        
                                <source 
                                    data-srcset="
                                        {$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','webp'} 1x,
                                        {$leaflet->getShop()->getLogoUrl() |image:160,140,'fit','webp'} 2x
                                    " 
                                    type="image/webp"
                                >                                                        
                                <img 
                                    src="{$basePath}/images/placeholder-80x70.png" 
                                    data-srcset="
                                        {$leaflet->getShop()->getLogoUrl() |image:80,70,'fit','png'} 1x,
                                        {$leaflet->getShop()->getLogoUrl() |image:160,140,'fit','png'} 2x
                                    " 
                                    width="80" 
                                    height="70" 
                                    alt="{$leaflet->getShop()->getName()}" 
                                    class="lazyload leaflet__detail-header-logo"
                                >
                            </picture>  
						</a>
					</div>
				</div>                

                <div>
                    <!-- Letaky - Detail letaku - Responsive - 1 -->
                    
                </div>                

                <div class="leaflet-preview mb-5">
                    {foreach $leaflet->getPages() as $page}
                        <picture>
                            <source 
                                data-srcset="
                                    {$page->getImageUrl() |image:870,null,'fit','webp'} 1x,
                                    {$page->getImageUrl() |image:1740,null,'fit','webp'} 2x
                                " 
                                type="image/webp"
                            >
                            <img 
                                src="{$basePath}/images/placeholder-870.png"
                                data-srcset="
                                    {$page->getImageUrl() |image:870,null} 1x,
                                    {$page->getImageUrl() |image:1740,null} 2x
                                " 
                                width="870" 
                                height="1190" 
                                alt="{$leaflet->getShop()->getName()}" 
                                class="lazyload"
                            >
                        </picture>
                    {/foreach}
                </div>

                <div>
                    <!-- Letaky - Detail letaku - Responsive - 2 -->
                    <ins class="adsbygoogle mrec-xs mrec-sm mrec-md leaderboard-lg" data-ad-client="ca-pub-4233432057183172" data-ad-slot="4885879417" data-ad-format="auto" data-full-width-responsive="true"></ins>
                </div>
                    
                <div class="leaflet__ads-wrapper">
                    <!-- Letaky - Detail letaku - Responsive - 3 -->
                    
                </div>

                {capture $leafletBrandLink}                    
                    <a n:href="Shop:shop $leaflet->getShop()" class="td-underline td-hover-none">{$leaflet->getShop()->getName()}</a>
                {/capture}                

                <div class="mt-3 px-3 px-lg-0">
                    <p class="color-grey fz-m lh-15 mb-3"><strong>{_ofertocom.newsletter.smallTitle, [brand => $leaflet->getShop()->getName()]} {$leaflet->getValidSince()|localDate:'long'}</strong></p>
                    <p class="color-grey fz-m lh-15 mb-5">
                        {_'ofertocom.newsletter.desc', [leafletBrandLink => $leafletBrandLink, validSince => $validSince] |noescape}                        
                    </p>
                </div>

                <div class="d-flex mb-5 px-3 px-lg-0">
                    <a href="{link Leaflets:leaflets}" class="color-grey fz-m td-underline td-hover-none mr-3"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_ofertocom.newsletter.backToLeaflets}</a>                    
                    <a n:href="Shop:shop $leaflet->getShop()" class="color-grey fz-m td-underline td-hover-none mr-3" >{_ofertocom.newsletter.allBrandLeaflets, [brand => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                </div>
            </div>        
			
            <div class="leaflet__aside">
                <div class="lf__box lf__box-lg-border">
                    <h3 class="lf__box-title px-3 px-lg-0">{_ofertocom.newsletter.recommendedLeaflets}</h3>
                    <div class="lf__box-wrapper">
                        {foreach $recommendedLeaflets as $recommendedLeaflet}
                            {continueIf $recommendedLeaflet->getId() == $leaflet->getId()}
                            <div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
                                <a class="lf__box-image-wrapper {if $recommendedLeaflet->isExpired()} expired{/if} mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">
                                    <picture>
                                        <source 
                                            data-srcset="
                                                {$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'} 1x,
                                                {$recommendedLeaflet->getFirstPage()->getImageUrl() |image:200,280,'exactTop','webp'} 2x
                                            " 
                                            type="image/webp"
                                        >
                                        <img 
                                            src="{$basePath}/images/placeholder-100x140.png" 
                                            data-srcset="
                                                {$recommendedLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'} 1x,
                                                {$recommendedLeaflet->getFirstPage()->getImageUrl() |image:200,280,'exactTop'} 2x
                                            " 
                                            width="100" 
                                            height="140" 
                                            alt="{$recommendedLeaflet->getName()}" 
                                            class="img-responsive lazyload"
                                        >
                                    </picture>
                                </a>
                                <p class="fz-xxs fz-sm-xs mb-0">
                                    <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $recommendedLeaflet->getShop(), $recommendedLeaflet">{$recommendedLeaflet->getName()}</a>
                                    <small>{$recommendedLeaflet->getValidSince()|localDate} - {$recommendedLeaflet->getValidTill()|localDate:'long'}</small>
                                </p>
                            </div>
                        {/foreach}
                    </div>
                </div>

                {*
                <!-- Detail letaku - Sidebar - 1 -->
                <ins class="adsbygoogle mrec-xs mrec-sm mrec-md skyscraper-lg" data-ad-client="ca-pub-4233432057183172" data-ad-slot="9808305120" data-ad-format="auto" data-full-width-responsive="true"></ins>
                *}
                
            </div>
        </div>

        <div class="leaflet__sidebar" style="height: auto !important;" n:if="count($similarLeaflets) > 0">
            <div class="lf__box">
                <h3 class="lf__box-title mt-3 mt-md-0 px-3 px-lg-0">{_ofertocom.newsletter.similarLeaflets, [brand => $leaflet->getShop()->getName()]}</h3>
                <div class="lf__box-wrapper">                    
                    <div n:foreach="$similarLeaflets as $similarLeaflet" class="lf__box-item lf__box-item--md-100 flex-direction-column flex-direction-lg-row mb-3">
                        <a class="lf__box-image-wrapper {if $similarLeaflet->isExpired()}expired{/if} lf__box-image--medium mb-3 mb-lg-0 mr-lg-3" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">
                            <picture>
                                <source 
                                    data-srcset="
                                        {$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop','webp'} 1x,
                                        {$similarLeaflet->getFirstPage()->getImageUrl() |image:200,280,'exactTop','webp'} 2x
                                    " 
                                    type="image/webp"
                                >
                                <img 
                                    src="{$basePath}/images/placeholder-100x140.png" 
                                    data-srcset="
                                        {$similarLeaflet->getFirstPage()->getImageUrl() |image:100,140,'exactTop'} 1x,
                                        {$similarLeaflet->getFirstPage()->getImageUrl() |image:200,280,'exactTop'} 2x
                                    " 
                                    width="100" 
                                    height="140" 
                                    alt="{$similarLeaflet->getName()}" 
                                    class="img-responsive lazyload"
                                >
                            </picture> 
                        </a>
                        <p class="fz-xxs fz-sm-xs mb-0">
                            <a class="lf__box-link d-block color-black strong" n:href="Leaflet:leaflet $similarLeaflet->getShop(), $similarLeaflet">{$similarLeaflet->getName()}</a>
                            <small>{$similarLeaflet->getValidSince()|localDate} - {$similarLeaflet->getValidTill()|localDate:'long'}</small>
                        </p>
                    </div>
                </div>
            </div>

            {*
            <div class="float-wrapper">
                <!-- Detail letaku - Sidebar - 2 -->
                <ins class="adsbygoogle mrec-xs mrec-sm skyscraper-md halfpage-lg" data-ad-client="ca-pub-4233432057183172" data-ad-slot="5041874235" data-ad-format="auto" data-full-width-responsive="true"></ins>                
            </div>
            *}
        </div>	
    </div>	

	<div class="float-wrapper__stop"></div>	
</div>

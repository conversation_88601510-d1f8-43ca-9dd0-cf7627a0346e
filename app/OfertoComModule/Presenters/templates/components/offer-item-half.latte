{dump $offer}
                                     
<div class="k-coupon ocom-coupon">    
    <div class="k-coupon__box {if false}k-coupon__box--sale{/if}">
        {var $shopValue = strlen($offer->getDiscountAmount())}
        <strong class="k-coupon__box-value{if $shopValue >= 4} k-coupon__box-value--small{/if}">{if $offer->getDiscountType() == relative}-{$offer->getDiscountAmount()} % {else}-{$offer->getDiscountAmount()|price:$offer->getLocalization()}{/if}</strong>
        {if !isset($hideBadge)}<small class="k-coupon__box-type">{_ofertocom.coupon.type.sale}</small>{/if}
    </div>                    

    <div class="k-coupon__content">
        {if !isset($hideShop)}
            <a href="{$presenter->link('Shop:shop', ['shop' => $offer->getShop()])}" class="k-coupon__box-logo mt-4 mb-4">
                <picture>
                    <source data-srcset="{$offer->getShop()->getLogoUrl() |image:160,140,'fit','webp'}" type="image/webp">
                    <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$offer->getShop()->getLogoUrl() |image:160,140}" width="80" height="70" alt="{$offer->getShop()->getName()}" class="lazyload img-responsive">
                </picture>
            </a>
        {/if}

        <h3 class="mt-0 mb-2"><a href="{$presenter->link('Shop:shop', ['shop' => $offer->getShop(), 'oid' => $offer->getId()])}" target="_blank" data-popup-link="{$presenter->link('Exit:offer', $offer)}" class="color-black td-hover-underline ocom-offer__title">{$offer->getName()}</a></h3>
        {$offer->getDescription()|truncate:150|noescape}                                
    </div>
    
    {if !isset($hideButton)}
    <a href="{$presenter->link('Shop:shop', ['shop' => $offer->getShop(), 'oid' => $offer->getId()])}" target="_blank" data-popup-link="{$presenter->link('Exit:offer', $offer)}" class="k-coupon__button k-coupon__button--code js-click-link">
        <span class="k-coupon__button-label">{_ofertocom.coupon.showCode}</span>
        <span class="k-coupon__button-code">{$offer->getCode()}</span>
    </a>                  
    {/if}  
</div>                                                                    
                    
                
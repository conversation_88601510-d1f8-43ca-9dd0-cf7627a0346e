{block scripts}
    {include parent}
{/block}

{block description}{_ofertocom.offer.text}{/block}

{block content}
<div class="container">
    
    <div class="k-shop">    
        {foreach $topShopsWithOffers as $shop}            
            <a n:href="Shop:shop $shop" class="k-shop__item">
                <span class="k-shop__image-wrapper">
                    <picture>
                        <source data-srcset="{$shop->getLogoUrl() |image:80,70,'fit','webp'}" type="image/webp">
                        <img src="{$basePath}/images/placeholder-80x70.png" data-src="{$shop->getLogoUrl() |image:80,70}" width="80" height="70" alt="{$shop->getName()}" class="lazyload">
                    </picture>                                    
                </span>
                <small class="k-shop__title">{$shop->getName()}</small>
            </a>
        {/foreach}
    </div>

    <div class="mb-4">
        <h1 n:block="title" class="k__title ta-center mt-4 mt-sm-5 mb-3">{_ofertocom.offer.title}</h1>
        <p class="k__text ta-center mw-700 mb-3">{_ofertocom.offer.text}</p>						    
    </div>    

    {if count($offers) > 0}
        {foreach $offers as $offer}
            {include '../components/offer-item.latte', offer => $offer}
        {/foreach}
    {/if}
</div>


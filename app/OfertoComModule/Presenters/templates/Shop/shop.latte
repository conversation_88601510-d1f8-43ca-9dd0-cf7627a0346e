{var $parameters = [
    'topOfferAbsolute' => $topOfferAbsolute ? $topOfferAbsolute->getDiscountAmount() : 0,
    'topOfferRelative' => $topOfferRelative ? $topOfferRelative->getDiscountAmount() : 0,
    'topOfferAbsoluteFormatted' => $topOfferAbsoluteFormatted,
    'topOfferRelativeFormatted' => $topOfferRelative ? ($topOfferRelative->getDiscountAmount() . ' %') : 0,
    'hasCoupon' => $hasCoupon,
    'shopName' => $shop->getName(),
    'month' => ('app.months.' . date('n') . '.genitive', |translate|lower|firstUpper),
    'year' => date('Y'),
    'hasOffer' => $topOfferAbsolute || $topOfferRelative,
]}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$seoGenerator->renderInSandbox($pageExtension->getTitle(), $parameters)}
    {elseif $topOffer}
        {capture $discountAmount}
            {if $topOffer->getDiscountType() == relative}
                {$topOffer->getDiscountAmount()} %
            {else}
                {$topOffer->getDiscountAmount()|price:$topOffer->getLocalization()}
            {/if}
        {/capture}

        {_ofertocom.shop.titleWithCoupon, ['shop' => $shop->getName(), 'discountAmount' => ($discountAmount |trim), 'month' => ('app.months.' . date('n') . '.genitive',  |translate|lower), 'year' => date('Y')]}
    {else}
        {_ofertocom.shop.titleWithoutCoupon, ['shop' => ($shop->getName()|firstUpper)]}
    {/if}
{/block}

{block description}
{if $pageExtension && $pageExtension->getDescription()}{$seoGenerator->renderInSandbox($pageExtension->getDescription(), $parameters)}
{else}
{_ofertocom.shop.metaDescription, ['shop' => ($shop->getName()|firstUpper)]}
{/if}
{/block}

{block head}
    {include parent}      
{/block}

{block scripts}
    {include parent}

    {if $getAverageShopReview() != 0}
        {var $averageShopReview = $getAverageShopReview()}
    
        <script type="application/ld+json">        
            {
                "@context": "http://schema.org/",
                "@type": "Organization",
                "name": {$shop->getName()},
                "image": {$shop->getLogoUrl() |image:160,140},			
                "aggregateRating": {
                    "@type": "AggregateRating",
                    "ratingValue": {round(($averageShopReview*2), 0)/2},
                    "ratingCount": {$getCountOfTotalReviews()}
                }
            }
        </script>
    {/if}

    <script n:if="$faqContentBlocks" type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
                {foreach $faqContentBlocks as $faq} {
                    "@type": "Question",
                    "name": {$faq->getHeading()},
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": {strip_tags($faq->getContent())}
                    }
                }{sep},{/sep}
                {/foreach}
            ]
        }
    </script>
{/block}

{define newsletters}
    <div n:if="count($leaflets) > 0" class="container">
        <div class="row w100">
            <div class="col-12">
            {* Newslettery *}
                <h2 class="fz-xxl fw-regular mb-3 px-3 px-lg-0 fw-bold">{_ofertocom.shop.newsletter, [brand => $shop->getName()]|noescape}</h2>

                <div class="k-leaflets__wrapper mt-3">
                    {if true}
                        <div class="k-leaflets__item k-leaflets__item--mobile mb-5">
                            <!-- letado.com / mobile_rectangle1 -->
                            {include "../components/mobile_rectangle1.latte"}
                        </div>
                    {/if}

                    {foreach $leaflets as $leaflet}
                        <div class="k-leaflets__item mb-5">
                            <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="k-leaflets__link {if $leaflet->isExpired()}expired{/if} mb-3">
                                <picture n:if="$leaflet->getFirstPage()">
                                    <source data-srcset="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop','webp'}" type="image/webp">
                                    <img src="{$basePath}/images/placeholder-230x288.png" data-src="{$leaflet->getFirstPage()->getImageUrl() |image:230,288,'exactTop'}" width="230" height="288" alt="{$leaflet->getName()}" class="k-leaflets__image lazyload">
                                </picture>
                            </a>
                            <p class="k-leaflets__title mt-0 mb-0">
                                <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet" class="color-black">
                                    {$leaflet->getName()}
                                </a>
                            </p>
                            <p class="k-leaflets__date mt-0 mb-0">{$leaflet->getValidSince()|localDate:'long'}</p>
                        </div>
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
{/define}

{block content}

<div class="leaflet k-lf-layout k-lf-layout--fixed-container ocom-shop">
        
    <div class="container">	
        <div class="leaflet__content">
            <div class="w100">

                <div class="k-profile-header k-profile-header--sm-center">                    
                    <span class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">                        						
                        <picture>
                            <source 
                                data-srcset="
                                    {$shop->getLogoUrl() |image:160,140,'fit','webp'} 1x,
                                    {$shop->getLogoUrl() |image:320,280,'fit','webp'} 2x
                                " 
                                type="image/webp"
                            >                            
                            <img 
                                src="{$basePath}/images/placeholder-80x70.png" 
                                data-srcset="
                                    {$shop->getLogoUrl() |image:160,140} 1x,
                                    {$shop->getLogoUrl() |image:320,280} 2x
                                " 
                                width="160" 
                                height="140" 
                                alt="{$shop->getName()}" 
                                class="lazyload k-profile-header__logo"
                            >
                        </picture>	
                    </span>
                    
                    <div class="k-profile-header__content">
                        <div class="d-flex flex-wrap align-items-center">
                            <h1 class="k-profile-header__title">
                                {if $pageExtension && $pageExtension->getHeading()}
                                    {var $heading = $getHeadingFromPageExtension($pageExtension)}
                                    {$heading}
                                {else}
                                    {_ofertocom.shop.heading, ['brand' => $shop->getName()]}                                    
                                {/if}
                            </h1>

                            {var $averageShopReview = $getAverageShopReview()}

                            <span class="ocom-star d-flex align-items-center mb-3 mb-sm-0 ml-sm-auto" n:if="$averageShopReview">
                                {var $countOfTotalReviews = $getCountOfTotalReviews()}
                                {var $roundedAverageShopReview = round(($averageShopReview*2), 0)/2}
                                {for $c = 1; $c <= 5; $c++}
                                    {if $c <= $roundedAverageShopReview}
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                                        </svg>
                                    {else}
                                        {if ($c-1 + 0.5) == $roundedAverageShopReview}
                                        {* TODO: Tady by měla být hvězda co má vyplněnou jen polovinu *}
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
                                            </svg>
                                        {else}
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" />
                                            </svg>
                                        {/if}
                                    {/if}
                                {/for}

                                <span class="ml-2 shop-header__score">{number_format($averageShopReview, 1)} <small>({$countOfTotalReviews}x)</small></span>
                            </span>
                        </div>

                        {var $shortDescriptionBlock = $getContentBlockByType('short_description')}

                        <input class="sd-content__content-btn" type="checkbox" id="content-btn">
                        <div class="sd-content">
                            {if $shortDescriptionBlock}
                                <div class="k-content k-profile-header__text mb-3">
                                    {$shortDescriptionBlock->getContent() |content|noescape}
                                </div>
                            {else}
                                <p class="k-content k-profile-header__text mb-3">
                                    {_ofertocom.shop.text, [brand => $shop->getName()]|noescape}
                                </p>
                            {/if}
                            
                            <label for="content-btn" class="sd-content__show">
                                <span class="sd-content__show-inner">
                                    {_ofertocom.shop.shoMoreText}
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="10" height="6" viewBox="0 0 10 6" class="sd-content__show-icon">
                                        <defs></defs>
                                        <path stroke="#3F4D71" d="M9 1L5 5 1 1"></path>
                                    </svg>
                                </span>
                            </label>
                        </div>                                                 

                        <div class="d-flex flex-wrap align-items-center">                            
                            <div class="k-profile-header__button-wrapper">
                                <a n:href="Exit:shop $shop" target="_blank" class="k-profile-header__button">{_ofertocom.shop.button, [brand => $shop->getName()]} »</a>
                            </div>                             
                        </div>
                        
                    </div>                                       
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="k-content mb-0">
            <h2  class="fz-xxl fw-regular mb-3 px-3 px-lg-0 fw-bold">{_ofertocom.shop.offer, [brand => $shop->getName(), month => ((new DateTime())|monthName|firstUpper), year => date('Y')]}</h2>
            <p n:if="count($coupons) == 0">{_ofertocom.shop.noCoupon, [brand => $shop->getName()]}</p>
        </div>
    </div>

    <div n:if="count($coupons) > 0" class="container">
        <div class="row w100">                        
            <div class="col-sm-12 w100">
                {* Kupony *}
                {foreach $coupons as $offer}
                    {include '../components/offer-item.latte', offer => $offer, hideShop => true}
                {/foreach}                          
            </div>            
        </div>
    </div>

    <div n:if="count($deals) > 0" class="container">
        <div class="row w100">
            <div class="col-sm-12 w100">
                {* Dealy *}
                {foreach $deals as $offer}
                    {include '../components/offer-item.latte', offer => $offer, hideShop => true}
                {/foreach}
            </div>
        </div>
    </div>

    <div n:if="count($coupons) > 0" class="container mt-4 mb-5">
        <div class="d-flex flex-wrap align-items-center mx-auto">                            
            <div class="k-profile-header__button-wrapper">
                <a n:href="Exit:shop $shop" target="_blank" class="k-profile-header__button zoom">{_ofertocom.shop.button, [brand => $shop->getName()]} »</a>
            </div>                             
        </div>
    </div>    

    <div n:if="count($expiredOffers) > 0" class="container mb-5">
        <div class="row w100">
            <div class="col-sm-12 w100">
                {* expirované slevy *}
                <h2 n:if="count($expiredOffers) > 0" class="fz-xxl fw-regular mb-3 px-3 px-lg-0 fw-bold">{_ofertocom.shop.expireCoupon.title}</h2>
                
                <input class="k-coupon-expire__content-btn" type="checkbox" id="k-coupon-expire-btn">
                <div class="k-coupon-expire-wrapper">                    
                    {foreach $expiredOffers as $offer}
                        {include '../components/offer-item-expire.latte', offer => $offer, hideShop => true}
                    {/foreach}
                    <label n:if="count($expiredOffers) > 1" for="k-coupon-expire-btn" class="k-coupon-expire__show">
                        <span class="sd-content__show-inner">
                            {_ofertocom.shop.expireCoupon.showMore}
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="10" height="6" viewBox="0 0 10 6" class="sd-content__show-icon">
                                <defs></defs>
                                <path stroke="#3F4D71" d="M9 1L5 5 1 1"></path>
                            </svg>
                        </span>
                    </label>
                </div>                
            </div>
        </div>
    </div>

    {if count($coupons) === 0}
        {include newsletters}
    {/if}

    <div class="d-flex align-items-center justify-content-center mt-5">
        <span class="k-profile-header__logo-wrapper k-profile-header__logo-wrapper--smaller">                        						
            <picture>
                <source 
                    data-srcset="
                        {$shop->getLogoUrl() |image:160,140,'fit','webp'} 1x,
                        {$shop->getLogoUrl() |image:320,280,'fit','webp'} 2x
                    " 
                    type="image/webp"
                >                            
                <img 
                    src="{$basePath}/images/placeholder-80x70.png" 
                    data-srcset="
                        {$shop->getLogoUrl() |image:160,140} 1x,
                        {$shop->getLogoUrl() |image:320,280} 2x
                    " 
                    width="160" 
                    height="140" 
                    alt="{$shop->getName()}" 
                    class="lazyload k-profile-header__logo"
                >
            </picture>	
        </span>
    </div>

    {var $couponsBlock = $getContentBlockByType('coupons')}
    {var $couponInstructionsBlock = $getContentBlockByType('coupon_instructions')}
    {var $discountsBlock = $getContentBlockByType('discounts')}
    {var $productsBlock = $getContentBlockByType('products')}
    {var $complaintsBlock = $getContentBlockByType('complaints')}
    {var $advantagesBlock = $getContentBlockByType('advantages')}

    <div class="">
        <div class="ocom-shop-nav">
            <a href="#coupons" n:if="$couponsBlock">{_ofertocom.shop.block.coupons}<span class="hover-line"></span></a>
            <a href="#coupon_instructions" n:if="$couponInstructionsBlock">{_ofertocom.shop.block.couponInstructions}<span class="hover-line"></span></a>
            <a href="#discounts" n:if="$discountsBlock">{_ofertocom.shop.block.discounts}<span class="hover-line"></span></a>
            <a href="#products" n:if="$productsBlock">{_ofertocom.shop.block.products}<span class="hover-line"></span></a>
            <a href="#complaints" n:if="$complaintsBlock">{_ofertocom.shop.block.complaints}<span class="hover-line"></span></a>
            <a href="#advantages" n:if="$advantagesBlock">{_ofertocom.shop.block.advantages}<span class="hover-line"></span></a>
        </div>
    </div>

    <div class="container ocom-shop__container">	
        <div class="mw-805">
            <div n:if="$couponsBlock" class="k-content k__text mb-3 px-3" id="coupons">
                <h2 n:if="$couponsBlock->getHeading()">{$couponsBlock->getHeading()}</h2>
                <p>{$couponsBlock->getContent() |content|noescape}</p>
            </div>

            <div n:if="$couponInstructionsBlock" class="k-content k__text mb-3 px-3" id="coupon_instructions">
                <h2 n:if="$couponInstructionsBlock->getHeading()">{$couponInstructionsBlock->getHeading()}</h2>
                <p>{$couponInstructionsBlock->getContent() |content|noescape}</p>
            </div>            

            <div n:if="$discountsBlock" class="k-content k__text mb-3 px-3" id="discounts">
                <h2 n:if="$discountsBlock->getHeading()">{$discountsBlock->getHeading()}</h2>
                <p>{$discountsBlock->getContent() |content|noescape}</p>
            </div>

            <div n:if="$productsBlock" class="k-content k__text mb-3 px-3" id="products">
                <h2 n:if="$productsBlock->getHeading()">{$productsBlock->getHeading()}</h2>
                <p>{$productsBlock->getContent() |content|noescape}</p>
            </div>

            <div n:if="$complaintsBlock" class="k-content k__text mb-3 px-3" id="complaints">
                <h2 n:if="$complaintsBlock->getHeading()">{$complaintsBlock->getHeading()}</h2>
                <p>{$complaintsBlock->getContent() |content|noescape}</p>
            </div>

            <div n:if="$advantagesBlock" class="k-content k__text mb-3 px-3" id="advantages">
                <h2 n:if="$advantagesBlock->getHeading()">{$advantagesBlock->getHeading()}</h2>
                <p>{$advantagesBlock->getContent() |content|noescape}</p>
            </div>

            {var $faq1Block = $getContentBlockByType('faq_1')}
            <div n:if="$faq1Block" class="k-content k__text mb-3 px-3" id="advantages">
                <h2 n:if="$faq1Block->getHeading()">{$faq1Block->getHeading()}</h2>
                <p>{$faq1Block->getContent() |content|noescape}</p>
            </div>

            {var $faq2Block = $getContentBlockByType('faq_2')}
            <div n:if="$faq2Block" class="k-content k__text mb-3 px-3" id="advantages">
                <h2 n:if="$faq2Block->getHeading()">{$faq2Block->getHeading()}</h2>
                <p>{$faq2Block->getContent() |content|noescape}</p>
            </div>

            {var $faq3Block = $getContentBlockByType('faq_3')}
            <div n:if="$faq3Block" class="k-content k__text mb-3 px-3" id="advantages">
                <h2 n:if="$faq3Block->getHeading()">{$faq3Block->getHeading()}</h2>
                <p>{$faq3Block->getContent() |content|noescape}</p>
            </div>

            {var $customBlock = $getContentBlockByType('custom')}
            <div n:if="$customBlock" class="k-content k__text mb-3 px-3">
                <h2 n:if="$customBlock->getHeading()">{$customBlock->getHeading()}</h2>
                <p>{$customBlock->getContent() |content|noescape}</p>
            </div>
        </div>
    </div>   

    <div n:if="$shop->getLabels()->isEmpty() === false" class="container ocom-shop__container">    
        <div class="mw-805 k-content k__text mb-3 px-3">
            <h2>{_ofertocom.shop.alternativeShops}</h2>
            <div class="k-tag">
            {foreach $shop->getLabels() as $label}
                {var $labelName = $label->getName()}
                {var $alternativeShops = $getAlternativeShopsByLabel($label, $shop)}

                {continueIf empty($alternativeShops)}                
                {*$labelName*}                    
                {foreach $alternativeShops as $alternativeShop}
                    <div class="k-tag__inner">
                        <a n:href="Shop:shop $alternativeShop" class="k-tag__item">{$alternativeShop->getAlternativeName($shop->getId())}</a>
                    </div>
                {/foreach}                                    
            {/foreach}
            </div>
        </div>
    </div>

    <div class="container ocom-shop__container">        
        <div class="mw-805 k-content k__text mb-3 px-3">
            <h2>{_ofertocom.shop.review.title}</h2>
            {control shopReviewControl}
        </div>
    </div>

    {if count($coupons) > 0}
        {include newsletters}
    {/if}

	<div class="float-wrapper__stop"></div>

    {if $popupCoupon}
        {include popup.latte, coupon => $popupCoupon}
    {/if}
</div>

{import 'components/form.latte'}

<!DOCTYPE html>
<html lang="{$localization->getLocale()}">
<head>
	<meta charset="utf-8">
	{capture $headTitle}
		{ifset title}{include title|stripHtml}{/ifset}
	{/capture}
	<title>{if strlen($headTitle) > 55}{$headTitle}{else}{$headTitle} | MrOferto{/if}</title>
	<meta name="keywords" content="">
    <meta name="description" content="{ifset description}{include description|stripHtml}{/ifset}">
	<meta name="author" content="oferto">
	<meta name="robots" content="{block #robots|stripHtml|trim}index, follow{/block}">	
	
	<link rel="canonical" href="{$canonicalUrl}">

	<meta property="og:title" content="{ifset title}{include title|stripHtml} | {/ifset}mroferto.com" />
    <meta property="og:site_name" content="oferto"/>
    <meta property="og:url" content="{link //this}" />
    <meta property="og:description" content="{ifset description}{include description|stripHtml}{/ifset}" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="{ifset image}{include image |strip}{else}{$baseUrl}/images/1200x627_0G_oferto.jpg{/ifset}" />
    <meta property="fb:app_id" content="" />

	<meta name="twitter:card" content="summary" />

	<!-- Viewport for mobile devices -->    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=1">

	<link rel="apple-touch-icon" sizes="180x180" href="{$basePath}/images/favicon/oferto/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="{$basePath}/images/favicon/oferto/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="{$basePath}/images/favicon/oferto/favicon-16x16.png">
	<link rel="icon" type="image/png" sizes="192x192"  href="{$basePath}/images/favicon/oferto/android-chrome-192x192.png">
	<link rel="icon" type="image/png" sizes="512x512"  href="{$basePath}/images/favicon/oferto/android-chrome-512x512.png">
{*	<link rel="manifest" href="{$basePath}/images/favicon/oferto/site.webmanifest">*}
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#ffffff">

	<meta name="ahrefs-site-verification" content="5ec5be26cfe58d5e25899ff9921ea420afd8c7d87d68794943404438899a4f15">
	
	{var $version = 0.55}

	<link rel="stylesheet" href="{$basePath}/css/main.oferto.css?v={$version}">
	<link rel="stylesheet" href="{$basePath}/css/main.ocom.css?v={$version}">

	<!-- Google Tag Manager -->
	<script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
	new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
	j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
	'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
	})(window,document,'script','dataLayer','GTM-P6BQLKD');</script>
	<!-- End Google Tag Manager -->

	{block head}{/block}

</head>

<body>
	<!-- Google Tag Manager (noscript) -->
	<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-P6BQLKD"
	height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
	<!-- End Google Tag Manager (noscript) -->

	<div class="k-header">
		<div class="container container--flex">
			<a n:href="Homepage:default" class="k-header__logo-link">
				<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 530.3963 114.1832" n:syntax="off"><defs><style>.a{fill:url(#a);}.b{opacity:0.53;}.c{fill:#2bb673;}</style><linearGradient id="a" x1="13.0185" y1="67.1923" x2="111.5204" y2="67.1923" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><path d="M135.5618,104.66h-6.3394V36.0118a6.2362,6.2362,0,0,1,6.2362-6.2362h3.9683l26.3877,56.0733,26.3872-56.0733H196.17a6.2362,6.2362,0,0,1,6.2362,6.2362V104.66h-6.3389a6.2363,6.2363,0,0,1-6.2363-6.2362V61.5232l-20.1,43.1372h-7.834l-20.1-43.1372v36.901A6.2362,6.2362,0,0,1,135.5618,104.66Z" transform="translate(-13.0185 -10.1007)"/><path d="M251.3848,94.8166q-10.7724-10.7709-10.7714-27.6245,0-16.9035,10.7714-27.6245,10.771-10.7709,27.6241-10.771t27.625,10.771q10.7709,10.7205,10.7714,27.6245,0,16.8531-10.7714,27.6245t-27.625,10.7715Q262.1558,105.5881,251.3848,94.8166Zm45.3536-47.7758a26.8076,26.8076,0,0,0-35.4581,0q-8.0918,7.4736-8.0918,20.1513t8.0918,20.1a26.6535,26.6535,0,0,0,35.4581,0q8.09-7.4215,8.0918-20.1Q304.83,54.514,296.7384,47.0408Z" transform="translate(-13.0185 -10.1007)"/><path d="M322.8808,61.1623V46.5256a19.6663,19.6663,0,0,1,1.8047-8.6069,14.9751,14.9751,0,0,1,7.06-7.0606A19.2313,19.2313,0,0,1,340.5585,28.9a30.3644,30.3644,0,0,1,13.2451,3.1953l-5.4629,11.2866a17.8937,17.8937,0,0,0-7.7822-1.9068,7.4282,7.4282,0,0,0-3.2978.6182,2.6134,2.6134,0,0,0-1.2891,1.2886,7.7685,7.7685,0,0,0-.5147,3.144V50.133h8.9669a6.2363,6.2363,0,0,1,6.2363,6.2363v4.793H335.4569V98.4242a6.2362,6.2362,0,0,1-6.2362,6.2362h-6.34Z" transform="translate(-13.0185 -10.1007)"/><path d="M367.2188,80.6438a15.9479,15.9479,0,0,0,3.917,8.9673,16.0561,16.0561,0,0,0,16.543,4.69,16.3474,16.3474,0,0,0,8.04-5.3081h14.8428q-5.6689,9.5353-13.3477,13.3482a30.7246,30.7246,0,0,1-14.122,3.247q-12.936,0-21.2334-8.3491-8.35-8.2976-8.3487-21.2851,0-12.9361,8.3487-21.2339,8.2968-8.349,21.2334-8.3492,12.9888,0,21.2851,8.3492,8.35,8.2983,8.35,21.2339v4.69Zm31.541-9.6895a15.35,15.35,0,0,0-5.9785-10.2558,16.513,16.513,0,0,0-10.3593-3.1953A16.327,16.327,0,0,0,372.32,61.0593a13.6289,13.6289,0,0,0-5.1016,9.895Z" transform="translate(-13.0185 -10.1007)"/><path d="M451.0832,58.5852V53.8439a6.2361,6.2361,0,0,1,6.2362-6.2362h3.0919v-10.72l6.306-.0333a6.2362,6.2362,0,0,1,6.2692,6.2361v4.517h8.6507V52.349a6.2362,6.2362,0,0,1-6.2362,6.2362h-2.4145v39.839A6.2363,6.2363,0,0,1,466.75,104.66h-6.339V58.5852Z" transform="translate(-13.0185 -10.1007)"/><path d="M513.987,46.68q12.8832,0,21.1816,8.2979,8.2457,8.2462,8.2461,21.13,0,12.9369-8.2461,21.1822-8.2983,8.2983-21.1816,8.2978-12.8848,0-21.1826-8.2978-8.2456-8.2449-8.2461-21.1822,0-12.8832,8.2461-21.13Q501.1027,46.68,513.987,46.68ZM502.3913,89.56a16.3447,16.3447,0,0,0,11.5957,4.4321A16.0587,16.0587,0,0,0,525.5827,89.56q5.2573-4.9988,5.2568-13.4517,0-8.3994-5.2568-13.4a16.353,16.353,0,0,0-11.5957-4.4321,16.06,16.06,0,0,0-11.5957,4.4321q-5.2574,5-5.2578,13.4Q497.1335,84.5618,502.3913,89.56Z" transform="translate(-13.0185 -10.1007)"/><path d="M418.2827,63.0977c0-5.6656-.0128-11.69-.11-16.4588h6.6692a6.2353,6.2353,0,0,1,6.2337,6.0469c.055,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4153-11.3326l0,8.9611a6.3352,6.3352,0,0,1-5.3562,6.3209c-7.063.9884-9.059,5.3614-9.059,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6465Z" transform="translate(-13.0185 -10.1007)"/><path d="M207.6968,63.0977c0-5.6656-.0127-11.69-.11-16.4588h6.6692a6.2351,6.2351,0,0,1,6.2336,6.0469c.0551,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4152-11.3326V55.27a6.3353,6.3353,0,0,1-5.3563,6.3209c-7.0629.9884-9.0589,5.3614-9.0589,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6466Z" transform="translate(-13.0185 -10.1007)"/><path class="a" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/><g class="b"><path class="c" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/></g></svg>
			</a>

			<form class="k-header__search">
				<input type="text" class="k-header__search-input js-search-input" data-search-url="{link Ajax:search}" placeholder="{_ofertocom.navbar.search.placeholder}">
				<input type="submit" class="k-header__search-submit js-search-submit" data-search-url="{link Search:search, q => 'q'}" value="{_ofertocom.navbar.search.submit}">

				<div class="k-header__search-wrapper">					
				</div>
			</form>

			<div class="k-header__nav">				
				<a href="{link Shops:shops}" class="k-header__nav-item">{_ofertocom.navbar.shops}</a>				
				<a href="{link Leaflets:leaflets}" class="k-header__nav-item">{_ofertocom.navbar.leaflets}</a>
				<a href="{link Articles:articles}" class="k-header__nav-item">{_ofertocom.navbar.articles}</a>
				{*<a href="{link Offers:offers}" class="k-header__nav-item">{_ofertocom.navbar.offers}</a>*}

				<span class="k-header__nav-separator">|</span>
				
				{foreach $headerShops() as $headerShop}
					<a n:if="$iterator->counter < 3" n:href="Shop:shop $headerShop" class="k-header__nav-item color-grey">{$headerShop->getName()}</a>

					{if $iterator->counter == 2}
					<div class="k-header__nav-dropdown-wrapper">
						<a href="{link Shops:shops}" class="k-header__nav-item k-header__nav-item--more">{_ofertocom.navbar.moreShops} »</a>

						<div class="k-header__nav-dropdown">
					{/if}
						<a n:if="$iterator->counter > 3" n:href="Shop:shop $headerShop" class="k-header__nav-dropdown-item">{$headerShop->getName()}</a>
					{if $iterator->last}
						</div>
					</div>
					{/if}
				{/foreach}
			</div>

			<button class="k-header__menu-icon">
				<svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bars" class="svg-inline--fa fa-bars fa-w-14" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"></path></svg>
			</button>			
		</div>
	</div>
		
	<div n:foreach="$flashes as $flash" n:class="alert, 'alert-' . $flash->type">{$flash->message}</div>				
	{include content}

	{if in_array($presenterName, ['OfertoCom:Shop', 'OfertoCom:Tag']) && isset($userLoggedIn)}
		<div class="k-page-extension">
			<span n:class="$pageExtension && $pageExtension->getTitle() ? k-page-extension__tag--green ,k-page-extension__tag">MT</span>
			<span n:class="$pageExtension && $pageExtension->getDescription() ? k-page-extension__tag--green ,k-page-extension__tag">MD</span>
			<span n:class="$pageExtension && $pageExtension->getHeading() ? k-page-extension__tag--green ,k-page-extension__tag">H1</span>
			<span n:class="$pageExtension && $pageExtension->getKeywords() ? k-page-extension__tag--green ,k-page-extension__tag">KW</span>			
			<span n:class="$pageExtension && $pageExtension->getShortDescription() ? k-page-extension__tag--green ,k-page-extension__tag">SD</span>
			<span n:class="$pageExtension && $pageExtension->getLongDescription() ? k-page-extension__tag--green ,k-page-extension__tag">LD</span>

			{if $presenterName == 'OfertoCom:Shop'}
				<span n:class="$contentBlocksAllowed ? k-page-extension__tag--green ,k-page-extension__tag">CB</span>
				<a n:href=":Admin:Shop:shop $shop->getId()" class="k-page-extension__btn" target="_blank">Edit shop</a>
			{/if}

			{if $presenterName == 'OfertoCom:Tag'}
				<a n:href=":Admin:Tag:tag $tag->getId()" class="k-page-extension__btn" target="_blank">Edit tag</a>
			{/if}

			{if $pageExtension}
				<a n:href=":Admin:Seo:pageExtension $pageExtension->getId()" class="k-page-extension__btn" target="_blank">Edit page extension</a>

				{var $shopKeywords = $pageExtension->getKeywords()}							
				<span class="k-alternative-name js-alternative-name" data-alternative-name="{$shopKeywords}"></span>				
			{else}
				<a n:href=":Admin:Seo:pageExtension id => null, websiteId => $website->getId(), slug => $pageExtensionSlug" class="k-page-extension__btn" target="_blank">Edit page extension</a>
			{/if}
		</div>
	{/if}

	<footer class="k-footer mt-5">		
		<div class="container">
			<div class="k-footer__wrapper">
				<div class="k-footer__column k-footer__column--first">
					<a n:href="Homepage:default" class="d-block mb-4">
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 530.3963 114.1832" n:syntax="off"><defs><style>.a{fill:url(#a);}.b{opacity:0.53;}.c{fill:#2bb673;}</style><linearGradient id="a" x1="13.0185" y1="67.1923" x2="111.5204" y2="67.1923" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#40ce85"/><stop offset="1" stop-color="#9df8af"/></linearGradient></defs><path d="M135.5618,104.66h-6.3394V36.0118a6.2362,6.2362,0,0,1,6.2362-6.2362h3.9683l26.3877,56.0733,26.3872-56.0733H196.17a6.2362,6.2362,0,0,1,6.2362,6.2362V104.66h-6.3389a6.2363,6.2363,0,0,1-6.2363-6.2362V61.5232l-20.1,43.1372h-7.834l-20.1-43.1372v36.901A6.2362,6.2362,0,0,1,135.5618,104.66Z" transform="translate(-13.0185 -10.1007)"/><path d="M251.3848,94.8166q-10.7724-10.7709-10.7714-27.6245,0-16.9035,10.7714-27.6245,10.771-10.7709,27.6241-10.771t27.625,10.771q10.7709,10.7205,10.7714,27.6245,0,16.8531-10.7714,27.6245t-27.625,10.7715Q262.1558,105.5881,251.3848,94.8166Zm45.3536-47.7758a26.8076,26.8076,0,0,0-35.4581,0q-8.0918,7.4736-8.0918,20.1513t8.0918,20.1a26.6535,26.6535,0,0,0,35.4581,0q8.09-7.4215,8.0918-20.1Q304.83,54.514,296.7384,47.0408Z" transform="translate(-13.0185 -10.1007)"/><path d="M322.8808,61.1623V46.5256a19.6663,19.6663,0,0,1,1.8047-8.6069,14.9751,14.9751,0,0,1,7.06-7.0606A19.2313,19.2313,0,0,1,340.5585,28.9a30.3644,30.3644,0,0,1,13.2451,3.1953l-5.4629,11.2866a17.8937,17.8937,0,0,0-7.7822-1.9068,7.4282,7.4282,0,0,0-3.2978.6182,2.6134,2.6134,0,0,0-1.2891,1.2886,7.7685,7.7685,0,0,0-.5147,3.144V50.133h8.9669a6.2363,6.2363,0,0,1,6.2363,6.2363v4.793H335.4569V98.4242a6.2362,6.2362,0,0,1-6.2362,6.2362h-6.34Z" transform="translate(-13.0185 -10.1007)"/><path d="M367.2188,80.6438a15.9479,15.9479,0,0,0,3.917,8.9673,16.0561,16.0561,0,0,0,16.543,4.69,16.3474,16.3474,0,0,0,8.04-5.3081h14.8428q-5.6689,9.5353-13.3477,13.3482a30.7246,30.7246,0,0,1-14.122,3.247q-12.936,0-21.2334-8.3491-8.35-8.2976-8.3487-21.2851,0-12.9361,8.3487-21.2339,8.2968-8.349,21.2334-8.3492,12.9888,0,21.2851,8.3492,8.35,8.2983,8.35,21.2339v4.69Zm31.541-9.6895a15.35,15.35,0,0,0-5.9785-10.2558,16.513,16.513,0,0,0-10.3593-3.1953A16.327,16.327,0,0,0,372.32,61.0593a13.6289,13.6289,0,0,0-5.1016,9.895Z" transform="translate(-13.0185 -10.1007)"/><path d="M451.0832,58.5852V53.8439a6.2361,6.2361,0,0,1,6.2362-6.2362h3.0919v-10.72l6.306-.0333a6.2362,6.2362,0,0,1,6.2692,6.2361v4.517h8.6507V52.349a6.2362,6.2362,0,0,1-6.2362,6.2362h-2.4145v39.839A6.2363,6.2363,0,0,1,466.75,104.66h-6.339V58.5852Z" transform="translate(-13.0185 -10.1007)"/><path d="M513.987,46.68q12.8832,0,21.1816,8.2979,8.2457,8.2462,8.2461,21.13,0,12.9369-8.2461,21.1822-8.2983,8.2983-21.1816,8.2978-12.8848,0-21.1826-8.2978-8.2456-8.2449-8.2461-21.1822,0-12.8832,8.2461-21.13Q501.1027,46.68,513.987,46.68ZM502.3913,89.56a16.3447,16.3447,0,0,0,11.5957,4.4321A16.0587,16.0587,0,0,0,525.5827,89.56q5.2573-4.9988,5.2568-13.4517,0-8.3994-5.2568-13.4a16.353,16.353,0,0,0-11.5957-4.4321,16.06,16.06,0,0,0-11.5957,4.4321q-5.2574,5-5.2578,13.4Q497.1335,84.5618,502.3913,89.56Z" transform="translate(-13.0185 -10.1007)"/><path d="M418.2827,63.0977c0-5.6656-.0128-11.69-.11-16.4588h6.6692a6.2353,6.2353,0,0,1,6.2337,6.0469c.055,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4153-11.3326l0,8.9611a6.3352,6.3352,0,0,1-5.3562,6.3209c-7.063.9884-9.059,5.3614-9.059,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6465Z" transform="translate(-13.0185 -10.1007)"/><path d="M207.6968,63.0977c0-5.6656-.0127-11.69-.11-16.4588h6.6692a6.2351,6.2351,0,0,1,6.2336,6.0469c.0551,1.8419.09,3.698.09,4.9555,2.5161-6.5165,4.7159-11.28,14.4152-11.3326V55.27a6.3353,6.3353,0,0,1-5.3563,6.3209c-7.0629.9884-9.0589,5.3614-9.0589,18.201V98.416a6.2361,6.2361,0,0,1-6.2362,6.2362h-6.6466Z" transform="translate(-13.0185 -10.1007)"/><path class="a" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/><g class="b"><path class="c" d="M70.9983,104.46C94.0115,100.4881,111.52,81.0644,111.52,57.6529c0-26.2622-22.0521-47.5522-49.251-47.5522s-49.2509,21.29-49.2509,47.5522c0,32.9522,25.9183,62.0119,67.4082,66.6311A29.903,29.903,0,0,1,68.35,117.3114C64.3,113.4227,65.96,105.329,70.9983,104.46ZM39.19,46.8681a10.241,10.241,0,1,1,10.2369,9.9458A10.1664,10.1664,0,0,1,39.19,46.8681Zm6.1989,32.3292,30.9666-40.72,2.4087,1.8267L47.8626,80.9738Zm19.1658-6.5634A10.2409,10.2409,0,1,1,74.7921,82.58,10.1665,10.1665,0,0,1,64.5551,72.6339Zm-15.1278-19.31a6.49,6.49,0,1,1,6.7469-6.4557A6.6955,6.6955,0,0,1,49.4273,53.3238ZM74.7921,79.09a6.49,6.49,0,1,1,6.7468-6.4562A6.6963,6.6963,0,0,1,74.7921,79.09Z" transform="translate(-13.0185 -10.1007)"/></g></svg>
					</a>
					<p class="fz-s color-grey lh-15 mb-3">{_ofertocom.footer.text}</p>
					<p class="fz-s color-grey mb-3">Copyright © {date('Y')} {_ofertocom.footer.copyright}</p>
				</div>
				<div class="k-footer__column">
					<div class="k-footer__wrapper">
						<div class="k-footer__column">
							<p>
								<strong class="d-block fz-m color-grey tt-uppercase mb-3">{_ofertocom.footer.shops}:</strong>
								<a n:foreach="$footerShops() as $footerShop" n:href="Shop:shop $footerShop" class="d-block fz-m color-black td-none td-hover-underline mb-3">{$footerShop->getName()}</a>
							</p>
						</div>								

						<div class="k-footer__column">
							<p>
								<strong class="d-block fz-m color-grey tt-uppercase mb-3">{_ofertocom.footer.aboutOferto}:</strong>
								<a n:href="Static:aboutUs" class="d-block fz-m color-black td-none td-hover-underline mb-3">{_ofertocom.footer.aboutUs}</a>
								<a n:href="Shops:shops" class="d-block fz-m color-black td-none td-hover-underline mb-3">{_ofertocom.footer.shops}</a>
								<a n:href="Leaflets:leaflets" class="d-block fz-m color-black td-none td-hover-underline mb-3">{_ofertocom.navbar.leaflets}</a>

								{if $conditions}
									<a n:href="Conditions:default, $conditions" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$conditions->getName()}</a>
								{/if}
							</p>
						</div>													
						
					</div>	
				</div>
			</div>

			{var $footerWebsites = $footerWebsites()}

			<div class="" n:if="count($footerWebsites) > 1">
				<p>
					<strong class="d-block fz-m color-grey tt-uppercase mb-3">{_ofertocom.footer.nextCountries}:</strong>

					{foreach $footerWebsites as $footerWebsite}
						{continueIf $footerWebsite === $website}

						<a href="{$footerWebsite->getDomain()}" class="fz-m color-black td-none td-hover-underline mb-3">{$footerWebsite->getLocalization()->getOriginalName()}</a>{sep}, {/sep}
					{/foreach}
				</p>
			</div>	
		</div>
	</footer>

	{block scripts}
		<script src="{$basePath}/js/lazysizes/lazysizes.min.js" async></script>		
		<script src="{$basePath}/js/main.js?v={$version}" async></script>

		{if $pageExtension && $pageExtension->getKeywords()}
			<script src="{$basePath}/js/page-extension.js?v={$version}" async></script>
		{/if}

		<script src="{$basePath}/js/main.oferto.js?v={$version}" async></script>						
	{/block}
</body>
</html>
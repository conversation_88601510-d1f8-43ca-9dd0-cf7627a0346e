{block head}
    {include parent}
    <script n:syntax="double">        
        window.dataLayer.push({
            'content_group' : 'Lists',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block scripts}
    {include parent}
{/block}

{block title}{_kaufino.leaflets.metaTitle}{/block}
{block description}{_kaufino.leaflets.metaDescription}{/block}

{block hreflang}
    {var $otherWebsites = $footerWebsites()}

    {foreach $otherWebsites as $otherWebsite}
        {var $localizedUrl = $getLocalizedUrl($otherWebsite, 'leaflets')}
        <link rel="alternate" n:attr="hreflang: $otherWebsite->getLocalization()->getFullLocale('-')" href={$localizedUrl} />
    {/foreach}
{/block}


{block content}
<div class="container">
    <div class="mb-6">
        <h1 class="k__title ta-center mt-5 mb-4">{_kaufino.leaflets.title}</h1>
        <p class="k__text ta-center mw-700 mb-0">{_kaufino.leaflets.text}</p>						    
    </div>

    <div class="k-tag k-tag--4 mb-5">
        {foreach $leafletsTags as $tag}
            <div class="k-tag__inner">
                <a n:href="Tag:tag $tag" class="k-tag__item">{$tag->getName()}</a>
            </div>
        {/foreach}
    </div>

    <div class="k-leaflets__wrapper k-leaflets__wrapper--xs-mx">
        {foreach $leaflets as $leaflet}
            {include '../components/leaflet.latte', leaflet => $leaflet, validBadgeShow => true}
        {/foreach}
    </div>

    <div n:if="count($cities) > 0">
        <h2 class="k__title ta-center">
            {_kaufino.leaflets.city}                        
        </h2>             
        
        <p class="k-tag mb-5">
            {foreach $cities as $city}
                <span class="k-tag__inner">
                    <a n:href="City:city $city" class="k-tag__item">{$city->getName()}</a>
                </span>
            {/foreach}
        </p>                    
    </div>
</div>

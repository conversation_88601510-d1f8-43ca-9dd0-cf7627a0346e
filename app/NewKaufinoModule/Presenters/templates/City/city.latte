{capture $stores}
    {var $uniqueStores = []}
    {var $i = 0}
    {foreach $shops as $shop}
        {continueIf !$shop->hasCity($city)}
        {var $uniqueStores[] = $shop}
        {var $i = $i + 1}
        {breakIf $i > 3}
    {/foreach}


    {var $countOfStores = count($uniqueStores)}
    {foreach $uniqueStores as $store}
        {if $iterator->isLast() && $countOfStores > 1} {_kaufino.city.city.generatedText.or} {/if}
        {if $city->isActiveBrandsKaufino()}
            <a n:href="City:shop $city, $store">{_kaufino.city.shop.leafletStores.title, [brand => $store->getName(), city => $city->getName()] |spaceless}</a>{if $iterator->getCounter() < $countOfStores-1 && $countOfStores > 2}, {/if}
        {else}
            <a n:href="Shop:shop $shop">{_kaufino.city.city.leafletStores.title, [brand => $store->getName()] |spaceless}</a>{if $iterator->getCounter() < $countOfStores-1 && $countOfStores > 2}, {/if}
        {/if}
    {/foreach}
{/capture}

{capture $shopsLink |spaceless|trim}
<a n:href="Shops:shops">{_kaufino.navbar.shops}</a>
{/capture}

{block head}
    {include parent}
    <script n:syntax="double">
        window.dataLayer.push({
            'content_group' : 'City',
            'country' : {{$localization->getRegion()}}
        });
    </script>
{/block}

{block title}
    {if $pageExtension && $pageExtension->getTitle()}
        {$pageExtension->getTitle()}
    {else}
        {_kaufino.city.city.title, [city => $city->getName()]}
    {/if}
{/block}

{block description}
    {if $pageExtension && $pageExtension->getDescription()}
        {$pageExtension->getDescription()}
    {else}
        {_kaufino.city.city.metaDescription, [city => $city->getName(), stores => trim($stores)]|noescape}
    {/if}
{/block}

{block breadcrumb}
    <div class="k-breadcrumb__container mt-4">
        <p class="k-breadcrumb">
            <a n:href="Leaflets:leaflets" class="link">{_kaufino.navbar.leaflets}</a>
        </p>
    </div>
{/block}

{block content}

<div class="container">
    <div class="mt-3 hidden md:flex flex-wrap gap-3 lg:gap-0 lg:justify-between mb-[45px]">
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Hypermarkety a supermarkety</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Elektro</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Nábytek</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Sport</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Bydlení a zahrada</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Drogerie a kosmetika</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Lékárny a zdraví</div>
        <div class="transition-transform duration-200 transform hover:scale-[103%] cursor-pointer text-sm font-light leading-[24.6px] pt-3 pb-[13px] px-4 bg-light-6 rounded-xl">Ostatní</div>
    </div>

    <div class="text-[26px] md:text-[33px] font-medium pt-8 md:pt-0">Obchody a potraviny <span class="text-primary">Praha</span></div>
    <div class="hidden md:flex gap-[9px] items-center font-light text-sm text-[#646C7C] mb-[18px]">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
            <path d="M2.6 7.54682V12H5.8V8.76132C5.8 8.54654 5.88429 8.34062 6.0343 8.18878C6.18432 8.03694 6.38784 7.95165 6.6 7.95165H7.4C7.61216 7.95165 7.81563 8.03694 7.96571 8.18878C8.11574 8.34062 8.2 8.54654 8.2 8.76132V12H11.4V7.54682M1 6.73715L6.43414 1.23733C6.50843 1.16209 6.59664 1.1024 6.69371 1.06168C6.79083 1.02096 6.89488 1 7 1C7.10512 1 7.20918 1.02096 7.30624 1.06168C7.40336 1.1024 7.49158 1.16209 7.56587 1.23733L13 6.73715" stroke="#80899C" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
            <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
        </svg>
        Obchody
        <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
            <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
        </svg>
        Hypermarkety a supermarkety
        <svg xmlns="http://www.w3.org/2000/svg" width="3" height="5" viewBox="0 0 3 5" fill="none">
            <path d="M0.0976311 0.0915292C-0.0325437 0.213568 -0.0325437 0.411432 0.0976311 0.533471L2.19526 2.5L0.0976312 4.46653C-0.0325435 4.58857 -0.0325435 4.78643 0.0976313 4.90847C0.227806 5.03051 0.438861 5.03051 0.569036 4.90847L2.90237 2.72097C3.03254 2.59893 3.03254 2.40107 2.90237 2.27903L0.569036 0.0915291C0.438861 -0.0305097 0.227806 -0.0305097 0.0976311 0.0915292Z" fill="#646C7C"></path>
        </svg>
        Lidl letáky
    </div>

    <div class="text-sm leading-[22px] font-light w-full max-w-[584px] text-grey-description mb-[30px]">
        Prohlédněte si nabídky těch nejoblíbenějších supermarketů a hypermarketů, pro které vám každý den přinášíme online aktuální letáky s bezkonkurenčními cenami.
    </div>

    <div class="grid grid-cols-2 md:grid-cols-6 gap-3 mb-8">
        {for $i = 1; $i <= 12 ; $i++}
        <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
            <div class="p-1.5 md:p-2 bg-light-6 rounded-xl">
                <div class="flex mb-[14px] md:mb-4">
                    <div class="bg-white flex items-center justify-center w-full h-[182px] rounded-lg">
                        <img class="w-full cover max-w-[160px] h-auto px-2 md:px-0"
                        src="https://img.tiplicdn.com/zoh4eiLi/IMG/7200/WOKXJigsBDNlKxftjC54cIXdpiwoX7XzvTeG_Ku4-zk/resize:fit:100:0:1/gravity:no/quality:90/aHR0cHM6Ly93d3cudGlwbGkuY3ovdXBsb2FkL2ltYWdlcy9zaG9wcy1zaG9wLWxvZ28vNzcxMjI1LnBuZw.png" alt="letak">
                    </div>
                </div>
                <div class="pl-3">
                    <div class="inline-flex text-xs font-light mb-2.5 border rounded border-light-3 py-1 px-2">19 prodejen</div>
                    <div class="flex items-center justify-between">
                        <div>PEPCO</div>
                        <div>
                            <svg width="39" height="15" viewBox="0 0 39 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19.5047 13.9999L13.1392 8.23403C9.67961 4.77447 14.7651 -1.86786 19.5047 3.50597C24.2443 -1.86786 29.3068 4.79753 25.8703 8.23403L19.5047 13.9999Z" stroke="#D7DBE0" stroke-width="1.60686" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {/for}
    </div>

    <div class="text-center underline leading-[24.5px] text-sm">
        <a href="#">Načíst další obchody</a>
    </div>
</div>

<div class="bg-light-6 mt-[55px]">
    <div class="container py-[60px]">
        <div class="font-light leading-7">Výhodné akce a slevy na rozmanitý sortiment pravidelně nabízí prodejna Lidl Tupolevova 722, kde lze při nákupu využít oblíbený Lidl leták. Ten si mohou zákazníci pohodlně prohlédnout online, stejně jako akce dostupné na pobočkách Lidl Kbelská 919 nebo Lidl Střížkovská 8.
        </div>

        <div class="text-[20px] font-medium leading-[35px] py-3">Akce Praha</div>

        <div class="font-light leading-7">Prodejna Lidl Tupolevova 722 zákazníkům nabízí nejen širokou nabídku zboží, ale také nízké ceny, o nichž pravidelně informuje Lidl leták. Pobočka Lidl Praha Tupolevova 722 je oblíbeným místem těch, kteří hledají cenově výhodné nabídky. Díky tomu, že je Lidl leták dostupný online, mají kupující aktuální slevy vždy při ruce. Pokud Lidl Tupolevova 722 nenabízí vše, co zákazník potřebuje, může využít také další obchody v okolí, jako jsou:

            Zjistěte, jaká je přesná adresa, kontakt na zákaznickou linku nebo otevírací doba oblíbených obchodů přehledně na jednom místě. Nechybí ani informace o tom, jaké pobočky se nachází ve vašem okolí a kde se dají využít další výhodné nabídky, o nichž informují akční letáky vybraných obchodů.
        </div>

        <div class="text-[20px] md:text-[26px] font-medium leading-[35px] md:leading-[39px] mb-[26px] md:mb-10 mt-[30px] md:mt-[72px]">Další letáky</div>

        <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
            {for $i = 1; $i <= 10 ; $i++}
                <div class="transition-transform duration-300 transform hover:scale-[102%] cursor-pointer">
                    <div class="p-1.5 md:p-2 bg-white rounded-xl">
                        <div class="flex mb-[13px] md:mb-[17px] relative" style="position: relative;">
                            <div class="absolute inset-0" style="background: linear-gradient(147deg, rgba(33, 11, 11, 0.00) 51.78%, #210B0B 98.98%); z-index: 1;"></div>
                            <div class="w-full relative">
                                <img class="rounded-lg w-full max-h-[297.66px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/czHSRlniiTNMGtjbVRgu3Sfho7ARfRvsh-oCkPKUk-0/resize:fill:230:288:1/gravity:no/quality:70/aHR0cHM6Ly9sZXRha3kudGlwbGkuY3ovZmlsZXMvbGVhZmxldHMvMjU0LzI1NDQ0NC8zY2QzNDhjYzcyZTUzMGI0Lmw4amZteXJ1dDkzdC5qcGc.webp" alt="letak">
                            </div>
                            <div class="flex flex-col lg:flex-row gap-1 absolute bottom-1 right-1 z-10">
                                <button class="w-fit self-end rounded-md inline-flex items-center bg-white py-1 text-xs md:text-sm leading-[24.5px] px-2.5 gap-2">
                                    Otevřít
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M3.66382 12.1494L11.1589 4.6543" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M11.8407 10.7868L11.8349 3.96717L5.03879 3.9614" stroke="#080B10" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center gap-1.5 md:gap-[15px] pl-1 md:pl-2 pb-[7px] md:pb-[9px]">
                            <img class="w-[36px] h-[36px] rounded-md" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="obchod">
                            <div class="leading-[21px]">
                                <div class="text-xs md:text-lg font-medium">Lidl</div>
                                <div class="text-xs font-light">04.04. - 07.04.2024</div>
                            </div>
                        </div>
                    </div>
                </div>
            {/for}
        </div>
    </div>
</div>

<div class="hidden md:block container">
    <div class="flex items-center gap-4 mt-[28px] mb-[32px]">
        <img class="w-[47px] h-[47px]" src="https://n.klmcdn.com/zoh4eiLi/IMG/7200/cT9HrUJ5ECk8X98guJfPoL0f9ZQ7rP7VeOdOjSrAN0g/resize:fit:80:80:1/gravity:no/quality:70/aHR0cHM6Ly9rYXVmaW5vLmNvbS91cGxvYWQvc2hvcHMvbG9nby9saWRsLTkwOS5wbmc.webp" alt="">
        <div class="text-[26px] font-medium leading-[39px]">Letáky Lidl najdete také v těchto zemích</div>
    </div>

    <div class="flex flex-wrap gap-1.5 md:gap-3">
        {foreach range(1, 17) as $i}
        <div class="transition-transform duration-200 transform hover:scale-[105%] cursor-pointer rounded-lg flex items-center gap-[5px] md:gap-[11px] p-1.5 md:p-3 text-xs md:text-sm leading-[21px] md:leading-[24.5px] font-light bg-light-6">
            <svg class="w-[15px] h-[15px] md:w-[33px] md:h-[33px]" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                <path d="M10.1086 0.466494C9.29607 0.165029 8.41728 0 7.49987 0C6.58247 0 5.70368 0.165029 4.89119 0.466494L4.23901 7.5L4.89119 14.5335C5.70368 14.835 6.58247 15 7.49987 15C8.41728 15 9.29607 14.835 10.1086 14.5335L10.7607 7.5L10.1086 0.466494Z" fill="#FFDA44"/>
                <path d="M15.0002 7.50003C15.0002 4.27532 12.9649 1.52622 10.1089 0.466553V14.5336C12.9649 13.4738 15.0002 10.7248 15.0002 7.50003Z" fill="#D80027"/>
                <path d="M0 7.50003C0 10.7248 2.03531 13.4738 4.89132 14.5336V0.466553C2.03531 1.52622 0 4.27532 0 7.50003Z" fill="black"/>
            </svg>
            Belgium
        </div>
        {/foreach}
    </div>
</div>
<!--KONIEC NOVEJ GRAFIKY-->

<div class="leaflet k-lf-layout k-lf-layout--fixed-container">
    <div class="container d-block">
        <div class="leaflet__content w100">
            <div class="w100">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">
                        <h1 class="page-header__title">
                            {if $pageExtension && $pageExtension->getHeading()}
                                {$pageExtension->getHeading()}
                            {else}
                                {_kaufino.city.city.title, [city => $city->getName()]}
                            {/if}
                        </h1>
					</div>
				</div>

                {if count($leaflets) > 0}
                    <div class="k-leaflets__wrapper">
                        {foreach $leaflets as $leaflet}
                            {if false && $iterator->counter == 1}
                                <div class="k-leaflets__item k-leaflets__item--first mb-3">
                                    <!-- Vypis mesta - Responsive - 2 -->
                                    <ins class="adsbygoogle adslot-1" style="display:block" data-ad-client="ca-pub-4233432057183172" data-ad-slot="2849716931" data-ad-format="auto" data-full-width-responsive="true"></ins>

                                    <script>
                                        (adsbygoogle = window.adsbygoogle || []).push({});
                                    </script>
                                </div>
                            {/if}

                            {include '../components/leaflet.latte', leaflet => $leaflet, cssClass => $iterator->counter > 10 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($leaflets) > 9" class="d-flex mt-3 mb-5">
                        <button class="link ml-auto k-show-more-button js-show-leaflet">{_'kaufino.showMore.leaflets'} »</button>
                    </p>
                {else}
                    <div class="alert alert-info mx-3">{_kaufino.tag.noLeaflets}</div>
                {/if}

                <div n:if="count($shops) > 0" class="">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.city.city.otherShops, [city => $city->getName()]}</h2>
                    <div class="k-shop">
                        {foreach $shops as $shop}
                            {include '../components/shop-logo.latte', shop => $shop, city => $city->isActiveBrandsKaufino() ? $city : null, cssClass => $iterator->counter > 12 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($shops) > 11" class="d-flex mt-3 mb-5">
                        <button class="link ml-auto k-show-more-button js-show-shop">{_'kaufino.showMore.shops'} »</button>
                    </p>
                </div>

                <div n:if="count($cityStores) > 0">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_'kaufino.city.city.storesTitle', ['city' => $city->getName()]}</h2>

                    <div class="k-offers">
                        {foreach $cityStores as $store}
                            <span class="k-tag__inner {$iterator->counter > 6 ? 'hidden'}">
                                <a n:href="City:store $city, $store->getShop(), $store" class="k-tag__item">{$store->getShop()->getName()} {$store->getStreet()}</a>
                            </span>
                        {/foreach}
                    </div>

                    <p n:if="count($cityStores) > 5" class="d-flex mt-3 mb-5">
                        <button class="link ml-auto k-show-more-button js-show-tag">{_'kaufino.city.city.storesMoreButton'}</button>
                    </p>
                </div>

                <div n:if="count($nearestCities)">
                    <h2 class="fz-xl fw-regular mb-5 px-3 px-lg-0">
                        {_kaufino.city.city.nearestCity}
                    </h2>

                    <div class="k-tag mb-1">
                        {foreach $nearestCities as $nearestCity}
                            <span class="k-tag__inner {$iterator->counter > 6 ? 'hidden'}">
                                <a n:href="City:city $nearestCity" class="k-tag__item">{$nearestCity->getName()}</a>
                            </span>
                        {/foreach}

                        <p n:if="count($nearestCities) > 5" class="d-flex mt-3 mb-5 w100">
                            <button class="link ml-auto k-show-more-button js-show-tag js-show-all-btn">{_'kaufino.showMore.cities'} »</button>
                            <a n:href="Cities:cities" class="link ml-auto hidden k-show-more-button js-all-btn">{_'kaufino.showMore.allCities'} »</a>
                        </p>
                    </div>
                </div>

                <div n:if="count($offers) > 0">
                    <h2 class="fz-xl fw-regular mb-3 px-3 px-lg-0">{_kaufino.shop.offersAll}</h2>

                    <div class="k-offers k-offers--4 mb-3">
                        {foreach $offers as $offer}
                            {continueIf !$offer->getLeafletPage()}
                            {include '../components/offer-item.latte', offer => $offer, hideShop => true, cssClass => $iterator->counter > 4 ? 'hidden' : ''}
                        {/foreach}
                    </div>

                    <p n:if="count($offers) > 3" class="d-flex mt-3 mb-5">
                        <button class="link ml-auto k-show-more-button js-show-offer">{_'kaufino.showMore.offers'} »</button>
                    </p>
                </div>

                <div class="k-content mt-5">
                    <p>
                        {if $pageExtension && $pageExtension->getShortDescription()}
                            {$pageExtension->getShortDescription()}
                        {else}
                            {if $localization->isCzech() || $localization->isItaly() || $localization->isHungarian()}
                                {_kaufino.city.city.text2, [city => $city->getName(), stores => trim($stores)]|noescape}
                            {else}
                                {_kaufino.city.city.text, [city => $city->getName(), stores => trim($stores)]|noescape}
                            {/if}
                        {/if}
                    </p>

                    {if $pageExtension && $pageExtension->getLongDescription()}
                        {$pageExtension->getLongDescription()|content|noescape}
                    {else}
                        <h2>{_'kaufino.city.city.h2', [city => $city->getName()]}</h2>
                    {/if}

                    {var $month = ((new DateTime())|monthName|lower)}

                    {capture $categoriesInText}
                        {var $items = ($footerShopsTags()|slice: 0, 3)}
                        {foreach $items as $_category}{last}{if count($items) > 1} {_kaufino.city.city.generatedText.and} {/if}{/last}<a n:href="Tag:tag $_category" title="{_kaufino.city.city.categoriesInText, [category => $_category->getName()]}">{$_category->getName()}</a>{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}

                    {capture $citiesInText}
                        {var $items = ($nearestCities|slice: 0, 4)}
                        {foreach $items as $_city}{last}{if count($items) > 1} {_kaufino.city.city.generatedText.and} {/if}{/last}<a n:href="City:city $_city" title="{_kaufino.city.city.citiesInText, [city => $_city->getName()]}">{$_city->getName()}</a>{sep}{if $iterator->getCounter() < count($items)-1}, {/if}{/sep}{/foreach}{/capture}

                    {capture $storesInText}
                        {var $uniqueStores = []}
                        {foreach $leaflets as $leaflet}
                            {skipIf !$leaflet->getShop()->hasCity($city)}
                            {var $uniqueStores[$leaflet->getShop()->getId()] = $leaflet->getShop()}
                            {breakIf $iterator->counter > 5}
                        {/foreach}

                        {foreach $uniqueStores as $store}
                            <a n:href="Shop:shop $store" title="{_kaufino.city.city.leafletStores.title, [brand => $store->getName()]}">{$store->getName()}</a>{sep}, {/sep}
                        {/foreach}
                    {/capture}

                    <p>{_kaufino.city.city.generatedText.1, [city => $city->getName(), cities => $citiesInText, population => (number_format($city->getPopulation(), 0, ',', ' ')), month => $month, category => $categoriesInText]|noescape}</p>
                    <p>{_kaufino.city.city.generatedText.2, [city => $city->getName(), stores => trim($storesInText)]|noescape}</p>
                    <ul>
                        <li n:foreach="$leaflets as $leaflet">
                            {capture $validSince}{$leaflet->getValidSince()|localDate} {/capture}
                            {capture $validTill}{$leaflet->getValidTill()|localDate}{/capture}

                            <a n:href="Leaflet:leaflet $leaflet->getShop(), $leaflet">{_kaufino.city.city.generatedText.leaflet, [brand => $leaflet->getShop()->getName(), validSince => $validSince, validTill => $validTill]}</a>
                            {breakIf $iterator->getCounter() > 2}
                        </li>
                    </ul>
                    <p n:if="count($nearestCities)">{_kaufino.city.city.generatedText.3, [city => $city->getName(), cities => $citiesInText, stores => $storesInText, shopsLink => $shopsLink,  month => $localization->isHungarian() ? ($month|firstUpper) : $month]|noescape}</p>
                </div>
            </div>
        </div>

    </div>

	<div class="float-wrapper__stop"></div>
</div>

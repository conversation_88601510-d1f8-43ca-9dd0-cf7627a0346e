{import 'components/form.latte'}

<!DOCTYPE html>
<html lang="{$localization->getLocale()}">
<head>
    <meta charset="utf-8">
    {capture $headTitle|spaceless|stripHtml}{ifset title}{include title}{/ifset}{/capture}
    <title>{if strlen($headTitle) > 50}{$headTitle}{else}{$headTitle} | Kaufino{/if}</title>
    <meta name="keywords" content="">
    <meta name="description" content="{ifset description}{include description|stripHtml}{/ifset}">
    <meta name="author" content="Kaufino">
    <meta name="robots" content="{block robots|stripHtml|trim}index,follow{/block}">
    <link rel="canonical" href="{$canonicalUrl}">

    <meta property="og:title" content="{if strlen($headTitle) > 50}{$headTitle}{else}{$headTitle} | Kaufino{/if}" />
    <meta property="og:site_name" content="Kaufino"/>
    <meta property="og:url" content="{link //this}" />
    <meta property="og:description" content="{ifset description}{include description|stripHtml}{/ifset}" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="{$basePath}/images/1200x627-og_kaufino.png" />
    <meta property="fb:app_id" content="" />

    <meta name="twitter:card" content="summary" />

    <!-- Viewport for mobile devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=1">

    <link rel="apple-touch-icon" sizes="180x180" href="{$basePath}/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="{$basePath}/images/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="{$basePath}/images/favicon/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="192x192"  href="{$basePath}/images/favicon/android-chrome-192x192.png">
    <link rel="icon" type="image/png" sizes="384x384"  href="{$basePath}/images/favicon/android-chrome-384x384.png">
    {*	<link rel="manifest" href="{$basePath}/images/favicon/site.webmanifest">*}
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">

    {ifset hreflang}{include hreflang}{/ifset}

    {var $version = 1.89}

    <link rel="stylesheet" href="{$basePath}/js/swiper/swiper-bundle.min.css" />
    <link rel="stylesheet" href="{$basePath}/css/main.front.css?v={$version}">
    <link rel="stylesheet" href="{$basePath}/css2/output.css">

<script>
	dataLayer = [{}];
</script>

	{block head}{/block}

    <script n:if="isset($googleOptimizeExperiment)">
			dataLayer.push({
				'expId': {$googleOptimizeExperiment->getExperimentId()},
				'expVar': {$googleOptimizeExperiment->getVariant()}
			});
    </script>

    <!-- Google Tag Manager -->
    <script n:syntax="off">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-T8M6QHZ');</script>
    <!-- End Google Tag Manager -->
</head>

<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T8M6QHZ"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<header class="h-[80px] relative flex items-center shadow-sm">
    <div class="container flex items-center">
        <a n:href="Homepage:default" class="k-header__logo-link">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 704 126" n:syntax="off">
                <path fill="#fedb2d" d="M8.28 59.902A51.447 51.447 0 0159.67 8.514h33.074V.234H59.669A59.669 59.669 0 000 59.902a59.669 59.669 0 0059.669 59.669h33.074v-8.28H59.669A51.447 51.447 0 018.281 59.9z"/>
                <path fill="#f8992a" d="M81.835 111.29h-7.897a51.388 51.388 0 110-102.776h7.897V.234h-7.898A59.669 59.669 0 0014.27 59.902a59.669 59.669 0 0059.668 59.669h7.898z"/>
                <circle fill="#fedb2d" cx="88.416" cy="60.047" r="60.047"/>
                <path fill="#f8992a" d="M117.834 19.096q1.802 1.297 3.482 2.745-1.679-1.447-3.482-2.745zM132.326 35.25q.43.759.833 1.534-.404-.775-.833-1.535zM116.093 17.894q.487.321.968.652-.48-.332-.968-.652zM41.622 78.852zM107.714 13.447c.283.117.563.24.843.362-.28-.123-.56-.245-.843-.362zM109.742 14.344c.289.136.578.27.864.41-.286-.14-.575-.275-.864-.41zM111.832 15.382q.582.306 1.156.626-.573-.32-1.156-.626zM128.237 29.12c.336.43.666.865.987 1.308q-.482-.664-.987-1.309zM105.66 12.643c.196.07.386.15.58.222-.194-.073-.384-.152-.58-.222zM129.31 30.547a50.5 50.5 0 012.913 4.524 50.441 50.441 0 00-2.914-4.524zM126.955 27.532zM123.364 23.706zM121.863 22.315q.518.46 1.022.934-.505-.473-1.022-.934zM124.515 24.845q1.214 1.246 2.342 2.573-1.128-1.326-2.342-2.573zM114.28 16.751c.314.188.623.384.932.578-.31-.194-.618-.39-.932-.578zM39.172 70.916zM38.66 68.252zM38.063 62.818zM38.29 65.552zM40.63 76.163zM39.827 73.547a49.94 49.94 0 01-.412-1.578q.193.794.412 1.578zM95.7 10.146c.166.024.333.042.497.068-.164-.026-.331-.044-.496-.068z"/>
                <path fill="#f8992a" d="M133.242 36.946a50.214 50.214 0 012.275 5.072A50.438 50.438 0 0042.972 81.94q-.574-1.191-1.085-2.417a50.435 50.435 0 1091.355-42.577z"/>
                <path fill="#f8992a" d="M98.418 10.61c.215.044.426.094.64.14-.214-.046-.425-.096-.64-.14zM90.768 9.674c.239.011.476.028.714.042-.238-.014-.475-.03-.714-.042zM102.9 11.737c.241.072.483.14.722.216-.239-.076-.48-.144-.721-.216zM100.638 11.114q.397.1.792.205-.395-.105-.792-.205zM93.142 9.842c.252.024.505.047.757.075-.252-.028-.505-.051-.757-.075z"/>
                <path d="M39.64 63.911a50.44 50.44 0 0195.877-21.893 50.214 50.214 0 00-2.275-5.072l-.083-.162q-.404-.775-.833-1.534l-.103-.179a50.5 50.5 0 00-2.914-4.524l-.085-.12a50.355 50.355 0 00-.987-1.308q-.13-.168-.263-.335a49.735 49.735 0 00-1.02-1.253l-.098-.113q-1.127-1.327-2.342-2.573l-.104-.11c-.343-.35-.694-.69-1.047-1.03a51.586 51.586 0 00-.479-.456q-.504-.474-1.021-.934c-.18-.16-.365-.317-.548-.474q-1.678-1.448-3.481-2.746c-.257-.184-.513-.37-.773-.55q-.48-.332-.969-.652a49.865 49.865 0 00-.88-.565c-.31-.194-.618-.39-.932-.578-.426-.255-.858-.5-1.292-.743q-.574-.32-1.157-.626a50.209 50.209 0 00-1.225-.627c-.286-.14-.576-.275-.865-.41q-.588-.276-1.184-.536c-.28-.122-.56-.245-.844-.362-.487-.202-.979-.394-1.474-.581-.193-.074-.384-.152-.579-.223a50.561 50.561 0 00-2.038-.69c-.24-.076-.481-.144-.722-.216q-.731-.22-1.47-.418-.395-.105-.792-.205c-.524-.13-1.051-.25-1.581-.364-.213-.046-.425-.096-.64-.14q-1.102-.222-2.22-.396c-.165-.026-.332-.044-.497-.068q-.896-.13-1.802-.23c-.252-.027-.505-.05-.757-.074q-.826-.076-1.66-.126c-.237-.014-.475-.03-.714-.042-.78-.036-1.562-.06-2.351-.06a50.432 50.432 0 00-50.432 50.433c0 .93.03 1.853.079 2.77.016.3.046.595.068.893.044.617.091 1.232.158 1.842.038.349.087.694.132 1.04.072.556.148 1.11.238 1.66.058.358.125.714.19 1.07.1.534.206 1.066.322 1.594q.117.529.243 1.053c.129.53.267 1.055.412 1.578.093.336.186.671.286 1.004.162.542.338 1.078.518 1.612.102.302.199.606.307.906.215.6.447 1.193.685 1.782.09.223.172.45.264.672q.514 1.224 1.086 2.417A50.29 50.29 0 0139.64 63.91z" fill="#f57f20"/>
                <path fill="#bc2026" d="M198.54 71.981a129.4 129.4 0 0014.102-14.422 133.04 133.04 0 0011.537-15.703h23.234q-5.61 8.495-12.419 16.986-6.813 8.494-14.662 16.505l18.428 26.92a11.847 11.847 0 003.125 3.205 6.907 6.907 0 003.766.962 10.455 10.455 0 006.25-2.083l-.641 12.659a16.159 16.159 0 01-5.449 2.563 25.56 25.56 0 01-7.05.962 19.927 19.927 0 01-5.93-.802 15.75 15.75 0 01-4.726-2.403 22.874 22.874 0 01-4.167-4.086q-2.005-2.483-4.246-5.85l-12.819-19.709q-2.085 1.765-4.167 3.284-2.084 1.525-4.166 2.965v25.639h-21.312V25.03h-9.295V9.968h25.64q2.722 0 3.845 1.282a5.715 5.715 0 011.121 3.846zM335.064 116.528a20.026 20.026 0 01-6.17 2.885 28.284 28.284 0 01-8.091 1.122 18.32 18.32 0 01-8.734-2.004 10.046 10.046 0 01-5.048-6.17 20.342 20.342 0 01-8.813 6.41 32.529 32.529 0 01-12.178 2.244q-12.499 0-20.03-6.65-7.534-6.647-7.531-19.148 0-12.98 8.332-19.63 8.33-6.648 22.113-6.65a50.213 50.213 0 018.413.721 41.483 41.483 0 017.291 1.843v-2.083a27.048 27.048 0 00-.641-6.41 11.433 11.433 0 00-2.403-4.646q-3.689-4.166-12.98-4.167a49.75 49.75 0 00-11.618 1.442 51.684 51.684 0 00-11.617 4.327V45.542a53.59 53.59 0 0112.499-3.846 76.93 76.93 0 0114.1-1.282q18.426 0 27.082 8.172a18.993 18.993 0 015.287 8.253 37.495 37.495 0 011.603 11.617v33.17q0 4.808 3.686 4.808a10.822 10.822 0 003.365-.642 8.941 8.941 0 002.884-1.441zm-30.446-33.49a21.479 21.479 0 00-4.887-1.362 34.965 34.965 0 00-6.17-.56q-7.053 0-10.336 3.765a13.748 13.748 0 00-3.285 9.374q0 6.412 3.285 9.694a12.006 12.006 0 008.893 3.285q5.927 0 9.295-3.685 3.203-3.523 3.205-9.775zM424.958 116.368a27.377 27.377 0 01-6.41 2.965 25.2 25.2 0 01-7.852 1.202 18.213 18.213 0 01-8.412-1.924 10.973 10.973 0 01-5.368-6.089 26.444 26.444 0 01-9.534 6.25 34.97 34.97 0 01-12.9 2.243q-14.103 0-21.312-7.53a23.03 23.03 0 01-5.208-9.055 40.87 40.87 0 01-1.522-11.778V41.856h21.312v47.432a37.059 37.059 0 00.48 6.49 12.013 12.013 0 002.083 4.887q3.366 4.326 10.095 4.326a14.034 14.034 0 006.49-1.362 13.19 13.19 0 004.407-3.606 14.093 14.093 0 002.563-5.848 41.194 41.194 0 00.642-7.772V41.856h21.311v59.77q0 4.808 3.846 4.808a10.118 10.118 0 003.285-.642 9.027 9.027 0 002.805-1.441zM485.848 24.87a40.982 40.982 0 00-6.25-1.602 37.816 37.816 0 00-6.73-.641q-5.449 0-7.53 2.885-2.085 2.564-2.084 8.492v7.852h18.908V56.92h-18.908v62.654H441.94V56.92h-12.177V44.9l12.177-3.044v-7.05a46.368 46.368 0 011.203-11.297 19.417 19.417 0 014.566-8.413q6.57-7.05 20.832-7.051a56.358 56.358 0 019.054.801 43.194 43.194 0 018.252 2.084zm3.845 32.049V41.856h25.639q2.723 0 3.845 1.282a5.715 5.715 0 011.122 3.845v54.643q0 4.807 3.846 4.807a10.118 10.118 0 003.285-.641 9.01 9.01 0 002.804-1.442l-.64 12.178a21.813 21.813 0 01-6.01 2.805 26.491 26.491 0 01-8.092 1.202q-9.614 0-13.62-5.448a13.91 13.91 0 01-2.243-5.449 35.905 35.905 0 01-.642-7.05v-45.67zm31.087-36.215a11.74 11.74 0 01-3.285 8.413 10.923 10.923 0 01-8.252 3.445 11.705 11.705 0 01-8.493-3.445 11.311 11.311 0 01-3.525-8.413 11.764 11.764 0 0111.858-11.858 11.187 11.187 0 018.332 3.446 11.586 11.586 0 013.365 8.412zM562.763 51.79a28.053 28.053 0 0110.095-8.251 32.582 32.582 0 0114.742-3.125q14.1 0 21.313 7.531A23.117 23.117 0 01614.12 57a40.934 40.934 0 011.522 11.777v50.797h-21.31V72.142a36.95 36.95 0 00-.482-6.49 12.05 12.05 0 00-2.083-4.887q-3.365-4.327-10.095-4.326a14.075 14.075 0 00-6.49 1.361 13.24 13.24 0 00-4.406 3.605 14.108 14.108 0 00-2.564 5.85 41.071 41.071 0 00-.641 7.772v44.547h-21.313V56.919h-9.294V41.856h19.39a6.02 6.02 0 013.845 1.041 6.084 6.084 0 011.763 3.766zM703.614 80.795q0 18.91-9.694 29.564-9.697 10.658-28.122 10.656-18.43 0-28.122-10.656-9.698-10.655-9.696-29.564 0-18.907 9.696-29.645 9.692-10.734 28.122-10.736 18.425 0 28.122 10.736 9.694 10.738 9.694 29.645zm-21.632 0a55.515 55.515 0 00-1.122-12.019 23.283 23.283 0 00-3.205-8.012 12.27 12.27 0 00-5.127-4.406 16.317 16.317 0 00-6.73-1.362 13.951 13.951 0 00-11.859 5.849q-4.325 5.852-4.326 19.95a54.158 54.158 0 001.122 11.937 23.108 23.108 0 003.204 7.933 12.215 12.215 0 005.128 4.406 17.307 17.307 0 0013.461 0 12.242 12.242 0 005.127-4.406 23.21 23.21 0 003.205-7.933 54.348 54.348 0 001.122-11.937z"/>
            </svg>
        </a>

        <form class="k-header__search">
            <input required type="text" class="k-header__search-input js-search-input" data-search-url="{link Ajax:search}" placeholder="{_kaufino.navbar.search.placeholder}">
            <input type="submit" class="k-header__search-submit js-search-submit" data-search-url="{link Search:search, q => 'q'}" value="{_kaufino.navbar.search.submit}">

            <div class="k-header__search-wrapper">
            </div>
        </form>

        <div class="k-header__nav hidden relative w-auto ml-auto top-0 lg:flex">
            <a href="{link Shops:shops}" class="k-header__nav-item">{_kaufino.navbar.shops}</a>

            <span class="k-header__nav-separator">|</span>

            {foreach $headerShops() as $headerShop}
                {if $iterator->first}
                    <div class="relative w-full">
                    <a href="{link Leaflets:leaflets}" class="k-header__nav-item k-header__nav-item--more">{_kaufino.navbar.leaflets}</a>

                    <div class="k-header__nav-dropdown">
                {/if}
                <a n:href="Shop:shop $headerShop" class="k-header__nav-dropdown-item">{$headerShop->getName()}</a>

                {if $iterator->last}
                    </div>
                    </div>
                {/if}
            {/foreach}

            <span class="k-header__nav-separator">|</span>
            <a href="{link Offers:offers}" class="k-header__nav-item">{_kaufino.navbar.offers}</a>
        </div>

        <button class="k-header__menu-icon relative flex items-center justify-center lg:hidden ml-4">
            <svg class="svg-inline--fa fa-bars fa-w-14 relative block w-[20px] h-[20px] text-primary outline-none" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bars" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"></path></svg>
        </button>
    </div>
</header>

{var $footerShopsTags = $footerShopsTags()}
<div n:if="$footerShopsTags && count($footerShopsTags) >= 7" class="k-header__category">
    <div class="container container--flex">
        <a n:foreach="$footerShopsTags as $footerTag" n:href="Tag:tag $footerTag" class="k-header__category-item d-block fz-m color-black td-none td-hover-underline {if $footerTag->isSeasonal() === true}fw-bold{/if}">{$footerTag->getName()}</a>
    </div>
</div>

<div n:foreach="$flashes as $flash" n:class="alert, 'alert-' . $flash->type">{$flash->message}</div>

{block breadcrumb}{/block}

{include content}

{if in_array($presenterName, ['Kaufino:Shop', 'Kaufino:Tag', 'Kaufino:City', 'Kaufino:Article']) && $user->isLoggedIn()}
    <div class="k-page-extension">
        <span n:class="$pageExtension && $pageExtension->getTitle() ? k-page-extension__tag--green ,k-page-extension__tag">MT</span>
        <span n:class="$pageExtension && $pageExtension->getDescription() ? k-page-extension__tag--green ,k-page-extension__tag">MD</span>
        <span n:class="$pageExtension && $pageExtension->getHeading() ? k-page-extension__tag--green ,k-page-extension__tag">H1</span>
        <span n:class="$pageExtension && $pageExtension->getKeywords() ? k-page-extension__tag--green ,k-page-extension__tag">KW</span>

        {if $presenterName == 'Kaufino:Shop'}
            {var $shopAlternativeNames = $shop->getAlternativeNames()}

            <span n:class="$shopAlternativeNames && count($shopAlternativeNames|explode) ? k-page-extension__tag--green ,k-page-extension__tag">AN</span>
            <a n:href=":Admin:Shop:shop $shop->getId()" class="k-page-extension__btn" target="_blank">Edit shop</a>
        {/if}

			{if $presenterName == 'Kaufino:Tag'}
            <a n:href=":Admin:Tag:tag $tag->getId()" class="k-page-extension__btn" target="_blank">Edit tag</a>
        {/if}

			{if $presenterName == 'Kaufino:Article'}
            <a n:href=":Admin:Article:article $article->getId()" class="k-page-extension__btn" target="_blank">Edit article</a>
        {/if}

			{if $pageExtension}
            <a n:href=":Admin:Seo:pageExtension $pageExtension->getId()" class="k-page-extension__btn" target="_blank">Edit page extension</a>

        {var $shopKeywords = $pageExtension->getKeywords()}
            <span class="k-alternative-name js-alternative-name" data-alternative-name="{$shopKeywords}"></span>
        {else}
            <a n:href=":Admin:Seo:pageExtension id => null, websiteId => $website->getId(), slug => $pageExtensionSlug" class="k-page-extension__btn" target="_blank">Edit page extension</a>
        {/if}

        <a n:href=":Admin:Translations:Dictionary:dictionary dictionary => 'kaufino', localizationId => $localization->getId()" class="k-page-extension__btn" target="_blank">Translations</a>
    </div>
{/if}

<footer class="k-footer mt-5">
    <div class="container">
        <div class="k-footer__wrapper">
            <div class="k-footer__column k-footer__column--first">
                <a n:href="Homepage:default region => 'no'" class="d-block mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 703.614 121.016" n:syntax="off">
                        <path fill="#fedb2d" d="M8.28 59.902A51.447 51.447 0 0159.67 8.514h33.074V.234H59.669A59.669 59.669 0 000 59.902a59.669 59.669 0 0059.669 59.669h33.074v-8.28H59.669A51.447 51.447 0 018.281 59.9z"/>
                        <path fill="#f8992a" d="M81.835 111.29h-7.897a51.388 51.388 0 110-102.776h7.897V.234h-7.898A59.669 59.669 0 0014.27 59.902a59.669 59.669 0 0059.668 59.669h7.898z"/>
                        <circle fill="#fedb2d" cx="88.416" cy="60.047" r="60.047"/>
                        <path fill="#f8992a" d="M117.834 19.096q1.802 1.297 3.482 2.745-1.679-1.447-3.482-2.745zM132.326 35.25q.43.759.833 1.534-.404-.775-.833-1.535zM116.093 17.894q.487.321.968.652-.48-.332-.968-.652zM41.622 78.852zM107.714 13.447c.283.117.563.24.843.362-.28-.123-.56-.245-.843-.362zM109.742 14.344c.289.136.578.27.864.41-.286-.14-.575-.275-.864-.41zM111.832 15.382q.582.306 1.156.626-.573-.32-1.156-.626zM128.237 29.12c.336.43.666.865.987 1.308q-.482-.664-.987-1.309zM105.66 12.643c.196.07.386.15.58.222-.194-.073-.384-.152-.58-.222zM129.31 30.547a50.5 50.5 0 012.913 4.524 50.441 50.441 0 00-2.914-4.524zM126.955 27.532zM123.364 23.706zM121.863 22.315q.518.46 1.022.934-.505-.473-1.022-.934zM124.515 24.845q1.214 1.246 2.342 2.573-1.128-1.326-2.342-2.573zM114.28 16.751c.314.188.623.384.932.578-.31-.194-.618-.39-.932-.578zM39.172 70.916zM38.66 68.252zM38.063 62.818zM38.29 65.552zM40.63 76.163zM39.827 73.547a49.94 49.94 0 01-.412-1.578q.193.794.412 1.578zM95.7 10.146c.166.024.333.042.497.068-.164-.026-.331-.044-.496-.068z"/>
                        <path fill="#f8992a" d="M133.242 36.946a50.214 50.214 0 012.275 5.072A50.438 50.438 0 0042.972 81.94q-.574-1.191-1.085-2.417a50.435 50.435 0 1091.355-42.577z"/>
                        <path fill="#f8992a" d="M98.418 10.61c.215.044.426.094.64.14-.214-.046-.425-.096-.64-.14zM90.768 9.674c.239.011.476.028.714.042-.238-.014-.475-.03-.714-.042zM102.9 11.737c.241.072.483.14.722.216-.239-.076-.48-.144-.721-.216zM100.638 11.114q.397.1.792.205-.395-.105-.792-.205zM93.142 9.842c.252.024.505.047.757.075-.252-.028-.505-.051-.757-.075z"/>
                        <path d="M39.64 63.911a50.44 50.44 0 0195.877-21.893 50.214 50.214 0 00-2.275-5.072l-.083-.162q-.404-.775-.833-1.534l-.103-.179a50.5 50.5 0 00-2.914-4.524l-.085-.12a50.355 50.355 0 00-.987-1.308q-.13-.168-.263-.335a49.735 49.735 0 00-1.02-1.253l-.098-.113q-1.127-1.327-2.342-2.573l-.104-.11c-.343-.35-.694-.69-1.047-1.03a51.586 51.586 0 00-.479-.456q-.504-.474-1.021-.934c-.18-.16-.365-.317-.548-.474q-1.678-1.448-3.481-2.746c-.257-.184-.513-.37-.773-.55q-.48-.332-.969-.652a49.865 49.865 0 00-.88-.565c-.31-.194-.618-.39-.932-.578-.426-.255-.858-.5-1.292-.743q-.574-.32-1.157-.626a50.209 50.209 0 00-1.225-.627c-.286-.14-.576-.275-.865-.41q-.588-.276-1.184-.536c-.28-.122-.56-.245-.844-.362-.487-.202-.979-.394-1.474-.581-.193-.074-.384-.152-.579-.223a50.561 50.561 0 00-2.038-.69c-.24-.076-.481-.144-.722-.216q-.731-.22-1.47-.418-.395-.105-.792-.205c-.524-.13-1.051-.25-1.581-.364-.213-.046-.425-.096-.64-.14q-1.102-.222-2.22-.396c-.165-.026-.332-.044-.497-.068q-.896-.13-1.802-.23c-.252-.027-.505-.05-.757-.074q-.826-.076-1.66-.126c-.237-.014-.475-.03-.714-.042-.78-.036-1.562-.06-2.351-.06a50.432 50.432 0 00-50.432 50.433c0 .93.03 1.853.079 2.77.016.3.046.595.068.893.044.617.091 1.232.158 1.842.038.349.087.694.132 1.04.072.556.148 1.11.238 1.66.058.358.125.714.19 1.07.1.534.206 1.066.322 1.594q.117.529.243 1.053c.129.53.267 1.055.412 1.578.093.336.186.671.286 1.004.162.542.338 1.078.518 1.612.102.302.199.606.307.906.215.6.447 1.193.685 1.782.09.223.172.45.264.672q.514 1.224 1.086 2.417A50.29 50.29 0 0139.64 63.91z" fill="#f57f20"/>
                        <path fill="#bc2026" d="M198.54 71.981a129.4 129.4 0 0014.102-14.422 133.04 133.04 0 0011.537-15.703h23.234q-5.61 8.495-12.419 16.986-6.813 8.494-14.662 16.505l18.428 26.92a11.847 11.847 0 003.125 3.205 6.907 6.907 0 003.766.962 10.455 10.455 0 006.25-2.083l-.641 12.659a16.159 16.159 0 01-5.449 2.563 25.56 25.56 0 01-7.05.962 19.927 19.927 0 01-5.93-.802 15.75 15.75 0 01-4.726-2.403 22.874 22.874 0 01-4.167-4.086q-2.005-2.483-4.246-5.85l-12.819-19.709q-2.085 1.765-4.167 3.284-2.084 1.525-4.166 2.965v25.639h-21.312V25.03h-9.295V9.968h25.64q2.722 0 3.845 1.282a5.715 5.715 0 011.121 3.846zM335.064 116.528a20.026 20.026 0 01-6.17 2.885 28.284 28.284 0 01-8.091 1.122 18.32 18.32 0 01-8.734-2.004 10.046 10.046 0 01-5.048-6.17 20.342 20.342 0 01-8.813 6.41 32.529 32.529 0 01-12.178 2.244q-12.499 0-20.03-6.65-7.534-6.647-7.531-19.148 0-12.98 8.332-19.63 8.33-6.648 22.113-6.65a50.213 50.213 0 018.413.721 41.483 41.483 0 017.291 1.843v-2.083a27.048 27.048 0 00-.641-6.41 11.433 11.433 0 00-2.403-4.646q-3.689-4.166-12.98-4.167a49.75 49.75 0 00-11.618 1.442 51.684 51.684 0 00-11.617 4.327V45.542a53.59 53.59 0 0112.499-3.846 76.93 76.93 0 0114.1-1.282q18.426 0 27.082 8.172a18.993 18.993 0 015.287 8.253 37.495 37.495 0 011.603 11.617v33.17q0 4.808 3.686 4.808a10.822 10.822 0 003.365-.642 8.941 8.941 0 002.884-1.441zm-30.446-33.49a21.479 21.479 0 00-4.887-1.362 34.965 34.965 0 00-6.17-.56q-7.053 0-10.336 3.765a13.748 13.748 0 00-3.285 9.374q0 6.412 3.285 9.694a12.006 12.006 0 008.893 3.285q5.927 0 9.295-3.685 3.203-3.523 3.205-9.775zM424.958 116.368a27.377 27.377 0 01-6.41 2.965 25.2 25.2 0 01-7.852 1.202 18.213 18.213 0 01-8.412-1.924 10.973 10.973 0 01-5.368-6.089 26.444 26.444 0 01-9.534 6.25 34.97 34.97 0 01-12.9 2.243q-14.103 0-21.312-7.53a23.03 23.03 0 01-5.208-9.055 40.87 40.87 0 01-1.522-11.778V41.856h21.312v47.432a37.059 37.059 0 00.48 6.49 12.013 12.013 0 002.083 4.887q3.366 4.326 10.095 4.326a14.034 14.034 0 006.49-1.362 13.19 13.19 0 004.407-3.606 14.093 14.093 0 002.563-5.848 41.194 41.194 0 00.642-7.772V41.856h21.311v59.77q0 4.808 3.846 4.808a10.118 10.118 0 003.285-.642 9.027 9.027 0 002.805-1.441zM485.848 24.87a40.982 40.982 0 00-6.25-1.602 37.816 37.816 0 00-6.73-.641q-5.449 0-7.53 2.885-2.085 2.564-2.084 8.492v7.852h18.908V56.92h-18.908v62.654H441.94V56.92h-12.177V44.9l12.177-3.044v-7.05a46.368 46.368 0 011.203-11.297 19.417 19.417 0 014.566-8.413q6.57-7.05 20.832-7.051a56.358 56.358 0 019.054.801 43.194 43.194 0 018.252 2.084zm3.845 32.049V41.856h25.639q2.723 0 3.845 1.282a5.715 5.715 0 011.122 3.845v54.643q0 4.807 3.846 4.807a10.118 10.118 0 003.285-.641 9.01 9.01 0 002.804-1.442l-.64 12.178a21.813 21.813 0 01-6.01 2.805 26.491 26.491 0 01-8.092 1.202q-9.614 0-13.62-5.448a13.91 13.91 0 01-2.243-5.449 35.905 35.905 0 01-.642-7.05v-45.67zm31.087-36.215a11.74 11.74 0 01-3.285 8.413 10.923 10.923 0 01-8.252 3.445 11.705 11.705 0 01-8.493-3.445 11.311 11.311 0 01-3.525-8.413 11.764 11.764 0 0111.858-11.858 11.187 11.187 0 018.332 3.446 11.586 11.586 0 013.365 8.412zM562.763 51.79a28.053 28.053 0 0110.095-8.251 32.582 32.582 0 0114.742-3.125q14.1 0 21.313 7.531A23.117 23.117 0 01614.12 57a40.934 40.934 0 011.522 11.777v50.797h-21.31V72.142a36.95 36.95 0 00-.482-6.49 12.05 12.05 0 00-2.083-4.887q-3.365-4.327-10.095-4.326a14.075 14.075 0 00-6.49 1.361 13.24 13.24 0 00-4.406 3.605 14.108 14.108 0 00-2.564 5.85 41.071 41.071 0 00-.641 7.772v44.547h-21.313V56.919h-9.294V41.856h19.39a6.02 6.02 0 013.845 1.041 6.084 6.084 0 011.763 3.766zM703.614 80.795q0 18.91-9.694 29.564-9.697 10.658-28.122 10.656-18.43 0-28.122-10.656-9.698-10.655-9.696-29.564 0-18.907 9.696-29.645 9.692-10.734 28.122-10.736 18.425 0 28.122 10.736 9.694 10.738 9.694 29.645zm-21.632 0a55.515 55.515 0 00-1.122-12.019 23.283 23.283 0 00-3.205-8.012 12.27 12.27 0 00-5.127-4.406 16.317 16.317 0 00-6.73-1.362 13.951 13.951 0 00-11.859 5.849q-4.325 5.852-4.326 19.95a54.158 54.158 0 001.122 11.937 23.108 23.108 0 003.204 7.933 12.215 12.215 0 005.128 4.406 17.307 17.307 0 0013.461 0 12.242 12.242 0 005.127-4.406 23.21 23.21 0 003.205-7.933 54.348 54.348 0 001.122-11.937z"/>
                    </svg>
                </a>
                <p class="fz-s color-grey mb-2">Copyright © {date('Y')} {_kaufino.footer.copyright}
						{if $user->isLoggedIn()}
                        ({$geoCountry})
                    {/if}
                </p>
            </div>
            <div class="k-footer__column">
                <div class="k-footer__wrapper">
                    <div class="k-footer__column">
                        <p>
                            <strong class="d-block fz-m color-grey tt-uppercase mb-4">{_kaufino.footer.shops}:</strong>
                            <a n:foreach="$footerShops() as $footerShop" n:href="Shop:shop $footerShop" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$footerShop->getName()}</a>
                        </p>
                    </div>

                    <div class="k-footer__column">
                        <p>
                            <strong class="d-block fz-m color-grey tt-uppercase mb-4">{_kaufino.footer.category}:</strong>
                            <a n:foreach="$footerShopsTags as $footerTag" n:href="Tag:tag $footerTag" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$footerTag->getName()}</a>
                        </p>
                    </div>

                    {var $footerOffersTags = $footerOffersTags()}
                    <div n:if="$footerOffersTags" class="k-footer__column">
                        <p>
                            <strong class="d-block fz-m color-grey tt-uppercase mb-4">{_kaufino.footer.offersCategory}:</strong>
                            <a n:foreach="$footerOffersTags as $footerTag" n:href="Tag:tag $footerTag" class="d-block fz-m color-black td-none td-hover-underline mb-4">{$footerTag->getName()}</a>
                        </p>
                    </div>

                    <div class="k-footer__column">
                        <p>
                            <strong class="d-block fz-m color-grey tt-uppercase mb-4">{_kaufino.footer.aboutKaufino}:</strong>
                            <a n:href="Leaflets:leaflets" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.footer.leaflets}</a>
                            <a n:href="Shops:shops" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.footer.shops}</a>

                            {if count($cities) > 0}
                                <a n:href="Cities:cities" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_'kaufino.navbar.cities'}</a>
                            {/if}

								{if $website->isKaufino() && $localization->hasArticles()}
                                <a href="{link Articles:articles}" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.navbar.articles}</a>
                            {/if}

								<a n:href="Static:aboutUs" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.footer.aboutUs}</a>
								<a n:href="Static:cookies" class="d-block fz-m color-black td-none td-hover-underline mb-4">{_kaufino.footer.cookies}</a>

								{* Skryto
									<a n:href="Static:privacy" class="d-block fz-m color-black td-none td-hover-underline mb-4">Nastavení ochrany soukromí</a>
									<a n:href="Static:cookies" class="d-block fz-m color-black td-none td-hover-underline mb-4">Zásady užívání cookies</a>
									<a n:href="Static:termsOfUse" class="d-block fz-m color-black td-none td-hover-underline mb-4">Podmínky užívání</a>
								*}
                        </p>
                    </div>

                    <div class="k-footer__column">
                        <p>
                            <strong class="d-block fz-m color-grey tt-uppercase mb-4">{_kaufino.footer.nextCountries}:</strong>
                            {foreach $footerWebsites() as $footerWebsite}
                                {continueIf $footerWebsite === $website}

                                <a href="{$footerWebsite->getDomain()}" class="d-block fz-m color-black td-none td-hover-underline mb-4">
                                    {$footerWebsite->getLocalization()->getOriginalName()}
                                </a>
                            {/foreach}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

{block scripts}
    {*<script src="{$basePath}/js/lazysizes/lazysizes.min.js" async></script>*}
    <script src="{$basePath}/js/main.js?v={$version}" async></script>

{if isset($userLoggedIn) && $pageExtension && $pageExtension->getKeywords()}
    <script src="{$basePath}/js/page-extension.js?v={$version}" async></script>
{/if}

    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4233432057183172" crossorigin="anonymous"></script>
{/block}

<script src="{$basePath}/js/swiper/swiper-bundle.min.js"></script>
</body>
</html>
<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\ChatGPT\ChatGPTClient;
use <PERSON><PERSON>ino\Model\Shops\ContentBlockFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\ContentBlock;

class GenerateContentBlocks extends Job
{
	/** @var ContentBlockFacade */
	private $contentBlockFacade;

	/** @var ChatGPTClient */
	private $chatGPTClient;

	public function __construct(
		ContentBlockFacade $contentBlockFacade,
		ChatGPTClient $chatGPTClient
	) {
		parent::__construct();

		$this->contentBlockFacade = $contentBlockFacade;
		$this->chatGPTClient = $chatGPTClient;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:generate-content-blocks');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->generateContentBlocks();

		$this->onFinish($log);
	}

	private function generateContentBlocks(): void
	{
		$contentBlocks = $this->contentBlockFacade->findContentBlocksToGenerate();

		/** @var ContentBlock $contentBlock */
		foreach ($contentBlocks as $contentBlock) {
			echo "\nGeneruji obsah pro content block: " . $contentBlock->getId();
			$start = time();
			$generatedContent = $this->chatGPTClient->getCompletion($contentBlock->getContentBlockType()->getDefaultPrompt());
			echo "\nČas: " . (time() - $start) . "s\n";

			$contentBlock->setGeneratedContent($generatedContent);
			$this->contentBlockFacade->saveContentBlock($contentBlock);
		}
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\StoreFacade;
use <PERSON><PERSON><PERSON>\Model\SteveClient;
use Nette\Utils\Strings;

class ProcessStores extends Job
{
	private SteveClient $steveClient;
	private StoreFacade $storeFacade;
	private ShopFacade $shopFacade;
	private GeoFacade $geoFacade;
	private LocalizationFacade $localizationFacade;

	public function __construct(SteveClient $steveClient, StoreFacade $storeFacade, ShopFacade $shopFacade, GeoFacade $geoFacade, LocalizationFacade $localizationFacade)
	{
		parent::__construct();

		$this->steveClient = $steveClient;
		$this->storeFacade = $storeFacade;
		$this->shopFacade = $shopFacade;
		$this->geoFacade = $geoFacade;
		$this->localizationFacade = $localizationFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-stores');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->processStores();

		$this->onFinish($log);
	}

	private function processStores(): void
	{
		foreach ($this->steveClient->getStores() as $storeItem) {
			$store = $this->storeFacade->findByStoreId($storeItem->id);

			if ($storeItem->street === null || $storeItem->houseNumber === null || $storeItem->city === null || $storeItem->zipCode === null) {
				continue;
			}

			$slug = $this->resolveSlug($storeItem->street, $storeItem->houseNumber);

			$shop = $this->shopFacade->findShopByShopId($storeItem->shop->id);

			if ($shop === null) {
				continue;
			}

			$fullAddress = Strings::firstUpper($shop->getName()) . ' ' . $storeItem->street . ($storeItem->houseNumber ? ' ' . $storeItem->houseNumber : '') . ', ' . $storeItem->zipCode . ' ' . $storeItem->city->name;

			if ($store === null) {
				$city = $this->geoFacade->findCityByCityId($storeItem->city->id);

				if ($city === null) {
					continue;
				}

				$store = $this->storeFacade->createStore(
					$this->localizationFacade->findLocalizationFromSteveLocale($storeItem->locale),
					$shop,
					$city,
					$storeItem->name,
					$fullAddress,
					$storeItem->id,
					$storeItem->type,
					$storeItem->municipality,
					$storeItem->street,
					$storeItem->houseNumber,
					$storeItem->zipCode,
					$storeItem->lat,
					$storeItem->lng,
					$slug
				);
			} else {
				$store->update();
			}

			if ($shop->hasCity($store->getCity()) === false) {
				$shop->addCity($store->getCity());
				$this->shopFacade->saveShop($shop);
			}

			$store->setFullAddress($fullAddress);
			$store->setOpeningHours($storeItem->openingHours);
			$store->setPhoneNumber($storeItem->phoneNumber);
			$store->setWebsiteUrl($storeItem->websiteUrl);
			$store->setStoreUrl($storeItem->storeUrl);
			$store->setEmail($storeItem->email);

			$store->setSlug($slug);

			$this->storeFacade->saveStore($store);
		}
	}

	private function resolveSlug(string $address, ?string $houseNumber)
	{
		$slug = Strings::webalize($address);

		if ($houseNumber !== null) {
			$slug .= '-' . $houseNumber;
		}

		return Strings::webalize($slug);
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use Nette\Database\Explorer;

class ImportSqlData extends Job
{
	private Explorer $explorer;

	public function __construct(Explorer $explorer)
	{
		parent::__construct();

		$this->explorer = $explorer;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:import-sql-data:run');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->importSqlData();

		$this->onFinish($log);
	}

	private function importSqlData()
	{
		$data = file_get_contents('bin/import.sql');

		$this->explorer->query($data);
	}
}

<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Offers\Entities\Offer;
use <PERSON><PERSON><PERSON>\Model\Offers\OfferFacade;
use <PERSON>\Debugger;

class RemoveOfferImages extends Job
{
	private const DIR = __DIR__ . '/../../www';

	private OfferFacade $offerFacade;

	public function __construct(OfferFacade $offerFacade)
	{
		parent::__construct();

		$this->offerFacade = $offerFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:remove-offer-images:run');
	}

	public function start(): void
	{
		/** @var Offer $offer */
		foreach ($this->offerFacade->findOffersToRemove() as $offer) {
			if ($offer->getImageUrl() !== null) {
				$filePath = self::DIR . $offer->getImageUrl();

				if (file_exists($filePath)) {
					unlink($filePath);
				}
			}

			$offer->remove();

			$this->offerFacade->saveOffer($offer);
		}
	}
}

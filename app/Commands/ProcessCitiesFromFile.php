<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\ShopFacade;
use Tracy\Debugger;

class ProcessCitiesFromFile extends Job
{
	private GeoFacade $geoFacade;

	private LocalizationFacade $localizationFacade;

	private Localization $localization;

	private ShopFacade $shopFacade;

	private const SLUG_REPLACEMENT = [
		'md' => 'md-discount',
		'expert' => 'expert-elektro',
		'penny' => 'penny-market',
		'deco' => 'deco-superstore',
	];

	public function __construct(GeoFacade $geoFacade, LocalizationFacade $localizationFacade, ShopFacade $shopFacade)
	{
		parent::__construct();

		$this->geoFacade = $geoFacade;
		$this->localizationFacade = $localizationFacade;
		$this->shopFacade = $shopFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-cities-from-file')
			->setDescription('Executes job.');
	}

	public function start(): void
	{
		$this->localization = $this->localizationFacade->findLocalization(6);

		$this->processCities();
	}

	public function processCities()
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', '2400');

		$urls = $this->getUrlsFromFile();

		$processedCities = [];

		$pairedTotal = 0;

		$missingShops = [];
		$missingCities = [];

		foreach ($urls as $url) {
			$defaultUrl = $url;
			$url = str_replace('https://www.doveconviene.it/', '', $url);
			[$citySlug, $type, $shopSlug] = explode('/', $url);

			if (isset(self::SLUG_REPLACEMENT[$shopSlug])) {
				$shopSlug = self::SLUG_REPLACEMENT[$shopSlug];
			}

			$city = $this->geoFacade->findCityBySlug($this->localization, $citySlug);

			if ($city === null) {
				if (in_array($citySlug, $missingCities) === false) {
					Debugger::log($citySlug, 'missing-cities');

					$missingCities[] = $citySlug;
				}

				continue;
			}

			$shop = $this->shopFacade->findShopBySlug($this->localization, $shopSlug);

			if ($shop === null) {
				if (in_array($shopSlug, $missingShops) === false) {
					Debugger::log($defaultUrl, 'missing-shops');

					$missingShops[] = $shopSlug;
				}

				continue;
			}

			if ($shop->hasCity($city)) {
				continue;
			}

			$pairedTotal++;
			Debugger::log($pairedTotal, 'paired-cities-to-brands-total');

			if ($city->isActive()) {
				if (isset($processedCities[$citySlug]) && $processedCities[$citySlug] >= 5) {
					continue;
				}

				$processedCities[$citySlug] = isset($processedCities[$citySlug]) ? $processedCities[$citySlug] + 1 : 1;
			}

			$shop->addCity($city);
			$this->shopFacade->saveShop($shop);
		}
	}

	private function getUrlsFromFile(): array
	{
		return file(__DIR__ . '/cities.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use DateTime;
use <PERSON><PERSON>ino\Model\Shops\ContentBlockFacade;
use <PERSON><PERSON>ino\Model\Shops\Entities\ContentBlockType;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Shop;
use <PERSON><PERSON>ino\Model\Shops\ShopFacade;
use Ka<PERSON>ino\Model\Websites\Entities\Website;
use Nette\Caching\Cache;
use Nette\Caching\Storage;

class PrepareContentBlocks extends Job
{
	/** @var ShopFacade */
	private $shopFacade;

	/** @var ContentBlockFacade */
	private $contentBlockFacade;

	/** @var Cache */
	private $cache;

	public function __construct(
		ShopFacade $shopFacade,
		ContentBlockFacade $contentBlockFacade,
		Storage $storage
	) {
		parent::__construct();

		$this->shopFacade = $shopFacade;
		$this->contentBlockFacade = $contentBlockFacade;
		$this->cache = new Cache($storage, 'kaufino:prepare-content-blocks');
	}

	protected function configure(): void
	{
		$this->setName('kaufino:prepare-content-blocks');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->prepareContentBlocks();

		$this->onFinish($log);
	}

	private function prepareContentBlocks(): void
	{
		$checkedIds = $this->cache->load('checkedIds') ? : [];

		$shops = $this->shopFacade->getShops();

		if (count($checkedIds) > 0) {
			$shops = $shops
				->andWhere('s.id NOT IN (:ids)')
				->setParameter('ids', $checkedIds)
			;
		}

		$shops = $shops->setMaxResults(20)->getQuery()->getResult();

		/** @var Shop $shop */
		foreach ($shops as $shop) {
			echo implode(" - ", [$shop->getId(), $shop->getLocalization()->getName(), $shop->getName()]) . "\n";
			$checkedIds[] = $shop->getId();

			$websiteTypes = [];
			// kaufino/leatdo
			if ($shop->isActiveLeaflets()) {
				$websiteTypes[] = Website::MODULE_KAUFINO;
				$websiteTypes[] = Website::MODULE_LETADO;
			}

			// oferto
			if ($shop->isActiveOferto()) {
				$websiteTypes[] = Website::MODULE_OFERTO;
			}

			// oferto com
			if ($shop->isActiveCoupons()) {
				$websiteTypes[] = Website::MODULE_OFERTO_COM;
			}

			/** @var string $websiteType */
			foreach ($websiteTypes as $websiteType) {
				$contentBlockTypes = $this->contentBlockFacade->findContentBlockTypesByWebsiteType($websiteType, 'shop');

				/** @var ContentBlockType $contentBlockType */
				foreach ($contentBlockTypes as $contentBlockType) {
					if ($this->contentBlockFacade->findContentBlockForShopByContentBlockType($shop, $contentBlockType) !== null) {
						continue;
					}

					$this->contentBlockFacade->createContentBlock(
						$contentBlockType,
						$shop,
						null, // @todo
						null,
						null,
						null
					);
				}
			}
		}

		$this->cache->save('checkedIds', $checkedIds);
	}
}

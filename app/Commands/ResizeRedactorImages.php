<?php

declare(strict_types=1);

namespace Ka<PERSON>ino\Commands;

use Nette\Utils\Image;
use Nette\Utils\Finder;
use <PERSON>\Debugger;

class ResizeRedactorImages extends Job
{
	private const UPLOAD_DIR = __DIR__ . '/../../www/upload/rdctr';
	private const MAX_SIZE = 1200;

	protected function configure(): void
	{
		$this->setName('kaufino:resize-redactor-images');
		$this->setDescription('Zmenší obrázky v redactor složce na max 1200px a přepíše originály');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$processedCount = 0;
		$errorCount = 0;

		echo "Začínám zpracování obrázků v: " . self::UPLOAD_DIR . "\n";
		echo "POZOR: Originální soubory budou přepsány!\n";
		echo "Ujistěte se, že máte <PERSON> (ZIP) před spuštěním tohoto commandu.\n\n";

		if (!is_dir(self::UPLOAD_DIR)) {
			echo "Složka neexistuje: " . self::UPLOAD_DIR . "\n";
			$this->onFinish($log, "Složka neexistuje");
			return;
		}

		// Najdeme všechny obrázky ve složce
		$files = Finder::findFiles('*.jpg', '*.jpeg', '*.png')->in(self::UPLOAD_DIR);

		foreach ($files as $file) {
			try {
				$filePath = $file->getPathname();
				$fileName = $file->getBasename();

				echo "Zpracovávám: $fileName\n";

				// Načteme obrázek
				$image = Image::fromFile($filePath);

				// Zjistíme původní rozměry
				$originalWidth = $image->getWidth();
				$originalHeight = $image->getHeight();

				echo "  Původní rozměry: {$originalWidth}x{$originalHeight}\n";

				// Pokud je obrázek menší než MAX_SIZE, přeskočíme ho
				if ($originalWidth <= self::MAX_SIZE && $originalHeight <= self::MAX_SIZE) {
					echo "  Obrázek je již dostatečně malý, přeskakuji\n";
					continue;
				}

				// Změníme velikost - zachováme poměr stran
				if ($originalWidth > $originalHeight) {
					// Landscape - omezíme šířku
					$newWidth = self::MAX_SIZE;
					$newHeight = null;
				} else {
					// Portrait nebo čtverec - omezíme výšku
					$newWidth = null;
					$newHeight = self::MAX_SIZE;
				}

				$image->resize($newWidth, $newHeight);

				$newWidth = $image->getWidth();
				$newHeight = $image->getHeight();
				echo "  Nové rozměry: {$newWidth}x{$newHeight}\n";

				// Uložíme zmenšený obrázek se stejným názvem (přepíšeme originál)
				$image->save($filePath, 90); // 90% kvalita

				echo "  Uloženo (přepsán originál)\n";
				$processedCount++;
			} catch (\Exception $e) {
				echo "Chyba při zpracování $fileName: " . $e->getMessage() . "\n";
				$errorCount++;
				Debugger::log($e);
			}
		}

		$note = "Zpracováno: $processedCount souborů, chyby: $errorCount";
		echo "\n$note\n";

		$this->onFinish($log, $note);
	}
}

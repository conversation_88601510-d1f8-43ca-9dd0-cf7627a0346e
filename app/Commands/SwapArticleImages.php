<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use Nette\Utils\Finder;
use Symfony\Component\Console\Input\InputOption;

class SwapArticleImages extends Job
{
	private const UPLOAD_DIR = __DIR__ . '/../../www/upload/articles/image';

	protected function configure(): void
	{
		$this->setName('kaufino:swap-article-images');
		$this->setDescription('Přepíná mezi původními a zmenšenými obrázky článků');
		$this->addOption(
			'mode',
			'm',
			InputOption::VALUE_REQUIRED,
			'Režim: use-resized (přepne na zmenšené), use-original (vrátí původní), nebo cleanup-backups (smaže _backup soubory)',
			'use-resized'
		);
	}

	public function start(): void
	{
		$log = $this->onStart();

		$mode = $this->input->getOption('mode');

		if (!in_array($mode, ['use-resized', 'use-original', 'cleanup-backups'])) {
			echo "Chyba: Neplatný režim '$mode'. Použijte 'use-resized', 'use-original' nebo 'cleanup-backups'\n";
			$this->onFinish($log, "Neplatný režim");
			return;
		}

		echo "Režim: $mode\n";
		echo "Pracovní složka: " . self::UPLOAD_DIR . "\n\n";

		if (!is_dir(self::UPLOAD_DIR)) {
			echo "Složka neexistuje: " . self::UPLOAD_DIR . "\n";
			$this->onFinish($log, "Složka neexistuje");
			return;
		}

		$processedCount = 0;
		$errorCount = 0;

		if ($mode === 'use-resized') {
			$this->switchToResized($processedCount, $errorCount);
		} elseif ($mode === 'use-original') {
			$this->switchToOriginal($processedCount, $errorCount);
		} else {
			$this->cleanupBackups($processedCount, $errorCount);
		}

		$note = "Režim: $mode, Zpracováno: $processedCount souborů, chyby: $errorCount";
		echo "\n$note\n";

		$this->onFinish($log, $note);
	}

	private function switchToResized(int &$processedCount, int &$errorCount): void
	{
		echo "Přepínám na zmenšené obrázky...\n";

		// Najdeme všechny původní obrázky (bez _2 a _backup)
		$files = Finder::findFiles('*.jpg', '*.jpeg', '*.png')->in(self::UPLOAD_DIR);

		foreach ($files as $file) {
			try {
				$filePath = $file->getPathname();
				$fileName = $file->getBasename();
				$fileInfo = pathinfo($filePath);

				// Přeskočíme soubory s _2 nebo _backup
				if (strpos($fileInfo['filename'], '_2') !== false || strpos($fileInfo['filename'], '_backup') !== false) {
					continue;
				}

				// Zkontrolujeme, zda existuje zmenšená verze
				$resizedFileName = $fileInfo['filename'] . '_2.' . $fileInfo['extension'];
				$resizedFilePath = $fileInfo['dirname'] . '/' . $resizedFileName;

				if (!file_exists($resizedFilePath)) {
					echo "  Přeskakuji $fileName - neexistuje zmenšená verze\n";
					continue;
				}

				echo "  Zpracovávám: $fileName\n";

				// 1. Přejmenujeme původní na _backup
				$backupFileName = $fileInfo['filename'] . '_backup.' . $fileInfo['extension'];
				$backupFilePath = $fileInfo['dirname'] . '/' . $backupFileName;

				if (!rename($filePath, $backupFilePath)) {
					throw new \Exception("Nepodařilo se přejmenovat na backup");
				}
				echo "    $fileName → $backupFileName\n";

				// 2. Přejmenujeme _2 na původní název
				if (!rename($resizedFilePath, $filePath)) {
					// Pokud se nepodaří, vrátíme backup zpět
					rename($backupFilePath, $filePath);
					throw new \Exception("Nepodařilo se přejmenovat zmenšený obrázek");
				}
				echo "    $resizedFileName → $fileName\n";

				$processedCount++;
			} catch (\Exception $e) {
				echo "  Chyba při zpracování $fileName: " . $e->getMessage() . "\n";
				$errorCount++;
			}
		}
	}

	private function switchToOriginal(int &$processedCount, int &$errorCount): void
	{
		echo "Vracím původní obrázky...\n";

		// Najdeme všechny backup soubory
		$files = Finder::findFiles('*_backup.*')->in(self::UPLOAD_DIR);

		foreach ($files as $file) {
			try {
				$filePath = $file->getPathname();
				$fileName = $file->getBasename();
				$fileInfo = pathinfo($filePath);

				echo "  Zpracovávám: $fileName\n";

				// Získáme původní název (bez _backup)
				$originalBaseName = str_replace('_backup', '', $fileInfo['filename']);
				$originalFileName = $originalBaseName . '.' . $fileInfo['extension'];
				$originalFilePath = $fileInfo['dirname'] . '/' . $originalFileName;

				// Název pro _2 verzi
				$resizedFileName = $originalBaseName . '_2.' . $fileInfo['extension'];
				$resizedFilePath = $fileInfo['dirname'] . '/' . $resizedFileName;

				// 1. Přejmenujeme aktuální (zmenšený) na _2
				if (file_exists($originalFilePath)) {
					if (!rename($originalFilePath, $resizedFilePath)) {
						throw new \Exception("Nepodařilo se přejmenovat aktuální na _2");
					}
					echo "    $originalFileName → $resizedFileName\n";
				}

				// 2. Přejmenujeme backup na původní název
				if (!rename($filePath, $originalFilePath)) {
					// Pokud se nepodaří, vrátíme _2 zpět
					if (file_exists($resizedFilePath)) {
						rename($resizedFilePath, $originalFilePath);
					}
					throw new \Exception("Nepodařilo se obnovit z backup");
				}
				echo "    $fileName → $originalFileName\n";

				$processedCount++;
			} catch (\Exception $e) {
				echo "  Chyba při zpracování $fileName: " . $e->getMessage() . "\n";
				$errorCount++;
			}
		}
	}

	private function cleanupBackups(int &$processedCount, int &$errorCount): void
	{
		echo "Mažu backup soubory (_backup)...\n";
		echo "POZOR: Tato operace je nevratná!\n\n";

		// Najdeme všechny backup soubory
		$files = Finder::findFiles('*_backup.*')->in(self::UPLOAD_DIR);

		foreach ($files as $file) {
			try {
				$filePath = $file->getPathname();
				$fileName = $file->getBasename();

				echo "  Mažu: $fileName\n";

				if (!unlink($filePath)) {
					throw new \Exception("Nepodařilo se smazat soubor");
				}

				$processedCount++;
			} catch (\Exception $e) {
				echo "  Chyba při mazání $fileName: " . $e->getMessage() . "\n";
				$errorCount++;
			}
		}
	}
}

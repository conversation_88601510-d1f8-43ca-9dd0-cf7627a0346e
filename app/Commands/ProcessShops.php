<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\SyncManager;
use <PERSON>\Debugger;

class ProcessShops extends Job
{
	/** @var SyncManager */
	private $syncManager;

	public function __construct(SyncManager $syncManager)
	{
		parent::__construct();

		$this->syncManager = $syncManager;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-shops');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->processShops();

		$this->onFinish($log);
	}

	private function processShops(): void
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', '2000');

		$this->syncManager->syncShops();
	}
}

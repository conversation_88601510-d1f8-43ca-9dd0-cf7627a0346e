<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON>ino\Model\Leaflets\Entities\LeafletPage;
use <PERSON><PERSON><PERSON>\Model\Tags\TagFacade;
use Nette\Utils\Image;
use DateTime;
use <PERSON><PERSON><PERSON>\Model\Azure\AzureClient;
use <PERSON><PERSON><PERSON>\Model\Images\ImageFilter;
use <PERSON><PERSON>ino\Model\Leaflets\AnnotationCollection;
use Ka<PERSON>ino\Model\Leaflets\LeafletFacade;
use Ka<PERSON>ino\Model\Offers\Entities\Offer;
use Ka<PERSON>ino\Model\Offers\OfferFacade;
use Tracy\Debugger;

class ProcessLeafletPages extends Job
{
	/** @var LeafletFacade */
	private $leafletFacade;

	/** @var OfferFacade */
	private $offerFacade;

	/** @var AzureClient */
	private $azureClient;

	/**
	 * @var ImageFilter
	 */
	private $imageFilter;

	/** @var TagFacade */
	private $tagFacade;

	public function __construct(LeafletFacade $leafletFacade, OfferFacade $offerFacade, AzureClient $azureClient, ImageFilter $imageFilter, TagFacade $tagFacade)
	{
		parent::__construct();

		$this->leafletFacade = $leafletFacade;
		$this->offerFacade = $offerFacade;
		$this->azureClient = $azureClient;
		$this->imageFilter = $imageFilter;
		$this->tagFacade = $tagFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-leaflet-pages');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->processLeafletPages();

		$this->onFinish($log, null);
	}

	private function processLeafletPages(): void
	{
		echo "calculating score\n";
		$this->calculateLeafletPagesScore();

		echo "scanning OCR\n";
		$this->processOcr();

		echo "creating offers\n";
		$this->createOffers();
	}

	private function detectObjects(): void
	{
		$leafletPages = $this->leafletFacade->findLeafletPagesToUseDetectObjects(20);

		echo "total pages: " . count($leafletPages) . "\n";

		/** @var LeafletPage $leafletPage */
		foreach ($leafletPages as $leafletPage) {
			try {
				if ($leafletPage->getLeaflet()->isDeleted()) {
					throw new \Exception('Leaflet is deleted.');
				}

				if ($leafletPage->getLeaflet()->isNewsletter()) {
					continue;
				}

				$detectObjectsOutput = $this->azureClient->detectProducts(($this->imageFilter)($leafletPage->getImageUrl(), 1500, null, 'fit'));

				Debugger::dump($detectObjectsOutput);

				$leafletPage->setDetectObjectsOutput($detectObjectsOutput);
			} catch (\Exception $e) {
				$leafletPage->setDetectObjectsOutput($e->getMessage());
				$leafletPage->fail();
			}

			$this->leafletFacade->saveLeafletPage($leafletPage);
//			sleep(2);
		}
	}

	private function processOcr(int $limit = 50): void
	{
		$leafletPages = $this->leafletFacade->findLeafletPagesToUseOcr($limit);

		echo "total pages: " . count($leafletPages) . "\n";

		/** @var LeafletPage $leafletPage */
		foreach ($leafletPages as $leafletPage) {
			try {
				if ($leafletPage->getLeaflet()->isDeleted()) {
					echo "leaflet is deleted\n";
					throw new \Exception('Leaflet is deleted.');
				}

				if ($leafletPage->getLeaflet()->isNewsletter()) {
					echo "skipping newsletter\n";
					throw new \Exception('newsletter - skipped');
				}

				$startTime = microtime(true);
				$ocrOutput = $this->azureClient->processOCR(($this->imageFilter)($leafletPage->getImageUrl(), 1500, null, 'fit'), $leafletPage->getLeaflet()->getLocalization());
				$endTime = round(microtime(true) - $startTime, 3) * 1000;

				Debugger::log('OCR time: ' . $endTime . 'ms', 'azure-time');

				echo "\nOCR Output:";
				echo $ocrOutput;
				echo "\n";
				Debugger::dump($ocrOutput);

				$leafletPage->setOcrOutput($ocrOutput);
			} catch (\Exception $e) {
				$leafletPage->setOcrOutput($e->getMessage());
				$leafletPage->fail();
			}

			$this->leafletFacade->saveLeafletPage($leafletPage);
		}
	}

	private function createOffers(): void
	{
		$leafletPages = $this->leafletFacade->findLeafletPagesToCreateOffers(10);

		echo "total pages: " . count($leafletPages) . "\n";

		/** @var LeafletPage $leafletPage */
		foreach ($leafletPages as $leafletPage) {
			$annotations = $leafletPage->getAnnotationsOutput();
			$annotationCollection = AnnotationCollection::fromJson($annotations);
			$leaflet = $leafletPage->getLeaflet();

			$imageUrl = ($this->imageFilter)($leafletPage->getImageUrl(), 750, null, 'fit');
			$imageContents = file_get_contents($imageUrl);

			if ($imageContents === false) {
				continue;
			}

			$image = Image::fromString($imageContents);

			foreach ($annotationCollection->getAnnotations() as $annotation) {
				Debugger::dump('Image width');
				Debugger::dump($image->getWidth());
				Debugger::dump('Image height');
				Debugger::dump($image->getHeight());

				Debugger::dump('Image X');
				Debugger::dump($annotation->getX() * 100);
				Debugger::dump($annotation->getX() * $image->getWidth());

				Debugger::dump('Image Y');
				Debugger::dump($annotation->getY() * 100);
				Debugger::dump($annotation->getY() * $image->getHeight());

				Debugger::dump('Image Width');
				Debugger::dump($annotation->getWidth() * 100);
				Debugger::dump($annotation->getWidth() * $image->getWidth());

				Debugger::dump('Image Height');
				Debugger::dump($annotation->getHeight() * 100);
				Debugger::dump($annotation->getHeight() * $image->getHeight());

				Debugger::dump($annotation);

				$offerImage = clone $image;
				@$offerImage->crop(
					(int) round($annotation->getX() * $image->getWidth()),
					(int) round($annotation->getY() * $image->getHeight()),
					(int) round($annotation->getWidth() * $image->getWidth()),
					(int) round($annotation->getHeight() * $image->getHeight())
				);

				$offer = $this->offerFacade->createOffer(
					$leaflet->getLocalization(),
					"Waiting for name",
					uniqid(),
					$leaflet->getShop(),
					Offer::TYPE_LEAFLET,
					Offer::DISCOUNT_TYPE_ABSOLUTE,
					$leaflet->getValidSince(),
					$leaflet->getValidTill()
				);

				$leafletPage->process();
				$offer->setLeafletPage($leafletPage);
				$offer->setLeafletPageCoordinates($annotation->getX() . ',' . $annotation->getY() . ',' . $annotation->getWidth() . ',' . $annotation->getHeight());
				$offer->setOcrOutput($annotationCollection->getOcr($annotation, $leafletPage->getOcrOutput()));

				$this->offerFacade->saveOffer($offer);
				$this->offerFacade->saveOfferImage($offer, $offerImage, Image::JPEG);
			}

			echo "<img src='" . $leafletPage->getImageUrl() . "' width='500' />";
			echo "<br>";
			echo "<br>";
		}
	}

	private function calculateLeafletPagesScore(): void
	{
		$leafletPages = $this->leafletFacade->findLeafletPagesToCalculateScore(350);

		echo "total pages: " . count($leafletPages) . "\n";

		$startTime = time();

		/** @var LeafletPage $leafletPage */
		foreach ($leafletPages as $leafletPage) {
			$words = $this->leafletFacade->getAllOcrWords($leafletPage);

			if (empty($words)) {
				$leafletPage->setScore(0);
				$this->leafletFacade->saveLeafletPage($leafletPage);
				continue;
			}

			$score = 0;
			$usedTagsIds = [];

			/** @var string $word */
			foreach ($words as $word) {
				$tag = $this->tagFacade->findOffersTagByMatchRule($leafletPage->getLeaflet()->getLocalization(), $word);

				if ($tag !== null && in_array($tag->getId(), $usedTagsIds) === false && $tag->getScore() > 0) {
					$score += $tag->getScore();
					$usedTagsIds[] = $tag->getId();
				}
			}

			$leafletPage->setScore($score);
			$this->leafletFacade->saveLeafletPage($leafletPage);

			if (time() - $startTime > 110) {
				return;
			}
		}
	}
}

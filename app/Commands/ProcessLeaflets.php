<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Localization\Entities\Localization;
use <PERSON><PERSON>ino\Model\SyncManager;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;

class ProcessLeaflets extends Job
{
	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var SyncManager */
	private $syncManager;

	public function __construct(LocalizationFacade $localizationFacade, SyncManager $syncManager)
	{
		parent::__construct();

		$this->localizationFacade = $localizationFacade;
		$this->syncManager = $syncManager;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-leaflets');
	}

	public function start(?int $localizationId = null): void
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', '300');

		$log = $this->onStart();

		if ($localizationId) {
			$this->syncManager->syncLeaflets($this->localizationFacade->findLocalization($localizationId));
		} else {
			/** @var Localization $localization */
			foreach ($this->localizationFacade->findLocalizationsToProcessLeaflets() as $localization) {
				$localization->startLeafletSynchronization();
				$this->localizationFacade->saveLocalization($localization);

				$this->syncManager->syncLeaflets($localization);

				$localization->finishLeafletSynchronization();
				$this->localizationFacade->saveLocalization($localization);
			}
		}

		$this->onFinish($log);
	}
}

<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\SyncManager;

class ProcessCities extends Job
{
	/** @var LocalizationFacade */
	private $localizationFacade;

	/** @var SyncManager */
	private $syncManager;

	public function __construct(LocalizationFacade $localizationFacade, SyncManager $syncManager)
	{
		parent::__construct();

		$this->localizationFacade = $localizationFacade;
		$this->syncManager = $syncManager;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-cities');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$this->processCities();

		$this->onFinish($log);
	}

	private function processCities(): void
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', '300');

		$this->syncManager->syncCities();
	}
}

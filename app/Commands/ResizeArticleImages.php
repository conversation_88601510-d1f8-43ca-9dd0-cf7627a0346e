<?php

declare(strict_types=1);

namespace <PERSON><PERSON>ino\Commands;

use Nette\Utils\Image;
use Nette\Utils\Finder;
use <PERSON>\Debugger;

class ResizeArticleImages extends Job
{
	private const UPLOAD_DIR = __DIR__ . '/../../www/upload/articles/image';
	private const MAX_SIZE = 800;

	protected function configure(): void
	{
		$this->setName('kaufino:resize-article-images');
	}

	public function start(): void
	{
		$log = $this->onStart();

		$processedCount = 0;
		$errorCount = 0;

		echo "Začínám zpracování obrázků v: " . self::UPLOAD_DIR . "\n";

		if (!is_dir(self::UPLOAD_DIR)) {
			echo "Složka neexistuje: " . self::UPLOAD_DIR . "\n";
			$this->onFinish($log, "Složka neexistuje");
			return;
		}

		$files = Finder::findFiles('*.jpg', '*.jpeg', '*.png')->in(self::UPLOAD_DIR);

		foreach ($files as $file) {
			try {
				$filePath = $file->getPathname();
				$fileName = $file->getBasename();
				$fileInfo = pathinfo($filePath);

				if (strpos($fileInfo['filename'], '_2') !== false) {
					echo "Přeskakuji již zpracovaný soubor: $fileName\n";
					continue;
				}

				echo "Zpracovávám: $fileName\n";

				$image = Image::fromFile($filePath);

				$originalWidth = $image->getWidth();
				$originalHeight = $image->getHeight();

				echo "  Původní rozměry: {$originalWidth}x{$originalHeight}\n";

				if ($originalWidth <= self::MAX_SIZE && $originalHeight <= self::MAX_SIZE) {
					echo "  Obrázek je již dostatečně malý, přeskakuji\n";
					continue;
				}

				if ($originalWidth > $originalHeight) {
					$newWidth = self::MAX_SIZE;
					$newHeight = null;
				} else {
					$newWidth = null;
					$newHeight = self::MAX_SIZE;
				}

				$image->resize($newWidth, $newHeight);

				$newWidth = $image->getWidth();
				$newHeight = $image->getHeight();
				echo "  Nové rozměry: {$newWidth}x{$newHeight}\n";

				$newFileName = $fileInfo['filename'] . '_2.' . $fileInfo['extension'];
				$newFilePath = $fileInfo['dirname'] . '/' . $newFileName;

				$image->save($newFilePath, 90);

				echo "  Uloženo jako: $newFileName\n";
				$processedCount++;
			} catch (\Exception $e) {
				echo "Chyba při zpracování $fileName: " . $e->getMessage() . "\n";
				$errorCount++;
				Debugger::log($e);
			}
		}

		$note = "Zpracováno: $processedCount souborů, chyby: $errorCount";
		echo "\n$note\n";

		$this->onFinish($log, $note);
	}
}

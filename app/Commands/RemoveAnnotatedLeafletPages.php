<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Leaflets\Entities\LeafletPage;
use <PERSON><PERSON><PERSON>\Model\Leaflets\LeafletFacade;

class RemoveAnnotatedLeafletPages extends Job
{
	private const DIR = __DIR__ . '/../../www/upload/leaflet/annotated';

	private LeafletFacade $leafletFacade;

	public function __construct(LeafletFacade $leafletFacade)
	{
		parent::__construct();

		$this->leafletFacade = $leafletFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:remove-annotated-leaflet-pages:run');
	}

	public function start(): void
	{
		/** @var LeafletPage $leafletPage */
		foreach ($this->leafletFacade->findLeafletPageToRemoveAnnotatedImage() as $leafletPage) {
			$filePath = self::DIR . '/' . $leafletPage->getId() . '.jpg';

			if (file_exists($filePath)) {
				echo ".";
				unlink($filePath);
			}

			$leafletPage->removeAnnotatedImage();

			$this->leafletFacade->saveLeafletPage($leafletPage);
		}
	}
}

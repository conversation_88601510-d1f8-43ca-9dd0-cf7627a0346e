<?php

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON><PERSON>\Model\Geo\GeoFacade;
use <PERSON><PERSON><PERSON>\Model\Localization\LocalizationFacade;
use <PERSON><PERSON><PERSON>\Model\Shops\Entities\Store;

class ProcessStoresActivation extends Job
{
	private GeoFacade $geoFacade;

	private const LOCALIZATION_IDS = [1, 3, 5, 6, 9, 15, 17];

	private const OFERTO_LOCALIZATION_IDS = [1, 3, 6, 9];

	private LocalizationFacade $localizationFacade;

	public function __construct(GeoFacade $geoFacade, LocalizationFacade $localizationFacade)
	{
		parent::__construct();

		$this->geoFacade = $geoFacade;
		$this->localizationFacade = $localizationFacade;
	}

	protected function configure(): void
	{
		$this->setName('kaufino:process-stores-activation:run')
			->setDescription('Executes job.');
	}

	public function start(): void
	{
		$this->processStoresActivation();
	}

	public function processStoresActivation()
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', '2400');

		foreach (self::LOCALIZATION_IDS as $localizationId) {
			$localization = $this->localizationFacade->findLocalization($localizationId);

			/** @var Store $store */
			foreach ($this->geoFacade->findStoresToActivate($localization) as $store) {
				$store->activate();
				$this->geoFacade->saveStore($store);
			}
		}

		foreach (self::OFERTO_LOCALIZATION_IDS as $localizationId) {
			$localization = $this->localizationFacade->findLocalization($localizationId);

			$limit = 100;

			if ($localizationId === 3) {
				$limit = 120;
			}

			/** @var Store $store */
			foreach ($this->geoFacade->findStoresToActivateOferto($localization, $limit) as $store) {
				$store->activateOferto();
				$this->geoFacade->saveStore($store);
			}
		}
	}
}

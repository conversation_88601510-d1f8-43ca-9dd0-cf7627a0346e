<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\Commands;

use <PERSON><PERSON>ino\Model\Commands\Entities\Log;
use <PERSON><PERSON>ino\Model\Commands\LogFacade;
use <PERSON><PERSON>ino\Model\EntityManager;
use Nette\Utils\Random;
use ReflectionClass;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Tracy\Debugger;

abstract class Job extends Command
{
	/** @var float */
	private $startTime;

	/** @var string */
	protected $cronHash;

	/** @var InputInterface|array */
	protected $input = [];

	/** @var OutputInterface */
	protected $output;

	/** @var EntityManager @inject */
	public $em;

	/** @var LogFacade @inject */
	public $logFacade;

	abstract public function start(): void;

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->input = $input;
		$this->output = $output;

		$this->start();

		return 0;
	}

	protected function configure(): void
	{
		$this->addOption('sleep', null, InputOption::VALUE_OPTIONAL);
	}

	protected function onStart(): Log
	{
		$this->cronHash = Random::generate(5);
		Debugger::timer('cron-running-' . $this->cronHash);

		$this->startTime = microtime(true);

		return $this->logFacade->createLog((new ReflectionClass($this))->getShortName());
	}

	protected function onFinish(Log $log, $note = null): Log
	{
		echo "\n";
		return $this->logFacade->finishLog($log, $note);
	}

	protected function getElapsedTime()
	{
		$now = microtime(true);
		$delta = $now - $this->startTime;

		return $delta;
	}

	protected function sleepAndShutdown(int $sleepSeconds = 30)
	{
		if (PHP_SAPI !== 'cli') {
			return;
		}

		$this->em->close();
		$this->em->getConnection()->close();

		sleep($sleepSeconds);
	}

	protected function getInputArgument(string $argument)
	{
		return $this->input->getArgument($argument) ? : null;
	}

	protected function getInputOption(string $option)
	{
		return $this->input->getOption($option) ? : null;
	}

	protected function progressBar($done, $total, $info = '', $width = 50)
	{
		$perc = round(($done * 100) / $total);
		$bar = round(($width * $perc) / 100);

		return sprintf("%s%%[%s>%s]%s\r", $perc, str_repeat("=", $bar), str_repeat(" ", $width - $bar), $info);
	}
}
